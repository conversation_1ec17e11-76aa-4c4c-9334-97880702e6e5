/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
(() => {
var exports = {};
exports.id = "pages/admin/receipts";
exports.ids = ["pages/admin/receipts"];
exports.modules = {

/***/ "./styles/admin/AdminHeader.module.css":
/*!*********************************************!*\
  !*** ./styles/admin/AdminHeader.module.css ***!
  \*********************************************/
/***/ ((module) => {

eval("// Exports\nmodule.exports = {\n\t\"adminHeader\": \"AdminHeader_adminHeader__tAy8N\",\n\t\"headerLeft\": \"AdminHeader_headerLeft__FXjXr\",\n\t\"sidebarToggle\": \"AdminHeader_sidebarToggle__Vlukg\",\n\t\"hamburger\": \"AdminHeader_hamburger__3oPy_\",\n\t\"breadcrumb\": \"AdminHeader_breadcrumb__z_2w7\",\n\t\"breadcrumbLink\": \"AdminHeader_breadcrumbLink__iRTZW\",\n\t\"breadcrumbSeparator\": \"AdminHeader_breadcrumbSeparator__Q0xsW\",\n\t\"breadcrumbCurrent\": \"AdminHeader_breadcrumbCurrent__4QB_Y\",\n\t\"headerRight\": \"AdminHeader_headerRight__jgrCt\",\n\t\"quickActions\": \"AdminHeader_quickActions___NuOX\",\n\t\"quickAction\": \"AdminHeader_quickAction__XqmCI\",\n\t\"notifications\": \"AdminHeader_notifications__DWNcH\",\n\t\"notificationButton\": \"AdminHeader_notificationButton__hubpu\",\n\t\"notificationBadge\": \"AdminHeader_notificationBadge__spKqR\",\n\t\"notificationDropdown\": \"AdminHeader_notificationDropdown__mA8dq\",\n\t\"notificationHeader\": \"AdminHeader_notificationHeader__Ue15C\",\n\t\"markAllRead\": \"AdminHeader_markAllRead__UP_0Q\",\n\t\"notificationList\": \"AdminHeader_notificationList__JuL31\",\n\t\"notificationItem\": \"AdminHeader_notificationItem__ABEAH\",\n\t\"notificationIcon\": \"AdminHeader_notificationIcon__BSCLh\",\n\t\"notificationContent\": \"AdminHeader_notificationContent__tFkeh\",\n\t\"notificationTitle\": \"AdminHeader_notificationTitle__C5Il3\",\n\t\"notificationTime\": \"AdminHeader_notificationTime__DWutx\",\n\t\"notificationFooter\": \"AdminHeader_notificationFooter__T4khp\",\n\t\"userMenu\": \"AdminHeader_userMenu__YbO0w\",\n\t\"userButton\": \"AdminHeader_userButton__uP4qu\",\n\t\"userAvatar\": \"AdminHeader_userAvatar__QJdnj\",\n\t\"userInfo\": \"AdminHeader_userInfo__t2PHi\",\n\t\"userName\": \"AdminHeader_userName__4_RNy\",\n\t\"userRole\": \"AdminHeader_userRole__fQkGv\",\n\t\"dropdownArrow\": \"AdminHeader_dropdownArrow___vHwu\",\n\t\"userDropdown\": \"AdminHeader_userDropdown__NFy7A\",\n\t\"userDropdownHeader\": \"AdminHeader_userDropdownHeader__CYxvo\",\n\t\"userEmail\": \"AdminHeader_userEmail__nZCju\",\n\t\"userRoleBadge\": \"AdminHeader_userRoleBadge__W3Lbx\",\n\t\"userDropdownMenu\": \"AdminHeader_userDropdownMenu__7PJEX\",\n\t\"dropdownItem\": \"AdminHeader_dropdownItem__7zn2N\",\n\t\"dropdownIcon\": \"AdminHeader_dropdownIcon__ZZ3_U\",\n\t\"dropdownDivider\": \"AdminHeader_dropdownDivider__6AaxM\",\n\t\"logoutItem\": \"AdminHeader_logoutItem__R0CHw\"\n};\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///./styles/admin/AdminHeader.module.css\n");

/***/ }),

/***/ "./styles/admin/AdminLayout.module.css":
/*!*********************************************!*\
  !*** ./styles/admin/AdminLayout.module.css ***!
  \*********************************************/
/***/ ((module) => {

eval("// Exports\nmodule.exports = {\n\t\"adminLayout\": \"AdminLayout_adminLayout__5Oi4c\",\n\t\"mainContent\": \"AdminLayout_mainContent__INtLu\",\n\t\"sidebarCollapsed\": \"AdminLayout_sidebarCollapsed__oAEhD\",\n\t\"pageContent\": \"AdminLayout_pageContent__aWMEk\",\n\t\"adminFooter\": \"AdminLayout_adminFooter__mTvA1\",\n\t\"footerContent\": \"AdminLayout_footerContent__z6du0\",\n\t\"footerLeft\": \"AdminLayout_footerLeft__gGY8P\",\n\t\"version\": \"AdminLayout_version__vpU9q\",\n\t\"footerRight\": \"AdminLayout_footerRight__kyodA\",\n\t\"footerLink\": \"AdminLayout_footerLink__jvWuv\",\n\t\"mobileOverlay\": \"AdminLayout_mobileOverlay__BNO2v\",\n\t\"securityBanner\": \"AdminLayout_securityBanner__KTGT5\",\n\t\"securityIcon\": \"AdminLayout_securityIcon__eZwIM\",\n\t\"loadingContainer\": \"AdminLayout_loadingContainer__Wbedv\",\n\t\"loadingSpinner\": \"AdminLayout_loadingSpinner__C8mvO\",\n\t\"spin\": \"AdminLayout_spin__DZv4U\"\n};\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiLi9zdHlsZXMvYWRtaW4vQWRtaW5MYXlvdXQubW9kdWxlLmNzcyIsIm1hcHBpbmdzIjoiQUFBQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSIsInNvdXJjZXMiOlsid2VicGFjazovL29jZWFuc291bHNwYXJrbGVzLWFkbWluLy4vc3R5bGVzL2FkbWluL0FkbWluTGF5b3V0Lm1vZHVsZS5jc3M/YmJjZCJdLCJzb3VyY2VzQ29udGVudCI6WyIvLyBFeHBvcnRzXG5tb2R1bGUuZXhwb3J0cyA9IHtcblx0XCJhZG1pbkxheW91dFwiOiBcIkFkbWluTGF5b3V0X2FkbWluTGF5b3V0X181T2k0Y1wiLFxuXHRcIm1haW5Db250ZW50XCI6IFwiQWRtaW5MYXlvdXRfbWFpbkNvbnRlbnRfX0lOdEx1XCIsXG5cdFwic2lkZWJhckNvbGxhcHNlZFwiOiBcIkFkbWluTGF5b3V0X3NpZGViYXJDb2xsYXBzZWRfX29BRWhEXCIsXG5cdFwicGFnZUNvbnRlbnRcIjogXCJBZG1pbkxheW91dF9wYWdlQ29udGVudF9fYVdNRWtcIixcblx0XCJhZG1pbkZvb3RlclwiOiBcIkFkbWluTGF5b3V0X2FkbWluRm9vdGVyX19tVHZBMVwiLFxuXHRcImZvb3RlckNvbnRlbnRcIjogXCJBZG1pbkxheW91dF9mb290ZXJDb250ZW50X196NmR1MFwiLFxuXHRcImZvb3RlckxlZnRcIjogXCJBZG1pbkxheW91dF9mb290ZXJMZWZ0X19nR1k4UFwiLFxuXHRcInZlcnNpb25cIjogXCJBZG1pbkxheW91dF92ZXJzaW9uX192cFU5cVwiLFxuXHRcImZvb3RlclJpZ2h0XCI6IFwiQWRtaW5MYXlvdXRfZm9vdGVyUmlnaHRfX2t5b2RBXCIsXG5cdFwiZm9vdGVyTGlua1wiOiBcIkFkbWluTGF5b3V0X2Zvb3RlckxpbmtfX2p2V3V2XCIsXG5cdFwibW9iaWxlT3ZlcmxheVwiOiBcIkFkbWluTGF5b3V0X21vYmlsZU92ZXJsYXlfX0JOTzJ2XCIsXG5cdFwic2VjdXJpdHlCYW5uZXJcIjogXCJBZG1pbkxheW91dF9zZWN1cml0eUJhbm5lcl9fS1RHVDVcIixcblx0XCJzZWN1cml0eUljb25cIjogXCJBZG1pbkxheW91dF9zZWN1cml0eUljb25fX2Vad0lNXCIsXG5cdFwibG9hZGluZ0NvbnRhaW5lclwiOiBcIkFkbWluTGF5b3V0X2xvYWRpbmdDb250YWluZXJfX1diZWR2XCIsXG5cdFwibG9hZGluZ1NwaW5uZXJcIjogXCJBZG1pbkxheW91dF9sb2FkaW5nU3Bpbm5lcl9fQzhtdk9cIixcblx0XCJzcGluXCI6IFwiQWRtaW5MYXlvdXRfc3Bpbl9fRFp2NFVcIlxufTtcbiJdLCJuYW1lcyI6W10sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///./styles/admin/AdminLayout.module.css\n");

/***/ }),

/***/ "./styles/admin/AdminSidebar.module.css":
/*!**********************************************!*\
  !*** ./styles/admin/AdminSidebar.module.css ***!
  \**********************************************/
/***/ ((module) => {

eval("// Exports\nmodule.exports = {\n\t\"sidebar\": \"AdminSidebar_sidebar__qOEP2\",\n\t\"collapsed\": \"AdminSidebar_collapsed__mPopM\",\n\t\"mobile\": \"AdminSidebar_mobile__sXELg\",\n\t\"sidebarHeader\": \"AdminSidebar_sidebarHeader__h8NsD\",\n\t\"logo\": \"AdminSidebar_logo__MfKT2\",\n\t\"logoIcon\": \"AdminSidebar_logoIcon__ObH7O\",\n\t\"logoIconOnly\": \"AdminSidebar_logoIconOnly__AoqbB\",\n\t\"logoText\": \"AdminSidebar_logoText__6AwFU\",\n\t\"logoTitle\": \"AdminSidebar_logoTitle__rj3SO\",\n\t\"logoSubtitle\": \"AdminSidebar_logoSubtitle__ZlArc\",\n\t\"toggleButton\": \"AdminSidebar_toggleButton__93srV\",\n\t\"userInfo\": \"AdminSidebar_userInfo__0v9i_\",\n\t\"userAvatar\": \"AdminSidebar_userAvatar__Rg3G_\",\n\t\"userDetails\": \"AdminSidebar_userDetails__kA16n\",\n\t\"userName\": \"AdminSidebar_userName__2reke\",\n\t\"userRole\": \"AdminSidebar_userRole__Bo1eM\",\n\t\"navigation\": \"AdminSidebar_navigation__LpNEH\",\n\t\"menuList\": \"AdminSidebar_menuList__krOTx\",\n\t\"menuItem\": \"AdminSidebar_menuItem__A5Arm\",\n\t\"menuLink\": \"AdminSidebar_menuLink__ZSnZI\",\n\t\"active\": \"AdminSidebar_active__4G9nw\",\n\t\"menuIcon\": \"AdminSidebar_menuIcon__yJF_1\",\n\t\"menuLabel\": \"AdminSidebar_menuLabel__WEpLi\",\n\t\"expandButton\": \"AdminSidebar_expandButton__qS2q4\",\n\t\"submenu\": \"AdminSidebar_submenu__4dAAZ\",\n\t\"submenuItem\": \"AdminSidebar_submenuItem__WiecI\",\n\t\"submenuLink\": \"AdminSidebar_submenuLink__ZYwCJ\",\n\t\"submenuIcon\": \"AdminSidebar_submenuIcon__ThbKs\",\n\t\"submenuLabel\": \"AdminSidebar_submenuLabel__ocpuH\",\n\t\"sidebarFooter\": \"AdminSidebar_sidebarFooter__NML_U\",\n\t\"footerContent\": \"AdminSidebar_footerContent__qOiZI\",\n\t\"versionInfo\": \"AdminSidebar_versionInfo__bpisr\",\n\t\"version\": \"AdminSidebar_version__EyLxD\",\n\t\"environment\": \"AdminSidebar_environment__teF9S\",\n\t\"securityIndicator\": \"AdminSidebar_securityIndicator__S_6EA\",\n\t\"securityIcon\": \"AdminSidebar_securityIcon__GdGG2\",\n\t\"securityText\": \"AdminSidebar_securityText___evKe\"\n};\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///./styles/admin/AdminSidebar.module.css\n");

/***/ }),

/***/ "./styles/admin/ReceiptCustomizer.module.css":
/*!***************************************************!*\
  !*** ./styles/admin/ReceiptCustomizer.module.css ***!
  \***************************************************/
/***/ ((module) => {

eval("// Exports\nmodule.exports = {\n\t\"receiptCustomizer\": \"ReceiptCustomizer_receiptCustomizer___xcqY\",\n\t\"templatesSection\": \"ReceiptCustomizer_templatesSection__mXm3r\",\n\t\"sectionHeader\": \"ReceiptCustomizer_sectionHeader__my2P0\",\n\t\"createButton\": \"ReceiptCustomizer_createButton__4ec8B\",\n\t\"description\": \"ReceiptCustomizer_description__wPe27\",\n\t\"templateGrid\": \"ReceiptCustomizer_templateGrid__jr_H8\",\n\t\"templateCard\": \"ReceiptCustomizer_templateCard__kDgIc\",\n\t\"selected\": \"ReceiptCustomizer_selected__ACnk1\",\n\t\"templateHeader\": \"ReceiptCustomizer_templateHeader__rRww2\",\n\t\"templateIcon\": \"ReceiptCustomizer_templateIcon__Srxl_\",\n\t\"templateInfo\": \"ReceiptCustomizer_templateInfo__h23k2\",\n\t\"templateName\": \"ReceiptCustomizer_templateName__uk3z8\",\n\t\"defaultBadge\": \"ReceiptCustomizer_defaultBadge__SUgXq\",\n\t\"templateDescription\": \"ReceiptCustomizer_templateDescription__TSoFE\",\n\t\"templateFeatures\": \"ReceiptCustomizer_templateFeatures__iwBUY\",\n\t\"featureList\": \"ReceiptCustomizer_featureList__CYj_U\",\n\t\"feature\": \"ReceiptCustomizer_feature__d9Byw\",\n\t\"templateMeta\": \"ReceiptCustomizer_templateMeta__gqViy\",\n\t\"templateType\": \"ReceiptCustomizer_templateType__PNl3i\",\n\t\"businessName\": \"ReceiptCustomizer_businessName__E0OmH\",\n\t\"templateActions\": \"ReceiptCustomizer_templateActions__HQGqx\",\n\t\"actionButton\": \"ReceiptCustomizer_actionButton___jOWP\",\n\t\"deleteButton\": \"ReceiptCustomizer_deleteButton___EWs_\",\n\t\"previewSection\": \"ReceiptCustomizer_previewSection__dIl5u\",\n\t\"previewContainer\": \"ReceiptCustomizer_previewContainer__dzDqg\",\n\t\"receiptPreview\": \"ReceiptCustomizer_receiptPreview__HtJXa\",\n\t\"previewHeader\": \"ReceiptCustomizer_previewHeader__wWiko\",\n\t\"previewLabel\": \"ReceiptCustomizer_previewLabel__JFo7D\",\n\t\"refreshBtn\": \"ReceiptCustomizer_refreshBtn__FgVQ7\",\n\t\"previewContent\": \"ReceiptCustomizer_previewContent__UMFEW\",\n\t\"loading\": \"ReceiptCustomizer_loading__716Gm\",\n\t\"previewLoading\": \"ReceiptCustomizer_previewLoading__vjDL_\",\n\t\"loadingSpinner\": \"ReceiptCustomizer_loadingSpinner__yGDWU\",\n\t\"spin\": \"ReceiptCustomizer_spin__DWbsz\",\n\t\"error\": \"ReceiptCustomizer_error__vbyZV\",\n\t\"retryBtn\": \"ReceiptCustomizer_retryBtn__5NnSo\",\n\t\"noTemplates\": \"ReceiptCustomizer_noTemplates__WWmEb\",\n\t\"modalOverlay\": \"ReceiptCustomizer_modalOverlay__UkZ_k\",\n\t\"modal\": \"ReceiptCustomizer_modal__MMDt6\",\n\t\"modalHeader\": \"ReceiptCustomizer_modalHeader__09P89\",\n\t\"closeButton\": \"ReceiptCustomizer_closeButton__H_6ev\",\n\t\"modalForm\": \"ReceiptCustomizer_modalForm__SBIQZ\",\n\t\"formGrid\": \"ReceiptCustomizer_formGrid__QVIE5\",\n\t\"formSection\": \"ReceiptCustomizer_formSection__TQWnf\",\n\t\"formGroup\": \"ReceiptCustomizer_formGroup___04tR\",\n\t\"checkboxGroup\": \"ReceiptCustomizer_checkboxGroup__GWZbA\",\n\t\"checkboxLabel\": \"ReceiptCustomizer_checkboxLabel__sT_OL\",\n\t\"modalActions\": \"ReceiptCustomizer_modalActions__Rq3tJ\",\n\t\"cancelButton\": \"ReceiptCustomizer_cancelButton__H4wfS\",\n\t\"saveButton\": \"ReceiptCustomizer_saveButton__TI_tT\"\n};\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///./styles/admin/ReceiptCustomizer.module.css\n");

/***/ }),

/***/ "./styles/admin/Receipts.module.css":
/*!******************************************!*\
  !*** ./styles/admin/Receipts.module.css ***!
  \******************************************/
/***/ ((module) => {

eval("// Exports\nmodule.exports = {\n\t\"receiptsPage\": \"Receipts_receiptsPage__An9MG\",\n\t\"header\": \"Receipts_header__Tgjld\",\n\t\"headerContent\": \"Receipts_headerContent__BwCBu\",\n\t\"message\": \"Receipts_message__N57Jh\",\n\t\"success\": \"Receipts_success__V3pS6\",\n\t\"error\": \"Receipts_error__bkQIl\",\n\t\"tabNavigation\": \"Receipts_tabNavigation__7KXHx\",\n\t\"tabButton\": \"Receipts_tabButton__dIsYM\",\n\t\"active\": \"Receipts_active__KWnVy\",\n\t\"tabContent\": \"Receipts_tabContent__hIB4m\",\n\t\"tabHeader\": \"Receipts_tabHeader__O4oln\",\n\t\"templatesTab\": \"Receipts_templatesTab__oo3Xe\",\n\t\"settingsTab\": \"Receipts_settingsTab__PL01P\",\n\t\"receiptSettings\": \"Receipts_receiptSettings__OAeC7\",\n\t\"settingsGrid\": \"Receipts_settingsGrid__QU7aj\",\n\t\"settingsSection\": \"Receipts_settingsSection__VlV_E\",\n\t\"settingGroup\": \"Receipts_settingGroup__waunI\",\n\t\"checkboxLabel\": \"Receipts_checkboxLabel__FnREX\",\n\t\"settingsActions\": \"Receipts_settingsActions__tZqMs\",\n\t\"saveButton\": \"Receipts_saveButton__hE1zC\",\n\t\"loading\": \"Receipts_loading__QxdFG\",\n\t\"loadingSpinner\": \"Receipts_loadingSpinner__OEKwL\",\n\t\"spin\": \"Receipts_spin__qtEpY\"\n};\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///./styles/admin/Receipts.module.css\n");

/***/ }),

/***/ "./node_modules/next/dist/build/webpack/loaders/next-route-loader/index.js?kind=PAGES&page=%2Fadmin%2Freceipts&preferredRegion=&absolutePagePath=.%2Fpages%5Cadmin%5Creceipts.js&absoluteAppPath=private-next-pages%2F_app&absoluteDocumentPath=private-next-pages%2F_document&middlewareConfigBase64=e30%3D!":
/*!********************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-route-loader/index.js?kind=PAGES&page=%2Fadmin%2Freceipts&preferredRegion=&absolutePagePath=.%2Fpages%5Cadmin%5Creceipts.js&absoluteAppPath=private-next-pages%2F_app&absoluteDocumentPath=private-next-pages%2F_document&middlewareConfigBase64=e30%3D! ***!
  \********************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.a(module, async (__webpack_handle_async_dependencies__, __webpack_async_result__) => { try {\n__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   config: () => (/* binding */ config),\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__),\n/* harmony export */   getServerSideProps: () => (/* binding */ getServerSideProps),\n/* harmony export */   getStaticPaths: () => (/* binding */ getStaticPaths),\n/* harmony export */   getStaticProps: () => (/* binding */ getStaticProps),\n/* harmony export */   reportWebVitals: () => (/* binding */ reportWebVitals),\n/* harmony export */   routeModule: () => (/* binding */ routeModule),\n/* harmony export */   unstable_getServerProps: () => (/* binding */ unstable_getServerProps),\n/* harmony export */   unstable_getServerSideProps: () => (/* binding */ unstable_getServerSideProps),\n/* harmony export */   unstable_getStaticParams: () => (/* binding */ unstable_getStaticParams),\n/* harmony export */   unstable_getStaticPaths: () => (/* binding */ unstable_getStaticPaths),\n/* harmony export */   unstable_getStaticProps: () => (/* binding */ unstable_getStaticProps)\n/* harmony export */ });\n/* harmony import */ var next_dist_server_future_route_modules_pages_module_compiled__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/server/future/route-modules/pages/module.compiled */ \"./node_modules/next/dist/server/future/route-modules/pages/module.compiled.js\");\n/* harmony import */ var next_dist_server_future_route_modules_pages_module_compiled__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_future_route_modules_pages_module_compiled__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_dist_server_future_route_kind__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next/dist/server/future/route-kind */ \"./node_modules/next/dist/server/future/route-kind.js\");\n/* harmony import */ var next_dist_build_templates_helpers__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/dist/build/templates/helpers */ \"./node_modules/next/dist/build/templates/helpers.js\");\n/* harmony import */ var private_next_pages_document__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! private-next-pages/_document */ \"./node_modules/next/dist/pages/_document.js\");\n/* harmony import */ var private_next_pages_document__WEBPACK_IMPORTED_MODULE_3___default = /*#__PURE__*/__webpack_require__.n(private_next_pages_document__WEBPACK_IMPORTED_MODULE_3__);\n/* harmony import */ var private_next_pages_app__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! private-next-pages/_app */ \"./pages/_app.tsx\");\n/* harmony import */ var _pages_admin_receipts_js__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! ./pages\\admin\\receipts.js */ \"./pages/admin/receipts.js\");\nvar __webpack_async_dependencies__ = __webpack_handle_async_dependencies__([private_next_pages_app__WEBPACK_IMPORTED_MODULE_4__, _pages_admin_receipts_js__WEBPACK_IMPORTED_MODULE_5__]);\n([private_next_pages_app__WEBPACK_IMPORTED_MODULE_4__, _pages_admin_receipts_js__WEBPACK_IMPORTED_MODULE_5__] = __webpack_async_dependencies__.then ? (await __webpack_async_dependencies__)() : __webpack_async_dependencies__);\n\n\n\n// Import the app and document modules.\n\n\n// Import the userland code.\n\n// Re-export the component (should be the default export).\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = ((0,next_dist_build_templates_helpers__WEBPACK_IMPORTED_MODULE_2__.hoist)(_pages_admin_receipts_js__WEBPACK_IMPORTED_MODULE_5__, \"default\"));\n// Re-export methods.\nconst getStaticProps = (0,next_dist_build_templates_helpers__WEBPACK_IMPORTED_MODULE_2__.hoist)(_pages_admin_receipts_js__WEBPACK_IMPORTED_MODULE_5__, \"getStaticProps\");\nconst getStaticPaths = (0,next_dist_build_templates_helpers__WEBPACK_IMPORTED_MODULE_2__.hoist)(_pages_admin_receipts_js__WEBPACK_IMPORTED_MODULE_5__, \"getStaticPaths\");\nconst getServerSideProps = (0,next_dist_build_templates_helpers__WEBPACK_IMPORTED_MODULE_2__.hoist)(_pages_admin_receipts_js__WEBPACK_IMPORTED_MODULE_5__, \"getServerSideProps\");\nconst config = (0,next_dist_build_templates_helpers__WEBPACK_IMPORTED_MODULE_2__.hoist)(_pages_admin_receipts_js__WEBPACK_IMPORTED_MODULE_5__, \"config\");\nconst reportWebVitals = (0,next_dist_build_templates_helpers__WEBPACK_IMPORTED_MODULE_2__.hoist)(_pages_admin_receipts_js__WEBPACK_IMPORTED_MODULE_5__, \"reportWebVitals\");\n// Re-export legacy methods.\nconst unstable_getStaticProps = (0,next_dist_build_templates_helpers__WEBPACK_IMPORTED_MODULE_2__.hoist)(_pages_admin_receipts_js__WEBPACK_IMPORTED_MODULE_5__, \"unstable_getStaticProps\");\nconst unstable_getStaticPaths = (0,next_dist_build_templates_helpers__WEBPACK_IMPORTED_MODULE_2__.hoist)(_pages_admin_receipts_js__WEBPACK_IMPORTED_MODULE_5__, \"unstable_getStaticPaths\");\nconst unstable_getStaticParams = (0,next_dist_build_templates_helpers__WEBPACK_IMPORTED_MODULE_2__.hoist)(_pages_admin_receipts_js__WEBPACK_IMPORTED_MODULE_5__, \"unstable_getStaticParams\");\nconst unstable_getServerProps = (0,next_dist_build_templates_helpers__WEBPACK_IMPORTED_MODULE_2__.hoist)(_pages_admin_receipts_js__WEBPACK_IMPORTED_MODULE_5__, \"unstable_getServerProps\");\nconst unstable_getServerSideProps = (0,next_dist_build_templates_helpers__WEBPACK_IMPORTED_MODULE_2__.hoist)(_pages_admin_receipts_js__WEBPACK_IMPORTED_MODULE_5__, \"unstable_getServerSideProps\");\n// Create and export the route module that will be consumed.\nconst routeModule = new next_dist_server_future_route_modules_pages_module_compiled__WEBPACK_IMPORTED_MODULE_0__.PagesRouteModule({\n    definition: {\n        kind: next_dist_server_future_route_kind__WEBPACK_IMPORTED_MODULE_1__.RouteKind.PAGES,\n        page: \"/admin/receipts\",\n        pathname: \"/admin/receipts\",\n        // The following aren't used in production.\n        bundlePath: \"\",\n        filename: \"\"\n    },\n    components: {\n        App: private_next_pages_app__WEBPACK_IMPORTED_MODULE_4__[\"default\"],\n        Document: (private_next_pages_document__WEBPACK_IMPORTED_MODULE_3___default())\n    },\n    userland: _pages_admin_receipts_js__WEBPACK_IMPORTED_MODULE_5__\n});\n\n//# sourceMappingURL=pages.js.map\n__webpack_async_result__();\n} catch(e) { __webpack_async_result__(e); } });//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///./node_modules/next/dist/build/webpack/loaders/next-route-loader/index.js?kind=PAGES&page=%2Fadmin%2Freceipts&preferredRegion=&absolutePagePath=.%2Fpages%5Cadmin%5Creceipts.js&absoluteAppPath=private-next-pages%2F_app&absoluteDocumentPath=private-next-pages%2F_document&middlewareConfigBase64=e30%3D!\n");

/***/ }),

/***/ "./components/admin/AdminHeader.tsx":
/*!******************************************!*\
  !*** ./components/admin/AdminHeader.tsx ***!
  \******************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ AdminHeader)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"react/jsx-dev-runtime\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"react\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var next_link__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/link */ \"./node_modules/next/link.js\");\n/* harmony import */ var next_link__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(next_link__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var _styles_admin_AdminHeader_module_css__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ../../styles/admin/AdminHeader.module.css */ \"./styles/admin/AdminHeader.module.css\");\n/* harmony import */ var _styles_admin_AdminHeader_module_css__WEBPACK_IMPORTED_MODULE_3___default = /*#__PURE__*/__webpack_require__.n(_styles_admin_AdminHeader_module_css__WEBPACK_IMPORTED_MODULE_3__);\n\n\n\n\nfunction AdminHeader({ user, onLogout, onToggleSidebar, sidebarCollapsed }) {\n    const [showUserMenu, setShowUserMenu] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [showNotifications, setShowNotifications] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const userMenuRef = (0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)(null);\n    const notificationsRef = (0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)(null);\n    // Close dropdowns when clicking outside\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        const handleClickOutside = (event)=>{\n            if (userMenuRef.current && !userMenuRef.current.contains(event.target)) {\n                setShowUserMenu(false);\n            }\n            if (notificationsRef.current && !notificationsRef.current.contains(event.target)) {\n                setShowNotifications(false);\n            }\n        };\n        document.addEventListener(\"mousedown\", handleClickOutside);\n        return ()=>document.removeEventListener(\"mousedown\", handleClickOutside);\n    }, []);\n    const getRoleColor = (role)=>{\n        switch(role){\n            case \"DEV\":\n                return \"#dc3545\";\n            case \"Admin\":\n                return \"#3788d8\";\n            case \"Artist\":\n                return \"#28a745\";\n            case \"Braider\":\n                return \"#fd7e14\";\n            default:\n                return \"#6c757d\";\n        }\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"header\", {\n        className: (_styles_admin_AdminHeader_module_css__WEBPACK_IMPORTED_MODULE_3___default().adminHeader),\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: (_styles_admin_AdminHeader_module_css__WEBPACK_IMPORTED_MODULE_3___default().headerLeft),\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                        className: (_styles_admin_AdminHeader_module_css__WEBPACK_IMPORTED_MODULE_3___default().sidebarToggle),\n                        onClick: onToggleSidebar,\n                        title: sidebarCollapsed ? \"Expand sidebar\" : \"Collapse sidebar\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                            className: (_styles_admin_AdminHeader_module_css__WEBPACK_IMPORTED_MODULE_3___default().hamburger),\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {}, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\connectplusapps website\\\\admin.oceansoulsparkles.com.au\\\\oceansoulsparkles-admin\\\\components\\\\admin\\\\AdminHeader.tsx\",\n                                    lineNumber: 58,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {}, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\connectplusapps website\\\\admin.oceansoulsparkles.com.au\\\\oceansoulsparkles-admin\\\\components\\\\admin\\\\AdminHeader.tsx\",\n                                    lineNumber: 59,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {}, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\connectplusapps website\\\\admin.oceansoulsparkles.com.au\\\\oceansoulsparkles-admin\\\\components\\\\admin\\\\AdminHeader.tsx\",\n                                    lineNumber: 60,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\connectplusapps website\\\\admin.oceansoulsparkles.com.au\\\\oceansoulsparkles-admin\\\\components\\\\admin\\\\AdminHeader.tsx\",\n                            lineNumber: 57,\n                            columnNumber: 11\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\connectplusapps website\\\\admin.oceansoulsparkles.com.au\\\\oceansoulsparkles-admin\\\\components\\\\admin\\\\AdminHeader.tsx\",\n                        lineNumber: 52,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: (_styles_admin_AdminHeader_module_css__WEBPACK_IMPORTED_MODULE_3___default().breadcrumb),\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_2___default()), {\n                                href: \"/admin/dashboard\",\n                                className: (_styles_admin_AdminHeader_module_css__WEBPACK_IMPORTED_MODULE_3___default().breadcrumbLink),\n                                children: \"Dashboard\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\connectplusapps website\\\\admin.oceansoulsparkles.com.au\\\\oceansoulsparkles-admin\\\\components\\\\admin\\\\AdminHeader.tsx\",\n                                lineNumber: 65,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                className: (_styles_admin_AdminHeader_module_css__WEBPACK_IMPORTED_MODULE_3___default().breadcrumbSeparator),\n                                children: \"/\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\connectplusapps website\\\\admin.oceansoulsparkles.com.au\\\\oceansoulsparkles-admin\\\\components\\\\admin\\\\AdminHeader.tsx\",\n                                lineNumber: 68,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                className: (_styles_admin_AdminHeader_module_css__WEBPACK_IMPORTED_MODULE_3___default().breadcrumbCurrent),\n                                children: \"Overview\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\connectplusapps website\\\\admin.oceansoulsparkles.com.au\\\\oceansoulsparkles-admin\\\\components\\\\admin\\\\AdminHeader.tsx\",\n                                lineNumber: 69,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\connectplusapps website\\\\admin.oceansoulsparkles.com.au\\\\oceansoulsparkles-admin\\\\components\\\\admin\\\\AdminHeader.tsx\",\n                        lineNumber: 64,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\connectplusapps website\\\\admin.oceansoulsparkles.com.au\\\\oceansoulsparkles-admin\\\\components\\\\admin\\\\AdminHeader.tsx\",\n                lineNumber: 51,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: (_styles_admin_AdminHeader_module_css__WEBPACK_IMPORTED_MODULE_3___default().headerRight),\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: (_styles_admin_AdminHeader_module_css__WEBPACK_IMPORTED_MODULE_3___default().quickActions),\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_2___default()), {\n                                href: \"/admin/bookings/new\",\n                                className: (_styles_admin_AdminHeader_module_css__WEBPACK_IMPORTED_MODULE_3___default().quickAction),\n                                title: \"New Booking\",\n                                children: \"\\uD83D\\uDCC5\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\connectplusapps website\\\\admin.oceansoulsparkles.com.au\\\\oceansoulsparkles-admin\\\\components\\\\admin\\\\AdminHeader.tsx\",\n                                lineNumber: 76,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_2___default()), {\n                                href: \"/admin/customers/new\",\n                                className: (_styles_admin_AdminHeader_module_css__WEBPACK_IMPORTED_MODULE_3___default().quickAction),\n                                title: \"New Customer\",\n                                children: \"\\uD83D\\uDC64\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\connectplusapps website\\\\admin.oceansoulsparkles.com.au\\\\oceansoulsparkles-admin\\\\components\\\\admin\\\\AdminHeader.tsx\",\n                                lineNumber: 79,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                className: (_styles_admin_AdminHeader_module_css__WEBPACK_IMPORTED_MODULE_3___default().quickAction),\n                                title: \"Refresh\",\n                                children: \"\\uD83D\\uDD04\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\connectplusapps website\\\\admin.oceansoulsparkles.com.au\\\\oceansoulsparkles-admin\\\\components\\\\admin\\\\AdminHeader.tsx\",\n                                lineNumber: 82,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\connectplusapps website\\\\admin.oceansoulsparkles.com.au\\\\oceansoulsparkles-admin\\\\components\\\\admin\\\\AdminHeader.tsx\",\n                        lineNumber: 75,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: (_styles_admin_AdminHeader_module_css__WEBPACK_IMPORTED_MODULE_3___default().notifications),\n                        ref: notificationsRef,\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                className: (_styles_admin_AdminHeader_module_css__WEBPACK_IMPORTED_MODULE_3___default().notificationButton),\n                                onClick: ()=>setShowNotifications(!showNotifications),\n                                title: \"Notifications\",\n                                children: [\n                                    \"\\uD83D\\uDD14\",\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                        className: (_styles_admin_AdminHeader_module_css__WEBPACK_IMPORTED_MODULE_3___default().notificationBadge),\n                                        children: \"3\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\connectplusapps website\\\\admin.oceansoulsparkles.com.au\\\\oceansoulsparkles-admin\\\\components\\\\admin\\\\AdminHeader.tsx\",\n                                        lineNumber: 95,\n                                        columnNumber: 13\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\connectplusapps website\\\\admin.oceansoulsparkles.com.au\\\\oceansoulsparkles-admin\\\\components\\\\admin\\\\AdminHeader.tsx\",\n                                lineNumber: 89,\n                                columnNumber: 11\n                            }, this),\n                            showNotifications && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: (_styles_admin_AdminHeader_module_css__WEBPACK_IMPORTED_MODULE_3___default().notificationDropdown),\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: (_styles_admin_AdminHeader_module_css__WEBPACK_IMPORTED_MODULE_3___default().notificationHeader),\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                children: \"Notifications\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\connectplusapps website\\\\admin.oceansoulsparkles.com.au\\\\oceansoulsparkles-admin\\\\components\\\\admin\\\\AdminHeader.tsx\",\n                                                lineNumber: 101,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                className: (_styles_admin_AdminHeader_module_css__WEBPACK_IMPORTED_MODULE_3___default().markAllRead),\n                                                children: \"Mark all read\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\connectplusapps website\\\\admin.oceansoulsparkles.com.au\\\\oceansoulsparkles-admin\\\\components\\\\admin\\\\AdminHeader.tsx\",\n                                                lineNumber: 102,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\connectplusapps website\\\\admin.oceansoulsparkles.com.au\\\\oceansoulsparkles-admin\\\\components\\\\admin\\\\AdminHeader.tsx\",\n                                        lineNumber: 100,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: (_styles_admin_AdminHeader_module_css__WEBPACK_IMPORTED_MODULE_3___default().notificationList),\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: (_styles_admin_AdminHeader_module_css__WEBPACK_IMPORTED_MODULE_3___default().notificationItem),\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: (_styles_admin_AdminHeader_module_css__WEBPACK_IMPORTED_MODULE_3___default().notificationIcon),\n                                                        children: \"\\uD83D\\uDCC5\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\connectplusapps website\\\\admin.oceansoulsparkles.com.au\\\\oceansoulsparkles-admin\\\\components\\\\admin\\\\AdminHeader.tsx\",\n                                                        lineNumber: 106,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: (_styles_admin_AdminHeader_module_css__WEBPACK_IMPORTED_MODULE_3___default().notificationContent),\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: (_styles_admin_AdminHeader_module_css__WEBPACK_IMPORTED_MODULE_3___default().notificationTitle),\n                                                                children: \"New booking request\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\connectplusapps website\\\\admin.oceansoulsparkles.com.au\\\\oceansoulsparkles-admin\\\\components\\\\admin\\\\AdminHeader.tsx\",\n                                                                lineNumber: 108,\n                                                                columnNumber: 21\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: (_styles_admin_AdminHeader_module_css__WEBPACK_IMPORTED_MODULE_3___default().notificationTime),\n                                                                children: \"5 minutes ago\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\connectplusapps website\\\\admin.oceansoulsparkles.com.au\\\\oceansoulsparkles-admin\\\\components\\\\admin\\\\AdminHeader.tsx\",\n                                                                lineNumber: 109,\n                                                                columnNumber: 21\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\connectplusapps website\\\\admin.oceansoulsparkles.com.au\\\\oceansoulsparkles-admin\\\\components\\\\admin\\\\AdminHeader.tsx\",\n                                                        lineNumber: 107,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\connectplusapps website\\\\admin.oceansoulsparkles.com.au\\\\oceansoulsparkles-admin\\\\components\\\\admin\\\\AdminHeader.tsx\",\n                                                lineNumber: 105,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: (_styles_admin_AdminHeader_module_css__WEBPACK_IMPORTED_MODULE_3___default().notificationItem),\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: (_styles_admin_AdminHeader_module_css__WEBPACK_IMPORTED_MODULE_3___default().notificationIcon),\n                                                        children: \"\\uD83D\\uDCB0\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\connectplusapps website\\\\admin.oceansoulsparkles.com.au\\\\oceansoulsparkles-admin\\\\components\\\\admin\\\\AdminHeader.tsx\",\n                                                        lineNumber: 113,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: (_styles_admin_AdminHeader_module_css__WEBPACK_IMPORTED_MODULE_3___default().notificationContent),\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: (_styles_admin_AdminHeader_module_css__WEBPACK_IMPORTED_MODULE_3___default().notificationTitle),\n                                                                children: \"Payment received\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\connectplusapps website\\\\admin.oceansoulsparkles.com.au\\\\oceansoulsparkles-admin\\\\components\\\\admin\\\\AdminHeader.tsx\",\n                                                                lineNumber: 115,\n                                                                columnNumber: 21\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: (_styles_admin_AdminHeader_module_css__WEBPACK_IMPORTED_MODULE_3___default().notificationTime),\n                                                                children: \"1 hour ago\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\connectplusapps website\\\\admin.oceansoulsparkles.com.au\\\\oceansoulsparkles-admin\\\\components\\\\admin\\\\AdminHeader.tsx\",\n                                                                lineNumber: 116,\n                                                                columnNumber: 21\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\connectplusapps website\\\\admin.oceansoulsparkles.com.au\\\\oceansoulsparkles-admin\\\\components\\\\admin\\\\AdminHeader.tsx\",\n                                                        lineNumber: 114,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\connectplusapps website\\\\admin.oceansoulsparkles.com.au\\\\oceansoulsparkles-admin\\\\components\\\\admin\\\\AdminHeader.tsx\",\n                                                lineNumber: 112,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: (_styles_admin_AdminHeader_module_css__WEBPACK_IMPORTED_MODULE_3___default().notificationItem),\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: (_styles_admin_AdminHeader_module_css__WEBPACK_IMPORTED_MODULE_3___default().notificationIcon),\n                                                        children: \"⚠️\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\connectplusapps website\\\\admin.oceansoulsparkles.com.au\\\\oceansoulsparkles-admin\\\\components\\\\admin\\\\AdminHeader.tsx\",\n                                                        lineNumber: 120,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: (_styles_admin_AdminHeader_module_css__WEBPACK_IMPORTED_MODULE_3___default().notificationContent),\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: (_styles_admin_AdminHeader_module_css__WEBPACK_IMPORTED_MODULE_3___default().notificationTitle),\n                                                                children: \"Low inventory alert\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\connectplusapps website\\\\admin.oceansoulsparkles.com.au\\\\oceansoulsparkles-admin\\\\components\\\\admin\\\\AdminHeader.tsx\",\n                                                                lineNumber: 122,\n                                                                columnNumber: 21\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: (_styles_admin_AdminHeader_module_css__WEBPACK_IMPORTED_MODULE_3___default().notificationTime),\n                                                                children: \"2 hours ago\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\connectplusapps website\\\\admin.oceansoulsparkles.com.au\\\\oceansoulsparkles-admin\\\\components\\\\admin\\\\AdminHeader.tsx\",\n                                                                lineNumber: 123,\n                                                                columnNumber: 21\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\connectplusapps website\\\\admin.oceansoulsparkles.com.au\\\\oceansoulsparkles-admin\\\\components\\\\admin\\\\AdminHeader.tsx\",\n                                                        lineNumber: 121,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\connectplusapps website\\\\admin.oceansoulsparkles.com.au\\\\oceansoulsparkles-admin\\\\components\\\\admin\\\\AdminHeader.tsx\",\n                                                lineNumber: 119,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\connectplusapps website\\\\admin.oceansoulsparkles.com.au\\\\oceansoulsparkles-admin\\\\components\\\\admin\\\\AdminHeader.tsx\",\n                                        lineNumber: 104,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: (_styles_admin_AdminHeader_module_css__WEBPACK_IMPORTED_MODULE_3___default().notificationFooter),\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_2___default()), {\n                                            href: \"/admin/notifications\",\n                                            children: \"View all notifications\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\connectplusapps website\\\\admin.oceansoulsparkles.com.au\\\\oceansoulsparkles-admin\\\\components\\\\admin\\\\AdminHeader.tsx\",\n                                            lineNumber: 128,\n                                            columnNumber: 17\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\connectplusapps website\\\\admin.oceansoulsparkles.com.au\\\\oceansoulsparkles-admin\\\\components\\\\admin\\\\AdminHeader.tsx\",\n                                        lineNumber: 127,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\connectplusapps website\\\\admin.oceansoulsparkles.com.au\\\\oceansoulsparkles-admin\\\\components\\\\admin\\\\AdminHeader.tsx\",\n                                lineNumber: 99,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\connectplusapps website\\\\admin.oceansoulsparkles.com.au\\\\oceansoulsparkles-admin\\\\components\\\\admin\\\\AdminHeader.tsx\",\n                        lineNumber: 88,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: (_styles_admin_AdminHeader_module_css__WEBPACK_IMPORTED_MODULE_3___default().userMenu),\n                        ref: userMenuRef,\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                className: (_styles_admin_AdminHeader_module_css__WEBPACK_IMPORTED_MODULE_3___default().userButton),\n                                onClick: ()=>setShowUserMenu(!showUserMenu),\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: (_styles_admin_AdminHeader_module_css__WEBPACK_IMPORTED_MODULE_3___default().userAvatar),\n                                        children: [\n                                            user.firstName.charAt(0),\n                                            user.lastName.charAt(0)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\connectplusapps website\\\\admin.oceansoulsparkles.com.au\\\\oceansoulsparkles-admin\\\\components\\\\admin\\\\AdminHeader.tsx\",\n                                        lineNumber: 140,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: (_styles_admin_AdminHeader_module_css__WEBPACK_IMPORTED_MODULE_3___default().userInfo),\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: (_styles_admin_AdminHeader_module_css__WEBPACK_IMPORTED_MODULE_3___default().userName),\n                                                children: [\n                                                    user.firstName,\n                                                    \" \",\n                                                    user.lastName\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\connectplusapps website\\\\admin.oceansoulsparkles.com.au\\\\oceansoulsparkles-admin\\\\components\\\\admin\\\\AdminHeader.tsx\",\n                                                lineNumber: 144,\n                                                columnNumber: 15\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: (_styles_admin_AdminHeader_module_css__WEBPACK_IMPORTED_MODULE_3___default().userRole),\n                                                style: {\n                                                    color: getRoleColor(user.role)\n                                                },\n                                                children: user.role\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\connectplusapps website\\\\admin.oceansoulsparkles.com.au\\\\oceansoulsparkles-admin\\\\components\\\\admin\\\\AdminHeader.tsx\",\n                                                lineNumber: 147,\n                                                columnNumber: 15\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\connectplusapps website\\\\admin.oceansoulsparkles.com.au\\\\oceansoulsparkles-admin\\\\components\\\\admin\\\\AdminHeader.tsx\",\n                                        lineNumber: 143,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: (_styles_admin_AdminHeader_module_css__WEBPACK_IMPORTED_MODULE_3___default().dropdownArrow),\n                                        children: showUserMenu ? \"▲\" : \"▼\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\connectplusapps website\\\\admin.oceansoulsparkles.com.au\\\\oceansoulsparkles-admin\\\\components\\\\admin\\\\AdminHeader.tsx\",\n                                        lineNumber: 154,\n                                        columnNumber: 13\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\connectplusapps website\\\\admin.oceansoulsparkles.com.au\\\\oceansoulsparkles-admin\\\\components\\\\admin\\\\AdminHeader.tsx\",\n                                lineNumber: 136,\n                                columnNumber: 11\n                            }, this),\n                            showUserMenu && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: (_styles_admin_AdminHeader_module_css__WEBPACK_IMPORTED_MODULE_3___default().userDropdown),\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: (_styles_admin_AdminHeader_module_css__WEBPACK_IMPORTED_MODULE_3___default().userDropdownHeader),\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: (_styles_admin_AdminHeader_module_css__WEBPACK_IMPORTED_MODULE_3___default().userEmail),\n                                                children: user.email\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\connectplusapps website\\\\admin.oceansoulsparkles.com.au\\\\oceansoulsparkles-admin\\\\components\\\\admin\\\\AdminHeader.tsx\",\n                                                lineNumber: 162,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: (_styles_admin_AdminHeader_module_css__WEBPACK_IMPORTED_MODULE_3___default().userRoleBadge),\n                                                style: {\n                                                    backgroundColor: getRoleColor(user.role)\n                                                },\n                                                children: user.role\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\connectplusapps website\\\\admin.oceansoulsparkles.com.au\\\\oceansoulsparkles-admin\\\\components\\\\admin\\\\AdminHeader.tsx\",\n                                                lineNumber: 163,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\connectplusapps website\\\\admin.oceansoulsparkles.com.au\\\\oceansoulsparkles-admin\\\\components\\\\admin\\\\AdminHeader.tsx\",\n                                        lineNumber: 161,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: (_styles_admin_AdminHeader_module_css__WEBPACK_IMPORTED_MODULE_3___default().userDropdownMenu),\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_2___default()), {\n                                                href: \"/admin/profile\",\n                                                className: (_styles_admin_AdminHeader_module_css__WEBPACK_IMPORTED_MODULE_3___default().dropdownItem),\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        className: (_styles_admin_AdminHeader_module_css__WEBPACK_IMPORTED_MODULE_3___default().dropdownIcon),\n                                                        children: \"\\uD83D\\uDC64\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\connectplusapps website\\\\admin.oceansoulsparkles.com.au\\\\oceansoulsparkles-admin\\\\components\\\\admin\\\\AdminHeader.tsx\",\n                                                        lineNumber: 173,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    \"Profile Settings\"\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\connectplusapps website\\\\admin.oceansoulsparkles.com.au\\\\oceansoulsparkles-admin\\\\components\\\\admin\\\\AdminHeader.tsx\",\n                                                lineNumber: 172,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_2___default()), {\n                                                href: \"/admin/security\",\n                                                className: (_styles_admin_AdminHeader_module_css__WEBPACK_IMPORTED_MODULE_3___default().dropdownItem),\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        className: (_styles_admin_AdminHeader_module_css__WEBPACK_IMPORTED_MODULE_3___default().dropdownIcon),\n                                                        children: \"\\uD83D\\uDD12\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\connectplusapps website\\\\admin.oceansoulsparkles.com.au\\\\oceansoulsparkles-admin\\\\components\\\\admin\\\\AdminHeader.tsx\",\n                                                        lineNumber: 177,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    \"Security & MFA\"\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\connectplusapps website\\\\admin.oceansoulsparkles.com.au\\\\oceansoulsparkles-admin\\\\components\\\\admin\\\\AdminHeader.tsx\",\n                                                lineNumber: 176,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_2___default()), {\n                                                href: \"/admin/preferences\",\n                                                className: (_styles_admin_AdminHeader_module_css__WEBPACK_IMPORTED_MODULE_3___default().dropdownItem),\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        className: (_styles_admin_AdminHeader_module_css__WEBPACK_IMPORTED_MODULE_3___default().dropdownIcon),\n                                                        children: \"⚙️\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\connectplusapps website\\\\admin.oceansoulsparkles.com.au\\\\oceansoulsparkles-admin\\\\components\\\\admin\\\\AdminHeader.tsx\",\n                                                        lineNumber: 181,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    \"Preferences\"\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\connectplusapps website\\\\admin.oceansoulsparkles.com.au\\\\oceansoulsparkles-admin\\\\components\\\\admin\\\\AdminHeader.tsx\",\n                                                lineNumber: 180,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: (_styles_admin_AdminHeader_module_css__WEBPACK_IMPORTED_MODULE_3___default().dropdownDivider)\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\connectplusapps website\\\\admin.oceansoulsparkles.com.au\\\\oceansoulsparkles-admin\\\\components\\\\admin\\\\AdminHeader.tsx\",\n                                                lineNumber: 184,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_2___default()), {\n                                                href: \"/admin/help\",\n                                                className: (_styles_admin_AdminHeader_module_css__WEBPACK_IMPORTED_MODULE_3___default().dropdownItem),\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        className: (_styles_admin_AdminHeader_module_css__WEBPACK_IMPORTED_MODULE_3___default().dropdownIcon),\n                                                        children: \"❓\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\connectplusapps website\\\\admin.oceansoulsparkles.com.au\\\\oceansoulsparkles-admin\\\\components\\\\admin\\\\AdminHeader.tsx\",\n                                                        lineNumber: 186,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    \"Help & Support\"\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\connectplusapps website\\\\admin.oceansoulsparkles.com.au\\\\oceansoulsparkles-admin\\\\components\\\\admin\\\\AdminHeader.tsx\",\n                                                lineNumber: 185,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: (_styles_admin_AdminHeader_module_css__WEBPACK_IMPORTED_MODULE_3___default().dropdownDivider)\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\connectplusapps website\\\\admin.oceansoulsparkles.com.au\\\\oceansoulsparkles-admin\\\\components\\\\admin\\\\AdminHeader.tsx\",\n                                                lineNumber: 189,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                className: `${(_styles_admin_AdminHeader_module_css__WEBPACK_IMPORTED_MODULE_3___default().dropdownItem)} ${(_styles_admin_AdminHeader_module_css__WEBPACK_IMPORTED_MODULE_3___default().logoutItem)}`,\n                                                onClick: onLogout,\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        className: (_styles_admin_AdminHeader_module_css__WEBPACK_IMPORTED_MODULE_3___default().dropdownIcon),\n                                                        children: \"\\uD83D\\uDEAA\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\connectplusapps website\\\\admin.oceansoulsparkles.com.au\\\\oceansoulsparkles-admin\\\\components\\\\admin\\\\AdminHeader.tsx\",\n                                                        lineNumber: 194,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    \"Sign Out\"\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\connectplusapps website\\\\admin.oceansoulsparkles.com.au\\\\oceansoulsparkles-admin\\\\components\\\\admin\\\\AdminHeader.tsx\",\n                                                lineNumber: 190,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\connectplusapps website\\\\admin.oceansoulsparkles.com.au\\\\oceansoulsparkles-admin\\\\components\\\\admin\\\\AdminHeader.tsx\",\n                                        lineNumber: 171,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\connectplusapps website\\\\admin.oceansoulsparkles.com.au\\\\oceansoulsparkles-admin\\\\components\\\\admin\\\\AdminHeader.tsx\",\n                                lineNumber: 160,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\connectplusapps website\\\\admin.oceansoulsparkles.com.au\\\\oceansoulsparkles-admin\\\\components\\\\admin\\\\AdminHeader.tsx\",\n                        lineNumber: 135,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\connectplusapps website\\\\admin.oceansoulsparkles.com.au\\\\oceansoulsparkles-admin\\\\components\\\\admin\\\\AdminHeader.tsx\",\n                lineNumber: 73,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\connectplusapps website\\\\admin.oceansoulsparkles.com.au\\\\oceansoulsparkles-admin\\\\components\\\\admin\\\\AdminHeader.tsx\",\n        lineNumber: 50,\n        columnNumber: 5\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiLi9jb21wb25lbnRzL2FkbWluL0FkbWluSGVhZGVyLnRzeCIsIm1hcHBpbmdzIjoiOzs7Ozs7Ozs7Ozs7O0FBQW9EO0FBQ3ZCO0FBQ2tDO0FBZWhELFNBQVNLLFlBQVksRUFBRUMsSUFBSSxFQUFFQyxRQUFRLEVBQUVDLGVBQWUsRUFBRUMsZ0JBQWdCLEVBQW9CO0lBQ3pHLE1BQU0sQ0FBQ0MsY0FBY0MsZ0JBQWdCLEdBQUdYLCtDQUFRQSxDQUFDO0lBQ2pELE1BQU0sQ0FBQ1ksbUJBQW1CQyxxQkFBcUIsR0FBR2IsK0NBQVFBLENBQUM7SUFDM0QsTUFBTWMsY0FBY2IsNkNBQU1BLENBQWlCO0lBQzNDLE1BQU1jLG1CQUFtQmQsNkNBQU1BLENBQWlCO0lBRWhELHdDQUF3QztJQUN4Q0MsZ0RBQVNBLENBQUM7UUFDUixNQUFNYyxxQkFBcUIsQ0FBQ0M7WUFDMUIsSUFBSUgsWUFBWUksT0FBTyxJQUFJLENBQUNKLFlBQVlJLE9BQU8sQ0FBQ0MsUUFBUSxDQUFDRixNQUFNRyxNQUFNLEdBQVc7Z0JBQzlFVCxnQkFBZ0I7WUFDbEI7WUFDQSxJQUFJSSxpQkFBaUJHLE9BQU8sSUFBSSxDQUFDSCxpQkFBaUJHLE9BQU8sQ0FBQ0MsUUFBUSxDQUFDRixNQUFNRyxNQUFNLEdBQVc7Z0JBQ3hGUCxxQkFBcUI7WUFDdkI7UUFDRjtRQUVBUSxTQUFTQyxnQkFBZ0IsQ0FBQyxhQUFhTjtRQUN2QyxPQUFPLElBQU1LLFNBQVNFLG1CQUFtQixDQUFDLGFBQWFQO0lBQ3pELEdBQUcsRUFBRTtJQUVMLE1BQU1RLGVBQWUsQ0FBQ0M7UUFDcEIsT0FBUUE7WUFDTixLQUFLO2dCQUFPLE9BQU87WUFDbkIsS0FBSztnQkFBUyxPQUFPO1lBQ3JCLEtBQUs7Z0JBQVUsT0FBTztZQUN0QixLQUFLO2dCQUFXLE9BQU87WUFDdkI7Z0JBQVMsT0FBTztRQUNsQjtJQUNGO0lBRUEscUJBQ0UsOERBQUNDO1FBQU9DLFdBQVd2Qix5RkFBa0I7OzBCQUNuQyw4REFBQ3lCO2dCQUFJRixXQUFXdkIsd0ZBQWlCOztrQ0FDL0IsOERBQUMyQjt3QkFDQ0osV0FBV3ZCLDJGQUFvQjt3QkFDL0I2QixTQUFTekI7d0JBQ1QwQixPQUFPekIsbUJBQW1CLG1CQUFtQjtrQ0FFN0MsNEVBQUMwQjs0QkFBS1IsV0FBV3ZCLHVGQUFnQjs7OENBQy9CLDhEQUFDK0I7Ozs7OzhDQUNELDhEQUFDQTs7Ozs7OENBQ0QsOERBQUNBOzs7Ozs7Ozs7Ozs7Ozs7O2tDQUlMLDhEQUFDTjt3QkFBSUYsV0FBV3ZCLHdGQUFpQjs7MENBQy9CLDhEQUFDRCxrREFBSUE7Z0NBQUNtQyxNQUFLO2dDQUFtQlgsV0FBV3ZCLDRGQUFxQjswQ0FBRTs7Ozs7OzBDQUdoRSw4REFBQytCO2dDQUFLUixXQUFXdkIsaUdBQTBCOzBDQUFFOzs7Ozs7MENBQzdDLDhEQUFDK0I7Z0NBQUtSLFdBQVd2QiwrRkFBd0I7MENBQUU7Ozs7Ozs7Ozs7Ozs7Ozs7OzswQkFJL0MsOERBQUN5QjtnQkFBSUYsV0FBV3ZCLHlGQUFrQjs7a0NBRWhDLDhEQUFDeUI7d0JBQUlGLFdBQVd2QiwwRkFBbUI7OzBDQUNqQyw4REFBQ0Qsa0RBQUlBO2dDQUFDbUMsTUFBSztnQ0FBc0JYLFdBQVd2Qix5RkFBa0I7Z0NBQUU4QixPQUFNOzBDQUFjOzs7Ozs7MENBR3BGLDhEQUFDL0Isa0RBQUlBO2dDQUFDbUMsTUFBSztnQ0FBdUJYLFdBQVd2Qix5RkFBa0I7Z0NBQUU4QixPQUFNOzBDQUFlOzs7Ozs7MENBR3RGLDhEQUFDSDtnQ0FBT0osV0FBV3ZCLHlGQUFrQjtnQ0FBRThCLE9BQU07MENBQVU7Ozs7Ozs7Ozs7OztrQ0FNekQsOERBQUNMO3dCQUFJRixXQUFXdkIsMkZBQW9CO3dCQUFFMEMsS0FBSy9COzswQ0FDekMsOERBQUNnQjtnQ0FDQ0osV0FBV3ZCLGdHQUF5QjtnQ0FDcEM2QixTQUFTLElBQU1wQixxQkFBcUIsQ0FBQ0Q7Z0NBQ3JDc0IsT0FBTTs7b0NBQ1A7a0RBRUMsOERBQUNDO3dDQUFLUixXQUFXdkIsK0ZBQXdCO2tEQUFFOzs7Ozs7Ozs7Ozs7NEJBRzVDUSxtQ0FDQyw4REFBQ2lCO2dDQUFJRixXQUFXdkIsa0dBQTJCOztrREFDekMsOERBQUN5Qjt3Q0FBSUYsV0FBV3ZCLGdHQUF5Qjs7MERBQ3ZDLDhEQUFDK0M7MERBQUc7Ozs7OzswREFDSiw4REFBQ3BCO2dEQUFPSixXQUFXdkIseUZBQWtCOzBEQUFFOzs7Ozs7Ozs7Ozs7a0RBRXpDLDhEQUFDeUI7d0NBQUlGLFdBQVd2Qiw4RkFBdUI7OzBEQUNyQyw4REFBQ3lCO2dEQUFJRixXQUFXdkIsOEZBQXVCOztrRUFDckMsOERBQUN5Qjt3REFBSUYsV0FBV3ZCLDhGQUF1QjtrRUFBRTs7Ozs7O2tFQUN6Qyw4REFBQ3lCO3dEQUFJRixXQUFXdkIsaUdBQTBCOzswRUFDeEMsOERBQUN5QjtnRUFBSUYsV0FBV3ZCLCtGQUF3QjswRUFBRTs7Ozs7OzBFQUMxQyw4REFBQ3lCO2dFQUFJRixXQUFXdkIsOEZBQXVCOzBFQUFFOzs7Ozs7Ozs7Ozs7Ozs7Ozs7MERBRzdDLDhEQUFDeUI7Z0RBQUlGLFdBQVd2Qiw4RkFBdUI7O2tFQUNyQyw4REFBQ3lCO3dEQUFJRixXQUFXdkIsOEZBQXVCO2tFQUFFOzs7Ozs7a0VBQ3pDLDhEQUFDeUI7d0RBQUlGLFdBQVd2QixpR0FBMEI7OzBFQUN4Qyw4REFBQ3lCO2dFQUFJRixXQUFXdkIsK0ZBQXdCOzBFQUFFOzs7Ozs7MEVBQzFDLDhEQUFDeUI7Z0VBQUlGLFdBQVd2Qiw4RkFBdUI7MEVBQUU7Ozs7Ozs7Ozs7Ozs7Ozs7OzswREFHN0MsOERBQUN5QjtnREFBSUYsV0FBV3ZCLDhGQUF1Qjs7a0VBQ3JDLDhEQUFDeUI7d0RBQUlGLFdBQVd2Qiw4RkFBdUI7a0VBQUU7Ozs7OztrRUFDekMsOERBQUN5Qjt3REFBSUYsV0FBV3ZCLGlHQUEwQjs7MEVBQ3hDLDhEQUFDeUI7Z0VBQUlGLFdBQVd2QiwrRkFBd0I7MEVBQUU7Ozs7OzswRUFDMUMsOERBQUN5QjtnRUFBSUYsV0FBV3ZCLDhGQUF1QjswRUFBRTs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7O2tEQUkvQyw4REFBQ3lCO3dDQUFJRixXQUFXdkIsZ0dBQXlCO2tEQUN2Qyw0RUFBQ0Qsa0RBQUlBOzRDQUFDbUMsTUFBSztzREFBdUI7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7O2tDQU8xQyw4REFBQ1Q7d0JBQUlGLFdBQVd2QixzRkFBZTt3QkFBRTBDLEtBQUtoQzs7MENBQ3BDLDhEQUFDaUI7Z0NBQ0NKLFdBQVd2Qix3RkFBaUI7Z0NBQzVCNkIsU0FBUyxJQUFNdEIsZ0JBQWdCLENBQUNEOztrREFFaEMsOERBQUNtQjt3Q0FBSUYsV0FBV3ZCLHdGQUFpQjs7NENBQzlCRSxLQUFLeUQsU0FBUyxDQUFDQyxNQUFNLENBQUM7NENBQUkxRCxLQUFLMkQsUUFBUSxDQUFDRCxNQUFNLENBQUM7Ozs7Ozs7a0RBRWxELDhEQUFDbkM7d0NBQUlGLFdBQVd2QixzRkFBZTs7MERBQzdCLDhEQUFDeUI7Z0RBQUlGLFdBQVd2QixzRkFBZTs7b0RBQzVCRSxLQUFLeUQsU0FBUztvREFBQztvREFBRXpELEtBQUsyRCxRQUFROzs7Ozs7OzBEQUVqQyw4REFBQ3BDO2dEQUNDRixXQUFXdkIsc0ZBQWU7Z0RBQzFCaUUsT0FBTztvREFBRUMsT0FBTzlDLGFBQWFsQixLQUFLbUIsSUFBSTtnREFBRTswREFFdkNuQixLQUFLbUIsSUFBSTs7Ozs7Ozs7Ozs7O2tEQUdkLDhEQUFDSTt3Q0FBSUYsV0FBV3ZCLDJGQUFvQjtrREFDakNNLGVBQWUsTUFBTTs7Ozs7Ozs7Ozs7OzRCQUl6QkEsOEJBQ0MsOERBQUNtQjtnQ0FBSUYsV0FBV3ZCLDBGQUFtQjs7a0RBQ2pDLDhEQUFDeUI7d0NBQUlGLFdBQVd2QixnR0FBeUI7OzBEQUN2Qyw4REFBQ3lCO2dEQUFJRixXQUFXdkIsdUZBQWdCOzBEQUFHRSxLQUFLcUUsS0FBSzs7Ozs7OzBEQUM3Qyw4REFBQzlDO2dEQUNDRixXQUFXdkIsMkZBQW9CO2dEQUMvQmlFLE9BQU87b0RBQUVRLGlCQUFpQnJELGFBQWFsQixLQUFLbUIsSUFBSTtnREFBRTswREFFakRuQixLQUFLbUIsSUFBSTs7Ozs7Ozs7Ozs7O2tEQUlkLDhEQUFDSTt3Q0FBSUYsV0FBV3ZCLDhGQUF1Qjs7MERBQ3JDLDhEQUFDRCxrREFBSUE7Z0RBQUNtQyxNQUFLO2dEQUFpQlgsV0FBV3ZCLDBGQUFtQjs7a0VBQ3hELDhEQUFDK0I7d0RBQUtSLFdBQVd2QiwwRkFBbUI7a0VBQUU7Ozs7OztvREFBUzs7Ozs7OzswREFHakQsOERBQUNELGtEQUFJQTtnREFBQ21DLE1BQUs7Z0RBQWtCWCxXQUFXdkIsMEZBQW1COztrRUFDekQsOERBQUMrQjt3REFBS1IsV0FBV3ZCLDBGQUFtQjtrRUFBRTs7Ozs7O29EQUFTOzs7Ozs7OzBEQUdqRCw4REFBQ0Qsa0RBQUlBO2dEQUFDbUMsTUFBSztnREFBcUJYLFdBQVd2QiwwRkFBbUI7O2tFQUM1RCw4REFBQytCO3dEQUFLUixXQUFXdkIsMEZBQW1CO2tFQUFFOzs7Ozs7b0RBQVM7Ozs7Ozs7MERBR2pELDhEQUFDeUI7Z0RBQUlGLFdBQVd2Qiw2RkFBc0I7Ozs7OzswREFDdEMsOERBQUNELGtEQUFJQTtnREFBQ21DLE1BQUs7Z0RBQWNYLFdBQVd2QiwwRkFBbUI7O2tFQUNyRCw4REFBQytCO3dEQUFLUixXQUFXdkIsMEZBQW1CO2tFQUFFOzs7Ozs7b0RBQVE7Ozs7Ozs7MERBR2hELDhEQUFDeUI7Z0RBQUlGLFdBQVd2Qiw2RkFBc0I7Ozs7OzswREFDdEMsOERBQUMyQjtnREFDQ0osV0FBVyxDQUFDLEVBQUV2QiwwRkFBbUIsQ0FBQyxDQUFDLEVBQUVBLHdGQUFpQixDQUFDLENBQUM7Z0RBQ3hENkIsU0FBUzFCOztrRUFFVCw4REFBQzRCO3dEQUFLUixXQUFXdkIsMEZBQW1CO2tFQUFFOzs7Ozs7b0RBQVM7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7QUFVakUiLCJzb3VyY2VzIjpbIndlYnBhY2s6Ly9vY2VhbnNvdWxzcGFya2xlcy1hZG1pbi8uL2NvbXBvbmVudHMvYWRtaW4vQWRtaW5IZWFkZXIudHN4PzZiZWYiXSwic291cmNlc0NvbnRlbnQiOlsiaW1wb3J0IHsgdXNlU3RhdGUsIHVzZVJlZiwgdXNlRWZmZWN0IH0gZnJvbSAncmVhY3QnO1xyXG5pbXBvcnQgTGluayBmcm9tICduZXh0L2xpbmsnO1xyXG5pbXBvcnQgc3R5bGVzIGZyb20gJy4uLy4uL3N0eWxlcy9hZG1pbi9BZG1pbkhlYWRlci5tb2R1bGUuY3NzJztcclxuXHJcbmludGVyZmFjZSBBZG1pbkhlYWRlclByb3BzIHtcclxuICB1c2VyOiB7XHJcbiAgICBpZDogc3RyaW5nO1xyXG4gICAgZW1haWw6IHN0cmluZztcclxuICAgIHJvbGU6ICdERVYnIHwgJ0FkbWluJyB8ICdBcnRpc3QnIHwgJ0JyYWlkZXInO1xyXG4gICAgZmlyc3ROYW1lOiBzdHJpbmc7XHJcbiAgICBsYXN0TmFtZTogc3RyaW5nO1xyXG4gIH07XHJcbiAgb25Mb2dvdXQ6ICgpID0+IHZvaWQ7XHJcbiAgb25Ub2dnbGVTaWRlYmFyOiAoKSA9PiB2b2lkO1xyXG4gIHNpZGViYXJDb2xsYXBzZWQ6IGJvb2xlYW47XHJcbn1cclxuXHJcbmV4cG9ydCBkZWZhdWx0IGZ1bmN0aW9uIEFkbWluSGVhZGVyKHsgdXNlciwgb25Mb2dvdXQsIG9uVG9nZ2xlU2lkZWJhciwgc2lkZWJhckNvbGxhcHNlZCB9OiBBZG1pbkhlYWRlclByb3BzKSB7XHJcbiAgY29uc3QgW3Nob3dVc2VyTWVudSwgc2V0U2hvd1VzZXJNZW51XSA9IHVzZVN0YXRlKGZhbHNlKTtcclxuICBjb25zdCBbc2hvd05vdGlmaWNhdGlvbnMsIHNldFNob3dOb3RpZmljYXRpb25zXSA9IHVzZVN0YXRlKGZhbHNlKTtcclxuICBjb25zdCB1c2VyTWVudVJlZiA9IHVzZVJlZjxIVE1MRGl2RWxlbWVudD4obnVsbCk7XHJcbiAgY29uc3Qgbm90aWZpY2F0aW9uc1JlZiA9IHVzZVJlZjxIVE1MRGl2RWxlbWVudD4obnVsbCk7XHJcblxyXG4gIC8vIENsb3NlIGRyb3Bkb3ducyB3aGVuIGNsaWNraW5nIG91dHNpZGVcclxuICB1c2VFZmZlY3QoKCkgPT4ge1xyXG4gICAgY29uc3QgaGFuZGxlQ2xpY2tPdXRzaWRlID0gKGV2ZW50OiBNb3VzZUV2ZW50KSA9PiB7XHJcbiAgICAgIGlmICh1c2VyTWVudVJlZi5jdXJyZW50ICYmICF1c2VyTWVudVJlZi5jdXJyZW50LmNvbnRhaW5zKGV2ZW50LnRhcmdldCBhcyBOb2RlKSkge1xyXG4gICAgICAgIHNldFNob3dVc2VyTWVudShmYWxzZSk7XHJcbiAgICAgIH1cclxuICAgICAgaWYgKG5vdGlmaWNhdGlvbnNSZWYuY3VycmVudCAmJiAhbm90aWZpY2F0aW9uc1JlZi5jdXJyZW50LmNvbnRhaW5zKGV2ZW50LnRhcmdldCBhcyBOb2RlKSkge1xyXG4gICAgICAgIHNldFNob3dOb3RpZmljYXRpb25zKGZhbHNlKTtcclxuICAgICAgfVxyXG4gICAgfTtcclxuXHJcbiAgICBkb2N1bWVudC5hZGRFdmVudExpc3RlbmVyKCdtb3VzZWRvd24nLCBoYW5kbGVDbGlja091dHNpZGUpO1xyXG4gICAgcmV0dXJuICgpID0+IGRvY3VtZW50LnJlbW92ZUV2ZW50TGlzdGVuZXIoJ21vdXNlZG93bicsIGhhbmRsZUNsaWNrT3V0c2lkZSk7XHJcbiAgfSwgW10pO1xyXG5cclxuICBjb25zdCBnZXRSb2xlQ29sb3IgPSAocm9sZTogc3RyaW5nKSA9PiB7XHJcbiAgICBzd2l0Y2ggKHJvbGUpIHtcclxuICAgICAgY2FzZSAnREVWJzogcmV0dXJuICcjZGMzNTQ1JztcclxuICAgICAgY2FzZSAnQWRtaW4nOiByZXR1cm4gJyMzNzg4ZDgnO1xyXG4gICAgICBjYXNlICdBcnRpc3QnOiByZXR1cm4gJyMyOGE3NDUnO1xyXG4gICAgICBjYXNlICdCcmFpZGVyJzogcmV0dXJuICcjZmQ3ZTE0JztcclxuICAgICAgZGVmYXVsdDogcmV0dXJuICcjNmM3NTdkJztcclxuICAgIH1cclxuICB9O1xyXG5cclxuICByZXR1cm4gKFxyXG4gICAgPGhlYWRlciBjbGFzc05hbWU9e3N0eWxlcy5hZG1pbkhlYWRlcn0+XHJcbiAgICAgIDxkaXYgY2xhc3NOYW1lPXtzdHlsZXMuaGVhZGVyTGVmdH0+XHJcbiAgICAgICAgPGJ1dHRvbiBcclxuICAgICAgICAgIGNsYXNzTmFtZT17c3R5bGVzLnNpZGViYXJUb2dnbGV9XHJcbiAgICAgICAgICBvbkNsaWNrPXtvblRvZ2dsZVNpZGViYXJ9XHJcbiAgICAgICAgICB0aXRsZT17c2lkZWJhckNvbGxhcHNlZCA/ICdFeHBhbmQgc2lkZWJhcicgOiAnQ29sbGFwc2Ugc2lkZWJhcid9XHJcbiAgICAgICAgPlxyXG4gICAgICAgICAgPHNwYW4gY2xhc3NOYW1lPXtzdHlsZXMuaGFtYnVyZ2VyfT5cclxuICAgICAgICAgICAgPHNwYW4+PC9zcGFuPlxyXG4gICAgICAgICAgICA8c3Bhbj48L3NwYW4+XHJcbiAgICAgICAgICAgIDxzcGFuPjwvc3Bhbj5cclxuICAgICAgICAgIDwvc3Bhbj5cclxuICAgICAgICA8L2J1dHRvbj5cclxuXHJcbiAgICAgICAgPGRpdiBjbGFzc05hbWU9e3N0eWxlcy5icmVhZGNydW1ifT5cclxuICAgICAgICAgIDxMaW5rIGhyZWY9XCIvYWRtaW4vZGFzaGJvYXJkXCIgY2xhc3NOYW1lPXtzdHlsZXMuYnJlYWRjcnVtYkxpbmt9PlxyXG4gICAgICAgICAgICBEYXNoYm9hcmRcclxuICAgICAgICAgIDwvTGluaz5cclxuICAgICAgICAgIDxzcGFuIGNsYXNzTmFtZT17c3R5bGVzLmJyZWFkY3J1bWJTZXBhcmF0b3J9Pi88L3NwYW4+XHJcbiAgICAgICAgICA8c3BhbiBjbGFzc05hbWU9e3N0eWxlcy5icmVhZGNydW1iQ3VycmVudH0+T3ZlcnZpZXc8L3NwYW4+XHJcbiAgICAgICAgPC9kaXY+XHJcbiAgICAgIDwvZGl2PlxyXG5cclxuICAgICAgPGRpdiBjbGFzc05hbWU9e3N0eWxlcy5oZWFkZXJSaWdodH0+XHJcbiAgICAgICAgey8qIFF1aWNrIEFjdGlvbnMgKi99XHJcbiAgICAgICAgPGRpdiBjbGFzc05hbWU9e3N0eWxlcy5xdWlja0FjdGlvbnN9PlxyXG4gICAgICAgICAgPExpbmsgaHJlZj1cIi9hZG1pbi9ib29raW5ncy9uZXdcIiBjbGFzc05hbWU9e3N0eWxlcy5xdWlja0FjdGlvbn0gdGl0bGU9XCJOZXcgQm9va2luZ1wiPlxyXG4gICAgICAgICAgICDwn5OFXHJcbiAgICAgICAgICA8L0xpbms+XHJcbiAgICAgICAgICA8TGluayBocmVmPVwiL2FkbWluL2N1c3RvbWVycy9uZXdcIiBjbGFzc05hbWU9e3N0eWxlcy5xdWlja0FjdGlvbn0gdGl0bGU9XCJOZXcgQ3VzdG9tZXJcIj5cclxuICAgICAgICAgICAg8J+RpFxyXG4gICAgICAgICAgPC9MaW5rPlxyXG4gICAgICAgICAgPGJ1dHRvbiBjbGFzc05hbWU9e3N0eWxlcy5xdWlja0FjdGlvbn0gdGl0bGU9XCJSZWZyZXNoXCI+XHJcbiAgICAgICAgICAgIPCflIRcclxuICAgICAgICAgIDwvYnV0dG9uPlxyXG4gICAgICAgIDwvZGl2PlxyXG5cclxuICAgICAgICB7LyogTm90aWZpY2F0aW9ucyAqL31cclxuICAgICAgICA8ZGl2IGNsYXNzTmFtZT17c3R5bGVzLm5vdGlmaWNhdGlvbnN9IHJlZj17bm90aWZpY2F0aW9uc1JlZn0+XHJcbiAgICAgICAgICA8YnV0dG9uIFxyXG4gICAgICAgICAgICBjbGFzc05hbWU9e3N0eWxlcy5ub3RpZmljYXRpb25CdXR0b259XHJcbiAgICAgICAgICAgIG9uQ2xpY2s9eygpID0+IHNldFNob3dOb3RpZmljYXRpb25zKCFzaG93Tm90aWZpY2F0aW9ucyl9XHJcbiAgICAgICAgICAgIHRpdGxlPVwiTm90aWZpY2F0aW9uc1wiXHJcbiAgICAgICAgICA+XHJcbiAgICAgICAgICAgIPCflJRcclxuICAgICAgICAgICAgPHNwYW4gY2xhc3NOYW1lPXtzdHlsZXMubm90aWZpY2F0aW9uQmFkZ2V9PjM8L3NwYW4+XHJcbiAgICAgICAgICA8L2J1dHRvbj5cclxuXHJcbiAgICAgICAgICB7c2hvd05vdGlmaWNhdGlvbnMgJiYgKFxyXG4gICAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT17c3R5bGVzLm5vdGlmaWNhdGlvbkRyb3Bkb3dufT5cclxuICAgICAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT17c3R5bGVzLm5vdGlmaWNhdGlvbkhlYWRlcn0+XHJcbiAgICAgICAgICAgICAgICA8aDM+Tm90aWZpY2F0aW9uczwvaDM+XHJcbiAgICAgICAgICAgICAgICA8YnV0dG9uIGNsYXNzTmFtZT17c3R5bGVzLm1hcmtBbGxSZWFkfT5NYXJrIGFsbCByZWFkPC9idXR0b24+XHJcbiAgICAgICAgICAgICAgPC9kaXY+XHJcbiAgICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9e3N0eWxlcy5ub3RpZmljYXRpb25MaXN0fT5cclxuICAgICAgICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPXtzdHlsZXMubm90aWZpY2F0aW9uSXRlbX0+XHJcbiAgICAgICAgICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPXtzdHlsZXMubm90aWZpY2F0aW9uSWNvbn0+8J+ThTwvZGl2PlxyXG4gICAgICAgICAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT17c3R5bGVzLm5vdGlmaWNhdGlvbkNvbnRlbnR9PlxyXG4gICAgICAgICAgICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPXtzdHlsZXMubm90aWZpY2F0aW9uVGl0bGV9Pk5ldyBib29raW5nIHJlcXVlc3Q8L2Rpdj5cclxuICAgICAgICAgICAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT17c3R5bGVzLm5vdGlmaWNhdGlvblRpbWV9PjUgbWludXRlcyBhZ288L2Rpdj5cclxuICAgICAgICAgICAgICAgICAgPC9kaXY+XHJcbiAgICAgICAgICAgICAgICA8L2Rpdj5cclxuICAgICAgICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPXtzdHlsZXMubm90aWZpY2F0aW9uSXRlbX0+XHJcbiAgICAgICAgICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPXtzdHlsZXMubm90aWZpY2F0aW9uSWNvbn0+8J+SsDwvZGl2PlxyXG4gICAgICAgICAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT17c3R5bGVzLm5vdGlmaWNhdGlvbkNvbnRlbnR9PlxyXG4gICAgICAgICAgICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPXtzdHlsZXMubm90aWZpY2F0aW9uVGl0bGV9PlBheW1lbnQgcmVjZWl2ZWQ8L2Rpdj5cclxuICAgICAgICAgICAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT17c3R5bGVzLm5vdGlmaWNhdGlvblRpbWV9PjEgaG91ciBhZ288L2Rpdj5cclxuICAgICAgICAgICAgICAgICAgPC9kaXY+XHJcbiAgICAgICAgICAgICAgICA8L2Rpdj5cclxuICAgICAgICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPXtzdHlsZXMubm90aWZpY2F0aW9uSXRlbX0+XHJcbiAgICAgICAgICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPXtzdHlsZXMubm90aWZpY2F0aW9uSWNvbn0+4pqg77iPPC9kaXY+XHJcbiAgICAgICAgICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPXtzdHlsZXMubm90aWZpY2F0aW9uQ29udGVudH0+XHJcbiAgICAgICAgICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9e3N0eWxlcy5ub3RpZmljYXRpb25UaXRsZX0+TG93IGludmVudG9yeSBhbGVydDwvZGl2PlxyXG4gICAgICAgICAgICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPXtzdHlsZXMubm90aWZpY2F0aW9uVGltZX0+MiBob3VycyBhZ288L2Rpdj5cclxuICAgICAgICAgICAgICAgICAgPC9kaXY+XHJcbiAgICAgICAgICAgICAgICA8L2Rpdj5cclxuICAgICAgICAgICAgICA8L2Rpdj5cclxuICAgICAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT17c3R5bGVzLm5vdGlmaWNhdGlvbkZvb3Rlcn0+XHJcbiAgICAgICAgICAgICAgICA8TGluayBocmVmPVwiL2FkbWluL25vdGlmaWNhdGlvbnNcIj5WaWV3IGFsbCBub3RpZmljYXRpb25zPC9MaW5rPlxyXG4gICAgICAgICAgICAgIDwvZGl2PlxyXG4gICAgICAgICAgICA8L2Rpdj5cclxuICAgICAgICAgICl9XHJcbiAgICAgICAgPC9kaXY+XHJcblxyXG4gICAgICAgIHsvKiBVc2VyIE1lbnUgKi99XHJcbiAgICAgICAgPGRpdiBjbGFzc05hbWU9e3N0eWxlcy51c2VyTWVudX0gcmVmPXt1c2VyTWVudVJlZn0+XHJcbiAgICAgICAgICA8YnV0dG9uIFxyXG4gICAgICAgICAgICBjbGFzc05hbWU9e3N0eWxlcy51c2VyQnV0dG9ufVxyXG4gICAgICAgICAgICBvbkNsaWNrPXsoKSA9PiBzZXRTaG93VXNlck1lbnUoIXNob3dVc2VyTWVudSl9XHJcbiAgICAgICAgICA+XHJcbiAgICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPXtzdHlsZXMudXNlckF2YXRhcn0+XHJcbiAgICAgICAgICAgICAge3VzZXIuZmlyc3ROYW1lLmNoYXJBdCgwKX17dXNlci5sYXN0TmFtZS5jaGFyQXQoMCl9XHJcbiAgICAgICAgICAgIDwvZGl2PlxyXG4gICAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT17c3R5bGVzLnVzZXJJbmZvfT5cclxuICAgICAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT17c3R5bGVzLnVzZXJOYW1lfT5cclxuICAgICAgICAgICAgICAgIHt1c2VyLmZpcnN0TmFtZX0ge3VzZXIubGFzdE5hbWV9XHJcbiAgICAgICAgICAgICAgPC9kaXY+XHJcbiAgICAgICAgICAgICAgPGRpdiBcclxuICAgICAgICAgICAgICAgIGNsYXNzTmFtZT17c3R5bGVzLnVzZXJSb2xlfVxyXG4gICAgICAgICAgICAgICAgc3R5bGU9e3sgY29sb3I6IGdldFJvbGVDb2xvcih1c2VyLnJvbGUpIH19XHJcbiAgICAgICAgICAgICAgPlxyXG4gICAgICAgICAgICAgICAge3VzZXIucm9sZX1cclxuICAgICAgICAgICAgICA8L2Rpdj5cclxuICAgICAgICAgICAgPC9kaXY+XHJcbiAgICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPXtzdHlsZXMuZHJvcGRvd25BcnJvd30+XHJcbiAgICAgICAgICAgICAge3Nob3dVc2VyTWVudSA/ICfilrInIDogJ+KWvCd9XHJcbiAgICAgICAgICAgIDwvZGl2PlxyXG4gICAgICAgICAgPC9idXR0b24+XHJcblxyXG4gICAgICAgICAge3Nob3dVc2VyTWVudSAmJiAoXHJcbiAgICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPXtzdHlsZXMudXNlckRyb3Bkb3dufT5cclxuICAgICAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT17c3R5bGVzLnVzZXJEcm9wZG93bkhlYWRlcn0+XHJcbiAgICAgICAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT17c3R5bGVzLnVzZXJFbWFpbH0+e3VzZXIuZW1haWx9PC9kaXY+XHJcbiAgICAgICAgICAgICAgICA8ZGl2IFxyXG4gICAgICAgICAgICAgICAgICBjbGFzc05hbWU9e3N0eWxlcy51c2VyUm9sZUJhZGdlfVxyXG4gICAgICAgICAgICAgICAgICBzdHlsZT17eyBiYWNrZ3JvdW5kQ29sb3I6IGdldFJvbGVDb2xvcih1c2VyLnJvbGUpIH19XHJcbiAgICAgICAgICAgICAgICA+XHJcbiAgICAgICAgICAgICAgICAgIHt1c2VyLnJvbGV9XHJcbiAgICAgICAgICAgICAgICA8L2Rpdj5cclxuICAgICAgICAgICAgICA8L2Rpdj5cclxuICAgICAgICAgICAgICBcclxuICAgICAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT17c3R5bGVzLnVzZXJEcm9wZG93bk1lbnV9PlxyXG4gICAgICAgICAgICAgICAgPExpbmsgaHJlZj1cIi9hZG1pbi9wcm9maWxlXCIgY2xhc3NOYW1lPXtzdHlsZXMuZHJvcGRvd25JdGVtfT5cclxuICAgICAgICAgICAgICAgICAgPHNwYW4gY2xhc3NOYW1lPXtzdHlsZXMuZHJvcGRvd25JY29ufT7wn5GkPC9zcGFuPlxyXG4gICAgICAgICAgICAgICAgICBQcm9maWxlIFNldHRpbmdzXHJcbiAgICAgICAgICAgICAgICA8L0xpbms+XHJcbiAgICAgICAgICAgICAgICA8TGluayBocmVmPVwiL2FkbWluL3NlY3VyaXR5XCIgY2xhc3NOYW1lPXtzdHlsZXMuZHJvcGRvd25JdGVtfT5cclxuICAgICAgICAgICAgICAgICAgPHNwYW4gY2xhc3NOYW1lPXtzdHlsZXMuZHJvcGRvd25JY29ufT7wn5SSPC9zcGFuPlxyXG4gICAgICAgICAgICAgICAgICBTZWN1cml0eSAmIE1GQVxyXG4gICAgICAgICAgICAgICAgPC9MaW5rPlxyXG4gICAgICAgICAgICAgICAgPExpbmsgaHJlZj1cIi9hZG1pbi9wcmVmZXJlbmNlc1wiIGNsYXNzTmFtZT17c3R5bGVzLmRyb3Bkb3duSXRlbX0+XHJcbiAgICAgICAgICAgICAgICAgIDxzcGFuIGNsYXNzTmFtZT17c3R5bGVzLmRyb3Bkb3duSWNvbn0+4pqZ77iPPC9zcGFuPlxyXG4gICAgICAgICAgICAgICAgICBQcmVmZXJlbmNlc1xyXG4gICAgICAgICAgICAgICAgPC9MaW5rPlxyXG4gICAgICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9e3N0eWxlcy5kcm9wZG93bkRpdmlkZXJ9PjwvZGl2PlxyXG4gICAgICAgICAgICAgICAgPExpbmsgaHJlZj1cIi9hZG1pbi9oZWxwXCIgY2xhc3NOYW1lPXtzdHlsZXMuZHJvcGRvd25JdGVtfT5cclxuICAgICAgICAgICAgICAgICAgPHNwYW4gY2xhc3NOYW1lPXtzdHlsZXMuZHJvcGRvd25JY29ufT7inZM8L3NwYW4+XHJcbiAgICAgICAgICAgICAgICAgIEhlbHAgJiBTdXBwb3J0XHJcbiAgICAgICAgICAgICAgICA8L0xpbms+XHJcbiAgICAgICAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT17c3R5bGVzLmRyb3Bkb3duRGl2aWRlcn0+PC9kaXY+XHJcbiAgICAgICAgICAgICAgICA8YnV0dG9uIFxyXG4gICAgICAgICAgICAgICAgICBjbGFzc05hbWU9e2Ake3N0eWxlcy5kcm9wZG93bkl0ZW19ICR7c3R5bGVzLmxvZ291dEl0ZW19YH1cclxuICAgICAgICAgICAgICAgICAgb25DbGljaz17b25Mb2dvdXR9XHJcbiAgICAgICAgICAgICAgICA+XHJcbiAgICAgICAgICAgICAgICAgIDxzcGFuIGNsYXNzTmFtZT17c3R5bGVzLmRyb3Bkb3duSWNvbn0+8J+aqjwvc3Bhbj5cclxuICAgICAgICAgICAgICAgICAgU2lnbiBPdXRcclxuICAgICAgICAgICAgICAgIDwvYnV0dG9uPlxyXG4gICAgICAgICAgICAgIDwvZGl2PlxyXG4gICAgICAgICAgICA8L2Rpdj5cclxuICAgICAgICAgICl9XHJcbiAgICAgICAgPC9kaXY+XHJcbiAgICAgIDwvZGl2PlxyXG4gICAgPC9oZWFkZXI+XHJcbiAgKTtcclxufVxyXG4iXSwibmFtZXMiOlsidXNlU3RhdGUiLCJ1c2VSZWYiLCJ1c2VFZmZlY3QiLCJMaW5rIiwic3R5bGVzIiwiQWRtaW5IZWFkZXIiLCJ1c2VyIiwib25Mb2dvdXQiLCJvblRvZ2dsZVNpZGViYXIiLCJzaWRlYmFyQ29sbGFwc2VkIiwic2hvd1VzZXJNZW51Iiwic2V0U2hvd1VzZXJNZW51Iiwic2hvd05vdGlmaWNhdGlvbnMiLCJzZXRTaG93Tm90aWZpY2F0aW9ucyIsInVzZXJNZW51UmVmIiwibm90aWZpY2F0aW9uc1JlZiIsImhhbmRsZUNsaWNrT3V0c2lkZSIsImV2ZW50IiwiY3VycmVudCIsImNvbnRhaW5zIiwidGFyZ2V0IiwiZG9jdW1lbnQiLCJhZGRFdmVudExpc3RlbmVyIiwicmVtb3ZlRXZlbnRMaXN0ZW5lciIsImdldFJvbGVDb2xvciIsInJvbGUiLCJoZWFkZXIiLCJjbGFzc05hbWUiLCJhZG1pbkhlYWRlciIsImRpdiIsImhlYWRlckxlZnQiLCJidXR0b24iLCJzaWRlYmFyVG9nZ2xlIiwib25DbGljayIsInRpdGxlIiwic3BhbiIsImhhbWJ1cmdlciIsImJyZWFkY3J1bWIiLCJocmVmIiwiYnJlYWRjcnVtYkxpbmsiLCJicmVhZGNydW1iU2VwYXJhdG9yIiwiYnJlYWRjcnVtYkN1cnJlbnQiLCJoZWFkZXJSaWdodCIsInF1aWNrQWN0aW9ucyIsInF1aWNrQWN0aW9uIiwibm90aWZpY2F0aW9ucyIsInJlZiIsIm5vdGlmaWNhdGlvbkJ1dHRvbiIsIm5vdGlmaWNhdGlvbkJhZGdlIiwibm90aWZpY2F0aW9uRHJvcGRvd24iLCJub3RpZmljYXRpb25IZWFkZXIiLCJoMyIsIm1hcmtBbGxSZWFkIiwibm90aWZpY2F0aW9uTGlzdCIsIm5vdGlmaWNhdGlvbkl0ZW0iLCJub3RpZmljYXRpb25JY29uIiwibm90aWZpY2F0aW9uQ29udGVudCIsIm5vdGlmaWNhdGlvblRpdGxlIiwibm90aWZpY2F0aW9uVGltZSIsIm5vdGlmaWNhdGlvbkZvb3RlciIsInVzZXJNZW51IiwidXNlckJ1dHRvbiIsInVzZXJBdmF0YXIiLCJmaXJzdE5hbWUiLCJjaGFyQXQiLCJsYXN0TmFtZSIsInVzZXJJbmZvIiwidXNlck5hbWUiLCJ1c2VyUm9sZSIsInN0eWxlIiwiY29sb3IiLCJkcm9wZG93bkFycm93IiwidXNlckRyb3Bkb3duIiwidXNlckRyb3Bkb3duSGVhZGVyIiwidXNlckVtYWlsIiwiZW1haWwiLCJ1c2VyUm9sZUJhZGdlIiwiYmFja2dyb3VuZENvbG9yIiwidXNlckRyb3Bkb3duTWVudSIsImRyb3Bkb3duSXRlbSIsImRyb3Bkb3duSWNvbiIsImRyb3Bkb3duRGl2aWRlciIsImxvZ291dEl0ZW0iXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///./components/admin/AdminHeader.tsx\n");

/***/ }),

/***/ "./components/admin/AdminLayout.tsx":
/*!******************************************!*\
  !*** ./components/admin/AdminLayout.tsx ***!
  \******************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.a(module, async (__webpack_handle_async_dependencies__, __webpack_async_result__) => { try {\n__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ AdminLayout)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"react/jsx-dev-runtime\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"react\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var next_router__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/router */ \"./node_modules/next/router.js\");\n/* harmony import */ var next_router__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(next_router__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var next_link__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! next/link */ \"./node_modules/next/link.js\");\n/* harmony import */ var next_link__WEBPACK_IMPORTED_MODULE_3___default = /*#__PURE__*/__webpack_require__.n(next_link__WEBPACK_IMPORTED_MODULE_3__);\n/* harmony import */ var react_toastify__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! react-toastify */ \"react-toastify\");\n/* harmony import */ var _AdminSidebar__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! ./AdminSidebar */ \"./components/admin/AdminSidebar.tsx\");\n/* harmony import */ var _AdminHeader__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! ./AdminHeader */ \"./components/admin/AdminHeader.tsx\");\n/* harmony import */ var _hooks_useAuth__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! ../../hooks/useAuth */ \"./hooks/useAuth.ts\");\n/* harmony import */ var _styles_admin_AdminLayout_module_css__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! ../../styles/admin/AdminLayout.module.css */ \"./styles/admin/AdminLayout.module.css\");\n/* harmony import */ var _styles_admin_AdminLayout_module_css__WEBPACK_IMPORTED_MODULE_8___default = /*#__PURE__*/__webpack_require__.n(_styles_admin_AdminLayout_module_css__WEBPACK_IMPORTED_MODULE_8__);\nvar __webpack_async_dependencies__ = __webpack_handle_async_dependencies__([react_toastify__WEBPACK_IMPORTED_MODULE_4__]);\nreact_toastify__WEBPACK_IMPORTED_MODULE_4__ = (__webpack_async_dependencies__.then ? (await __webpack_async_dependencies__)() : __webpack_async_dependencies__)[0];\n\n\n\n\n\n\n\n\n\nfunction AdminLayout({ children }) {\n    const router = (0,next_router__WEBPACK_IMPORTED_MODULE_2__.useRouter)();\n    const { user, loading } = (0,_hooks_useAuth__WEBPACK_IMPORTED_MODULE_7__.useAuth)();\n    const [sidebarCollapsed, setSidebarCollapsed] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [isMobile, setIsMobile] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        // Check if mobile\n        const checkMobile = ()=>{\n            setIsMobile(window.innerWidth < 768);\n            if (window.innerWidth < 768) {\n                setSidebarCollapsed(true);\n            }\n        };\n        checkMobile();\n        window.addEventListener(\"resize\", checkMobile);\n        return ()=>window.removeEventListener(\"resize\", checkMobile);\n    }, []);\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        // Redirect to login if not authenticated\n        if (!loading && !user) {\n            router.push(\"/admin/login\");\n        }\n    }, [\n        user,\n        loading,\n        router\n    ]);\n    const handleLogout = async ()=>{\n        try {\n            const response = await fetch(\"/api/auth/logout\", {\n                method: \"POST\",\n                headers: {\n                    \"Authorization\": `Bearer ${localStorage.getItem(\"admin-token\")}`\n                }\n            });\n            if (response.ok) {\n                localStorage.removeItem(\"admin-token\");\n                react_toastify__WEBPACK_IMPORTED_MODULE_4__.toast.success(\"Logged out successfully\");\n                router.push(\"/admin/login\");\n            } else {\n                throw new Error(\"Logout failed\");\n            }\n        } catch (error) {\n            console.error(\"Logout error:\", error);\n            // Force logout even if API call fails\n            localStorage.removeItem(\"admin-token\");\n            router.push(\"/admin/login\");\n        }\n    };\n    const toggleSidebar = ()=>{\n        setSidebarCollapsed(!sidebarCollapsed);\n    };\n    if (loading) {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: (_styles_admin_AdminLayout_module_css__WEBPACK_IMPORTED_MODULE_8___default().loadingContainer),\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: (_styles_admin_AdminLayout_module_css__WEBPACK_IMPORTED_MODULE_8___default().loadingSpinner)\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\connectplusapps website\\\\admin.oceansoulsparkles.com.au\\\\oceansoulsparkles-admin\\\\components\\\\admin\\\\AdminLayout.tsx\",\n                    lineNumber: 72,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                    children: \"Loading admin portal...\"\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\connectplusapps website\\\\admin.oceansoulsparkles.com.au\\\\oceansoulsparkles-admin\\\\components\\\\admin\\\\AdminLayout.tsx\",\n                    lineNumber: 73,\n                    columnNumber: 9\n                }, this)\n            ]\n        }, void 0, true, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\connectplusapps website\\\\admin.oceansoulsparkles.com.au\\\\oceansoulsparkles-admin\\\\components\\\\admin\\\\AdminLayout.tsx\",\n            lineNumber: 71,\n            columnNumber: 7\n        }, this);\n    }\n    if (!user) {\n        return null; // Will redirect to login\n    }\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: (_styles_admin_AdminLayout_module_css__WEBPACK_IMPORTED_MODULE_8___default().adminLayout),\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_AdminSidebar__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {\n                user: user,\n                collapsed: sidebarCollapsed,\n                onToggle: toggleSidebar,\n                isMobile: isMobile\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\connectplusapps website\\\\admin.oceansoulsparkles.com.au\\\\oceansoulsparkles-admin\\\\components\\\\admin\\\\AdminLayout.tsx\",\n                lineNumber: 85,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: `${(_styles_admin_AdminLayout_module_css__WEBPACK_IMPORTED_MODULE_8___default().mainContent)} ${sidebarCollapsed ? (_styles_admin_AdminLayout_module_css__WEBPACK_IMPORTED_MODULE_8___default().sidebarCollapsed) : \"\"}`,\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_AdminHeader__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {\n                        user: user,\n                        onLogout: handleLogout,\n                        onToggleSidebar: toggleSidebar,\n                        sidebarCollapsed: sidebarCollapsed\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\connectplusapps website\\\\admin.oceansoulsparkles.com.au\\\\oceansoulsparkles-admin\\\\components\\\\admin\\\\AdminLayout.tsx\",\n                        lineNumber: 95,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"main\", {\n                        className: (_styles_admin_AdminLayout_module_css__WEBPACK_IMPORTED_MODULE_8___default().pageContent),\n                        children: children\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\connectplusapps website\\\\admin.oceansoulsparkles.com.au\\\\oceansoulsparkles-admin\\\\components\\\\admin\\\\AdminLayout.tsx\",\n                        lineNumber: 103,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"footer\", {\n                        className: (_styles_admin_AdminLayout_module_css__WEBPACK_IMPORTED_MODULE_8___default().adminFooter),\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: (_styles_admin_AdminLayout_module_css__WEBPACK_IMPORTED_MODULE_8___default().footerContent),\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: (_styles_admin_AdminLayout_module_css__WEBPACK_IMPORTED_MODULE_8___default().footerLeft),\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                            children: \"\\xa9 2024 Ocean Soul Sparkles Admin Portal\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\connectplusapps website\\\\admin.oceansoulsparkles.com.au\\\\oceansoulsparkles-admin\\\\components\\\\admin\\\\AdminLayout.tsx\",\n                                            lineNumber: 111,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                            className: (_styles_admin_AdminLayout_module_css__WEBPACK_IMPORTED_MODULE_8___default().version),\n                                            children: \"v1.0.0\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\connectplusapps website\\\\admin.oceansoulsparkles.com.au\\\\oceansoulsparkles-admin\\\\components\\\\admin\\\\AdminLayout.tsx\",\n                                            lineNumber: 112,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\connectplusapps website\\\\admin.oceansoulsparkles.com.au\\\\oceansoulsparkles-admin\\\\components\\\\admin\\\\AdminLayout.tsx\",\n                                    lineNumber: 110,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: (_styles_admin_AdminLayout_module_css__WEBPACK_IMPORTED_MODULE_8___default().footerRight),\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_3___default()), {\n                                            href: \"/admin/help\",\n                                            className: (_styles_admin_AdminLayout_module_css__WEBPACK_IMPORTED_MODULE_8___default().footerLink),\n                                            children: \"Help\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\connectplusapps website\\\\admin.oceansoulsparkles.com.au\\\\oceansoulsparkles-admin\\\\components\\\\admin\\\\AdminLayout.tsx\",\n                                            lineNumber: 115,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_3___default()), {\n                                            href: \"/admin/privacy\",\n                                            className: (_styles_admin_AdminLayout_module_css__WEBPACK_IMPORTED_MODULE_8___default().footerLink),\n                                            children: \"Privacy\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\connectplusapps website\\\\admin.oceansoulsparkles.com.au\\\\oceansoulsparkles-admin\\\\components\\\\admin\\\\AdminLayout.tsx\",\n                                            lineNumber: 118,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_3___default()), {\n                                            href: \"/admin/terms\",\n                                            className: (_styles_admin_AdminLayout_module_css__WEBPACK_IMPORTED_MODULE_8___default().footerLink),\n                                            children: \"Terms\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\connectplusapps website\\\\admin.oceansoulsparkles.com.au\\\\oceansoulsparkles-admin\\\\components\\\\admin\\\\AdminLayout.tsx\",\n                                            lineNumber: 121,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\connectplusapps website\\\\admin.oceansoulsparkles.com.au\\\\oceansoulsparkles-admin\\\\components\\\\admin\\\\AdminLayout.tsx\",\n                                    lineNumber: 114,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\connectplusapps website\\\\admin.oceansoulsparkles.com.au\\\\oceansoulsparkles-admin\\\\components\\\\admin\\\\AdminLayout.tsx\",\n                            lineNumber: 109,\n                            columnNumber: 11\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\connectplusapps website\\\\admin.oceansoulsparkles.com.au\\\\oceansoulsparkles-admin\\\\components\\\\admin\\\\AdminLayout.tsx\",\n                        lineNumber: 108,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\connectplusapps website\\\\admin.oceansoulsparkles.com.au\\\\oceansoulsparkles-admin\\\\components\\\\admin\\\\AdminLayout.tsx\",\n                lineNumber: 93,\n                columnNumber: 7\n            }, this),\n            isMobile && !sidebarCollapsed && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: (_styles_admin_AdminLayout_module_css__WEBPACK_IMPORTED_MODULE_8___default().mobileOverlay),\n                onClick: ()=>setSidebarCollapsed(true)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\connectplusapps website\\\\admin.oceansoulsparkles.com.au\\\\oceansoulsparkles-admin\\\\components\\\\admin\\\\AdminLayout.tsx\",\n                lineNumber: 131,\n                columnNumber: 9\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: (_styles_admin_AdminLayout_module_css__WEBPACK_IMPORTED_MODULE_8___default().securityBanner),\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: (_styles_admin_AdminLayout_module_css__WEBPACK_IMPORTED_MODULE_8___default().securityIcon),\n                        children: \"\\uD83D\\uDD12\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\connectplusapps website\\\\admin.oceansoulsparkles.com.au\\\\oceansoulsparkles-admin\\\\components\\\\admin\\\\AdminLayout.tsx\",\n                        lineNumber: 139,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                        children: \"Secure Admin Portal - All actions are logged and monitored\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\connectplusapps website\\\\admin.oceansoulsparkles.com.au\\\\oceansoulsparkles-admin\\\\components\\\\admin\\\\AdminLayout.tsx\",\n                        lineNumber: 140,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\connectplusapps website\\\\admin.oceansoulsparkles.com.au\\\\oceansoulsparkles-admin\\\\components\\\\admin\\\\AdminLayout.tsx\",\n                lineNumber: 138,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\connectplusapps website\\\\admin.oceansoulsparkles.com.au\\\\oceansoulsparkles-admin\\\\components\\\\admin\\\\AdminLayout.tsx\",\n        lineNumber: 83,\n        columnNumber: 5\n    }, this);\n}\n\n__webpack_async_result__();\n} catch(e) { __webpack_async_result__(e); } });//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiLi9jb21wb25lbnRzL2FkbWluL0FkbWluTGF5b3V0LnRzeCIsIm1hcHBpbmdzIjoiOzs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7O0FBQTRDO0FBQ0o7QUFDWDtBQUNVO0FBQ0c7QUFDRjtBQUNNO0FBQ2lCO0FBTWhELFNBQVNTLFlBQVksRUFBRUMsUUFBUSxFQUFvQjtJQUNoRSxNQUFNQyxTQUFTVCxzREFBU0E7SUFDeEIsTUFBTSxFQUFFVSxJQUFJLEVBQUVDLE9BQU8sRUFBRSxHQUFHTix1REFBT0E7SUFDakMsTUFBTSxDQUFDTyxrQkFBa0JDLG9CQUFvQixHQUFHZiwrQ0FBUUEsQ0FBQztJQUN6RCxNQUFNLENBQUNnQixVQUFVQyxZQUFZLEdBQUdqQiwrQ0FBUUEsQ0FBQztJQUV6Q0MsZ0RBQVNBLENBQUM7UUFDUixrQkFBa0I7UUFDbEIsTUFBTWlCLGNBQWM7WUFDbEJELFlBQVlFLE9BQU9DLFVBQVUsR0FBRztZQUNoQyxJQUFJRCxPQUFPQyxVQUFVLEdBQUcsS0FBSztnQkFDM0JMLG9CQUFvQjtZQUN0QjtRQUNGO1FBRUFHO1FBQ0FDLE9BQU9FLGdCQUFnQixDQUFDLFVBQVVIO1FBQ2xDLE9BQU8sSUFBTUMsT0FBT0csbUJBQW1CLENBQUMsVUFBVUo7SUFDcEQsR0FBRyxFQUFFO0lBRUxqQixnREFBU0EsQ0FBQztRQUNSLHlDQUF5QztRQUN6QyxJQUFJLENBQUNZLFdBQVcsQ0FBQ0QsTUFBTTtZQUNyQkQsT0FBT1ksSUFBSSxDQUFDO1FBQ2Q7SUFDRixHQUFHO1FBQUNYO1FBQU1DO1FBQVNGO0tBQU87SUFFMUIsTUFBTWEsZUFBZTtRQUNuQixJQUFJO1lBQ0YsTUFBTUMsV0FBVyxNQUFNQyxNQUFNLG9CQUFvQjtnQkFDL0NDLFFBQVE7Z0JBQ1JDLFNBQVM7b0JBQ1AsaUJBQWlCLENBQUMsT0FBTyxFQUFFQyxhQUFhQyxPQUFPLENBQUMsZUFBZSxDQUFDO2dCQUNsRTtZQUNGO1lBRUEsSUFBSUwsU0FBU00sRUFBRSxFQUFFO2dCQUNmRixhQUFhRyxVQUFVLENBQUM7Z0JBQ3hCNUIsaURBQUtBLENBQUM2QixPQUFPLENBQUM7Z0JBQ2R0QixPQUFPWSxJQUFJLENBQUM7WUFDZCxPQUFPO2dCQUNMLE1BQU0sSUFBSVcsTUFBTTtZQUNsQjtRQUNGLEVBQUUsT0FBT0MsT0FBTztZQUNkQyxRQUFRRCxLQUFLLENBQUMsaUJBQWlCQTtZQUMvQixzQ0FBc0M7WUFDdENOLGFBQWFHLFVBQVUsQ0FBQztZQUN4QnJCLE9BQU9ZLElBQUksQ0FBQztRQUNkO0lBQ0Y7SUFFQSxNQUFNYyxnQkFBZ0I7UUFDcEJ0QixvQkFBb0IsQ0FBQ0Q7SUFDdkI7SUFFQSxJQUFJRCxTQUFTO1FBQ1gscUJBQ0UsOERBQUN5QjtZQUFJQyxXQUFXL0IsOEZBQXVCOzs4QkFDckMsOERBQUM4QjtvQkFBSUMsV0FBVy9CLDRGQUFxQjs7Ozs7OzhCQUNyQyw4REFBQ2tDOzhCQUFFOzs7Ozs7Ozs7Ozs7SUFHVDtJQUVBLElBQUksQ0FBQzlCLE1BQU07UUFDVCxPQUFPLE1BQU0seUJBQXlCO0lBQ3hDO0lBRUEscUJBQ0UsOERBQUMwQjtRQUFJQyxXQUFXL0IseUZBQWtCOzswQkFFaEMsOERBQUNILHFEQUFZQTtnQkFDWE8sTUFBTUE7Z0JBQ05nQyxXQUFXOUI7Z0JBQ1grQixVQUFVUjtnQkFDVnJCLFVBQVVBOzs7Ozs7MEJBSVosOERBQUNzQjtnQkFBSUMsV0FBVyxDQUFDLEVBQUUvQix5RkFBa0IsQ0FBQyxDQUFDLEVBQUVNLG1CQUFtQk4sOEZBQXVCLEdBQUcsR0FBRyxDQUFDOztrQ0FFeEYsOERBQUNGLG9EQUFXQTt3QkFDVk0sTUFBTUE7d0JBQ05tQyxVQUFVdkI7d0JBQ1Z3QixpQkFBaUJYO3dCQUNqQnZCLGtCQUFrQkE7Ozs7OztrQ0FJcEIsOERBQUNtQzt3QkFBS1YsV0FBVy9CLHlGQUFrQjtrQ0FDaENFOzs7Ozs7a0NBSUgsOERBQUN5Qzt3QkFBT1osV0FBVy9CLHlGQUFrQjtrQ0FDbkMsNEVBQUM4Qjs0QkFBSUMsV0FBVy9CLDJGQUFvQjs7OENBQ2xDLDhEQUFDOEI7b0NBQUlDLFdBQVcvQix3RkFBaUI7O3NEQUMvQiw4REFBQytDO3NEQUFLOzs7Ozs7c0RBQ04sOERBQUNBOzRDQUFLaEIsV0FBVy9CLHFGQUFjO3NEQUFFOzs7Ozs7Ozs7Ozs7OENBRW5DLDhEQUFDOEI7b0NBQUlDLFdBQVcvQix5RkFBa0I7O3NEQUNoQyw4REFBQ0wsa0RBQUlBOzRDQUFDdUQsTUFBSzs0Q0FBY25CLFdBQVcvQix3RkFBaUI7c0RBQUU7Ozs7OztzREFHdkQsOERBQUNMLGtEQUFJQTs0Q0FBQ3VELE1BQUs7NENBQWlCbkIsV0FBVy9CLHdGQUFpQjtzREFBRTs7Ozs7O3NEQUcxRCw4REFBQ0wsa0RBQUlBOzRDQUFDdUQsTUFBSzs0Q0FBZW5CLFdBQVcvQix3RkFBaUI7c0RBQUU7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7O1lBUy9EUSxZQUFZLENBQUNGLGtDQUNaLDhEQUFDd0I7Z0JBQ0NDLFdBQVcvQiwyRkFBb0I7Z0JBQy9CcUQsU0FBUyxJQUFNOUMsb0JBQW9COzs7Ozs7MEJBS3ZDLDhEQUFDdUI7Z0JBQUlDLFdBQVcvQiw0RkFBcUI7O2tDQUNuQyw4REFBQzhCO3dCQUFJQyxXQUFXL0IsMEZBQW1CO2tDQUFFOzs7Ozs7a0NBQ3JDLDhEQUFDK0M7a0NBQUs7Ozs7Ozs7Ozs7Ozs7Ozs7OztBQUlkIiwic291cmNlcyI6WyJ3ZWJwYWNrOi8vb2NlYW5zb3Vsc3BhcmtsZXMtYWRtaW4vLi9jb21wb25lbnRzL2FkbWluL0FkbWluTGF5b3V0LnRzeD8zNDU5Il0sInNvdXJjZXNDb250ZW50IjpbImltcG9ydCB7IHVzZVN0YXRlLCB1c2VFZmZlY3QgfSBmcm9tICdyZWFjdCc7XHJcbmltcG9ydCB7IHVzZVJvdXRlciB9IGZyb20gJ25leHQvcm91dGVyJztcclxuaW1wb3J0IExpbmsgZnJvbSAnbmV4dC9saW5rJztcclxuaW1wb3J0IHsgdG9hc3QgfSBmcm9tICdyZWFjdC10b2FzdGlmeSc7XHJcbmltcG9ydCBBZG1pblNpZGViYXIgZnJvbSAnLi9BZG1pblNpZGViYXInO1xyXG5pbXBvcnQgQWRtaW5IZWFkZXIgZnJvbSAnLi9BZG1pbkhlYWRlcic7XHJcbmltcG9ydCB7IHVzZUF1dGggfSBmcm9tICcuLi8uLi9ob29rcy91c2VBdXRoJztcclxuaW1wb3J0IHN0eWxlcyBmcm9tICcuLi8uLi9zdHlsZXMvYWRtaW4vQWRtaW5MYXlvdXQubW9kdWxlLmNzcyc7XHJcblxyXG5pbnRlcmZhY2UgQWRtaW5MYXlvdXRQcm9wcyB7XHJcbiAgY2hpbGRyZW46IFJlYWN0LlJlYWN0Tm9kZTtcclxufVxyXG5cclxuZXhwb3J0IGRlZmF1bHQgZnVuY3Rpb24gQWRtaW5MYXlvdXQoeyBjaGlsZHJlbiB9OiBBZG1pbkxheW91dFByb3BzKSB7XHJcbiAgY29uc3Qgcm91dGVyID0gdXNlUm91dGVyKCk7XHJcbiAgY29uc3QgeyB1c2VyLCBsb2FkaW5nIH0gPSB1c2VBdXRoKCk7XHJcbiAgY29uc3QgW3NpZGViYXJDb2xsYXBzZWQsIHNldFNpZGViYXJDb2xsYXBzZWRdID0gdXNlU3RhdGUoZmFsc2UpO1xyXG4gIGNvbnN0IFtpc01vYmlsZSwgc2V0SXNNb2JpbGVdID0gdXNlU3RhdGUoZmFsc2UpO1xyXG5cclxuICB1c2VFZmZlY3QoKCkgPT4ge1xyXG4gICAgLy8gQ2hlY2sgaWYgbW9iaWxlXHJcbiAgICBjb25zdCBjaGVja01vYmlsZSA9ICgpID0+IHtcclxuICAgICAgc2V0SXNNb2JpbGUod2luZG93LmlubmVyV2lkdGggPCA3NjgpO1xyXG4gICAgICBpZiAod2luZG93LmlubmVyV2lkdGggPCA3NjgpIHtcclxuICAgICAgICBzZXRTaWRlYmFyQ29sbGFwc2VkKHRydWUpO1xyXG4gICAgICB9XHJcbiAgICB9O1xyXG5cclxuICAgIGNoZWNrTW9iaWxlKCk7XHJcbiAgICB3aW5kb3cuYWRkRXZlbnRMaXN0ZW5lcigncmVzaXplJywgY2hlY2tNb2JpbGUpO1xyXG4gICAgcmV0dXJuICgpID0+IHdpbmRvdy5yZW1vdmVFdmVudExpc3RlbmVyKCdyZXNpemUnLCBjaGVja01vYmlsZSk7XHJcbiAgfSwgW10pO1xyXG5cclxuICB1c2VFZmZlY3QoKCkgPT4ge1xyXG4gICAgLy8gUmVkaXJlY3QgdG8gbG9naW4gaWYgbm90IGF1dGhlbnRpY2F0ZWRcclxuICAgIGlmICghbG9hZGluZyAmJiAhdXNlcikge1xyXG4gICAgICByb3V0ZXIucHVzaCgnL2FkbWluL2xvZ2luJyk7XHJcbiAgICB9XHJcbiAgfSwgW3VzZXIsIGxvYWRpbmcsIHJvdXRlcl0pO1xyXG5cclxuICBjb25zdCBoYW5kbGVMb2dvdXQgPSBhc3luYyAoKSA9PiB7XHJcbiAgICB0cnkge1xyXG4gICAgICBjb25zdCByZXNwb25zZSA9IGF3YWl0IGZldGNoKCcvYXBpL2F1dGgvbG9nb3V0Jywge1xyXG4gICAgICAgIG1ldGhvZDogJ1BPU1QnLFxyXG4gICAgICAgIGhlYWRlcnM6IHtcclxuICAgICAgICAgICdBdXRob3JpemF0aW9uJzogYEJlYXJlciAke2xvY2FsU3RvcmFnZS5nZXRJdGVtKCdhZG1pbi10b2tlbicpfWBcclxuICAgICAgICB9XHJcbiAgICAgIH0pO1xyXG5cclxuICAgICAgaWYgKHJlc3BvbnNlLm9rKSB7XHJcbiAgICAgICAgbG9jYWxTdG9yYWdlLnJlbW92ZUl0ZW0oJ2FkbWluLXRva2VuJyk7XHJcbiAgICAgICAgdG9hc3Quc3VjY2VzcygnTG9nZ2VkIG91dCBzdWNjZXNzZnVsbHknKTtcclxuICAgICAgICByb3V0ZXIucHVzaCgnL2FkbWluL2xvZ2luJyk7XHJcbiAgICAgIH0gZWxzZSB7XHJcbiAgICAgICAgdGhyb3cgbmV3IEVycm9yKCdMb2dvdXQgZmFpbGVkJyk7XHJcbiAgICAgIH1cclxuICAgIH0gY2F0Y2ggKGVycm9yKSB7XHJcbiAgICAgIGNvbnNvbGUuZXJyb3IoJ0xvZ291dCBlcnJvcjonLCBlcnJvcik7XHJcbiAgICAgIC8vIEZvcmNlIGxvZ291dCBldmVuIGlmIEFQSSBjYWxsIGZhaWxzXHJcbiAgICAgIGxvY2FsU3RvcmFnZS5yZW1vdmVJdGVtKCdhZG1pbi10b2tlbicpO1xyXG4gICAgICByb3V0ZXIucHVzaCgnL2FkbWluL2xvZ2luJyk7XHJcbiAgICB9XHJcbiAgfTtcclxuXHJcbiAgY29uc3QgdG9nZ2xlU2lkZWJhciA9ICgpID0+IHtcclxuICAgIHNldFNpZGViYXJDb2xsYXBzZWQoIXNpZGViYXJDb2xsYXBzZWQpO1xyXG4gIH07XHJcblxyXG4gIGlmIChsb2FkaW5nKSB7XHJcbiAgICByZXR1cm4gKFxyXG4gICAgICA8ZGl2IGNsYXNzTmFtZT17c3R5bGVzLmxvYWRpbmdDb250YWluZXJ9PlxyXG4gICAgICAgIDxkaXYgY2xhc3NOYW1lPXtzdHlsZXMubG9hZGluZ1NwaW5uZXJ9PjwvZGl2PlxyXG4gICAgICAgIDxwPkxvYWRpbmcgYWRtaW4gcG9ydGFsLi4uPC9wPlxyXG4gICAgICA8L2Rpdj5cclxuICAgICk7XHJcbiAgfVxyXG5cclxuICBpZiAoIXVzZXIpIHtcclxuICAgIHJldHVybiBudWxsOyAvLyBXaWxsIHJlZGlyZWN0IHRvIGxvZ2luXHJcbiAgfVxyXG5cclxuICByZXR1cm4gKFxyXG4gICAgPGRpdiBjbGFzc05hbWU9e3N0eWxlcy5hZG1pbkxheW91dH0+XHJcbiAgICAgIHsvKiBTaWRlYmFyICovfVxyXG4gICAgICA8QWRtaW5TaWRlYmFyXHJcbiAgICAgICAgdXNlcj17dXNlcn1cclxuICAgICAgICBjb2xsYXBzZWQ9e3NpZGViYXJDb2xsYXBzZWR9XHJcbiAgICAgICAgb25Ub2dnbGU9e3RvZ2dsZVNpZGViYXJ9XHJcbiAgICAgICAgaXNNb2JpbGU9e2lzTW9iaWxlfVxyXG4gICAgICAvPlxyXG5cclxuICAgICAgey8qIE1haW4gQ29udGVudCBBcmVhICovfVxyXG4gICAgICA8ZGl2IGNsYXNzTmFtZT17YCR7c3R5bGVzLm1haW5Db250ZW50fSAke3NpZGViYXJDb2xsYXBzZWQgPyBzdHlsZXMuc2lkZWJhckNvbGxhcHNlZCA6ICcnfWB9PlxyXG4gICAgICAgIHsvKiBIZWFkZXIgKi99XHJcbiAgICAgICAgPEFkbWluSGVhZGVyXHJcbiAgICAgICAgICB1c2VyPXt1c2VyfVxyXG4gICAgICAgICAgb25Mb2dvdXQ9e2hhbmRsZUxvZ291dH1cclxuICAgICAgICAgIG9uVG9nZ2xlU2lkZWJhcj17dG9nZ2xlU2lkZWJhcn1cclxuICAgICAgICAgIHNpZGViYXJDb2xsYXBzZWQ9e3NpZGViYXJDb2xsYXBzZWR9XHJcbiAgICAgICAgLz5cclxuXHJcbiAgICAgICAgey8qIFBhZ2UgQ29udGVudCAqL31cclxuICAgICAgICA8bWFpbiBjbGFzc05hbWU9e3N0eWxlcy5wYWdlQ29udGVudH0+XHJcbiAgICAgICAgICB7Y2hpbGRyZW59XHJcbiAgICAgICAgPC9tYWluPlxyXG5cclxuICAgICAgICB7LyogRm9vdGVyICovfVxyXG4gICAgICAgIDxmb290ZXIgY2xhc3NOYW1lPXtzdHlsZXMuYWRtaW5Gb290ZXJ9PlxyXG4gICAgICAgICAgPGRpdiBjbGFzc05hbWU9e3N0eWxlcy5mb290ZXJDb250ZW50fT5cclxuICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9e3N0eWxlcy5mb290ZXJMZWZ0fT5cclxuICAgICAgICAgICAgICA8c3Bhbj7CqSAyMDI0IE9jZWFuIFNvdWwgU3BhcmtsZXMgQWRtaW4gUG9ydGFsPC9zcGFuPlxyXG4gICAgICAgICAgICAgIDxzcGFuIGNsYXNzTmFtZT17c3R5bGVzLnZlcnNpb259PnYxLjAuMDwvc3Bhbj5cclxuICAgICAgICAgICAgPC9kaXY+XHJcbiAgICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPXtzdHlsZXMuZm9vdGVyUmlnaHR9PlxyXG4gICAgICAgICAgICAgIDxMaW5rIGhyZWY9XCIvYWRtaW4vaGVscFwiIGNsYXNzTmFtZT17c3R5bGVzLmZvb3Rlckxpbmt9PlxyXG4gICAgICAgICAgICAgICAgSGVscFxyXG4gICAgICAgICAgICAgIDwvTGluaz5cclxuICAgICAgICAgICAgICA8TGluayBocmVmPVwiL2FkbWluL3ByaXZhY3lcIiBjbGFzc05hbWU9e3N0eWxlcy5mb290ZXJMaW5rfT5cclxuICAgICAgICAgICAgICAgIFByaXZhY3lcclxuICAgICAgICAgICAgICA8L0xpbms+XHJcbiAgICAgICAgICAgICAgPExpbmsgaHJlZj1cIi9hZG1pbi90ZXJtc1wiIGNsYXNzTmFtZT17c3R5bGVzLmZvb3Rlckxpbmt9PlxyXG4gICAgICAgICAgICAgICAgVGVybXNcclxuICAgICAgICAgICAgICA8L0xpbms+XHJcbiAgICAgICAgICAgIDwvZGl2PlxyXG4gICAgICAgICAgPC9kaXY+XHJcbiAgICAgICAgPC9mb290ZXI+XHJcbiAgICAgIDwvZGl2PlxyXG5cclxuICAgICAgey8qIE1vYmlsZSBPdmVybGF5ICovfVxyXG4gICAgICB7aXNNb2JpbGUgJiYgIXNpZGViYXJDb2xsYXBzZWQgJiYgKFxyXG4gICAgICAgIDxkaXYgXHJcbiAgICAgICAgICBjbGFzc05hbWU9e3N0eWxlcy5tb2JpbGVPdmVybGF5fVxyXG4gICAgICAgICAgb25DbGljaz17KCkgPT4gc2V0U2lkZWJhckNvbGxhcHNlZCh0cnVlKX1cclxuICAgICAgICAvPlxyXG4gICAgICApfVxyXG5cclxuICAgICAgey8qIFNlY3VyaXR5IE5vdGljZSAqL31cclxuICAgICAgPGRpdiBjbGFzc05hbWU9e3N0eWxlcy5zZWN1cml0eUJhbm5lcn0+XHJcbiAgICAgICAgPGRpdiBjbGFzc05hbWU9e3N0eWxlcy5zZWN1cml0eUljb259PvCflJI8L2Rpdj5cclxuICAgICAgICA8c3Bhbj5TZWN1cmUgQWRtaW4gUG9ydGFsIC0gQWxsIGFjdGlvbnMgYXJlIGxvZ2dlZCBhbmQgbW9uaXRvcmVkPC9zcGFuPlxyXG4gICAgICA8L2Rpdj5cclxuICAgIDwvZGl2PlxyXG4gICk7XHJcbn1cclxuIl0sIm5hbWVzIjpbInVzZVN0YXRlIiwidXNlRWZmZWN0IiwidXNlUm91dGVyIiwiTGluayIsInRvYXN0IiwiQWRtaW5TaWRlYmFyIiwiQWRtaW5IZWFkZXIiLCJ1c2VBdXRoIiwic3R5bGVzIiwiQWRtaW5MYXlvdXQiLCJjaGlsZHJlbiIsInJvdXRlciIsInVzZXIiLCJsb2FkaW5nIiwic2lkZWJhckNvbGxhcHNlZCIsInNldFNpZGViYXJDb2xsYXBzZWQiLCJpc01vYmlsZSIsInNldElzTW9iaWxlIiwiY2hlY2tNb2JpbGUiLCJ3aW5kb3ciLCJpbm5lcldpZHRoIiwiYWRkRXZlbnRMaXN0ZW5lciIsInJlbW92ZUV2ZW50TGlzdGVuZXIiLCJwdXNoIiwiaGFuZGxlTG9nb3V0IiwicmVzcG9uc2UiLCJmZXRjaCIsIm1ldGhvZCIsImhlYWRlcnMiLCJsb2NhbFN0b3JhZ2UiLCJnZXRJdGVtIiwib2siLCJyZW1vdmVJdGVtIiwic3VjY2VzcyIsIkVycm9yIiwiZXJyb3IiLCJjb25zb2xlIiwidG9nZ2xlU2lkZWJhciIsImRpdiIsImNsYXNzTmFtZSIsImxvYWRpbmdDb250YWluZXIiLCJsb2FkaW5nU3Bpbm5lciIsInAiLCJhZG1pbkxheW91dCIsImNvbGxhcHNlZCIsIm9uVG9nZ2xlIiwibWFpbkNvbnRlbnQiLCJvbkxvZ291dCIsIm9uVG9nZ2xlU2lkZWJhciIsIm1haW4iLCJwYWdlQ29udGVudCIsImZvb3RlciIsImFkbWluRm9vdGVyIiwiZm9vdGVyQ29udGVudCIsImZvb3RlckxlZnQiLCJzcGFuIiwidmVyc2lvbiIsImZvb3RlclJpZ2h0IiwiaHJlZiIsImZvb3RlckxpbmsiLCJtb2JpbGVPdmVybGF5Iiwib25DbGljayIsInNlY3VyaXR5QmFubmVyIiwic2VjdXJpdHlJY29uIl0sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///./components/admin/AdminLayout.tsx\n");

/***/ }),

/***/ "./components/admin/AdminSidebar.tsx":
/*!*******************************************!*\
  !*** ./components/admin/AdminSidebar.tsx ***!
  \*******************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ AdminSidebar)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"react/jsx-dev-runtime\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"react\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var next_link__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/link */ \"./node_modules/next/link.js\");\n/* harmony import */ var next_link__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(next_link__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var next_router__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! next/router */ \"./node_modules/next/router.js\");\n/* harmony import */ var next_router__WEBPACK_IMPORTED_MODULE_3___default = /*#__PURE__*/__webpack_require__.n(next_router__WEBPACK_IMPORTED_MODULE_3__);\n/* harmony import */ var _styles_admin_AdminSidebar_module_css__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! ../../styles/admin/AdminSidebar.module.css */ \"./styles/admin/AdminSidebar.module.css\");\n/* harmony import */ var _styles_admin_AdminSidebar_module_css__WEBPACK_IMPORTED_MODULE_4___default = /*#__PURE__*/__webpack_require__.n(_styles_admin_AdminSidebar_module_css__WEBPACK_IMPORTED_MODULE_4__);\n\n\n\n\n\nconst MENU_ITEMS = [\n    {\n        id: \"dashboard\",\n        label: \"Dashboard\",\n        icon: \"\\uD83D\\uDCCA\",\n        href: \"/admin/dashboard\",\n        roles: [\n            \"DEV\",\n            \"Admin\",\n            \"Artist\",\n            \"Braider\"\n        ]\n    },\n    {\n        id: \"bookings\",\n        label: \"Bookings\",\n        icon: \"\\uD83D\\uDCC5\",\n        href: \"/admin/bookings\",\n        roles: [\n            \"DEV\",\n            \"Admin\",\n            \"Artist\",\n            \"Braider\"\n        ]\n    },\n    {\n        id: \"customers\",\n        label: \"Customers\",\n        icon: \"\\uD83D\\uDC65\",\n        href: \"/admin/customers\",\n        roles: [\n            \"DEV\",\n            \"Admin\"\n        ]\n    },\n    {\n        id: \"services\",\n        label: \"Services\",\n        icon: \"✨\",\n        href: \"/admin/services\",\n        roles: [\n            \"DEV\",\n            \"Admin\"\n        ]\n    },\n    {\n        id: \"products\",\n        label: \"Products\",\n        icon: \"\\uD83D\\uDECD️\",\n        href: \"/admin/products\",\n        roles: [\n            \"DEV\",\n            \"Admin\"\n        ]\n    },\n    {\n        id: \"staff\",\n        label: \"Staff Management\",\n        icon: \"\\uD83D\\uDC68‍\\uD83D\\uDCBC\",\n        href: \"/admin/staff\",\n        roles: [\n            \"DEV\",\n            \"Admin\"\n        ],\n        children: [\n            {\n                id: \"staff-overview\",\n                label: \"Staff Overview\",\n                icon: \"\\uD83D\\uDC65\",\n                href: \"/admin/staff\",\n                roles: [\n                    \"DEV\",\n                    \"Admin\"\n                ]\n            },\n            {\n                id: \"staff-onboarding\",\n                label: \"Onboarding\",\n                icon: \"\\uD83D\\uDCCB\",\n                href: \"/admin/staff/onboarding\",\n                roles: [\n                    \"DEV\",\n                    \"Admin\"\n                ]\n            },\n            {\n                id: \"staff-training\",\n                label: \"Training\",\n                icon: \"\\uD83C\\uDF93\",\n                href: \"/admin/staff/training\",\n                roles: [\n                    \"DEV\",\n                    \"Admin\"\n                ]\n            },\n            {\n                id: \"staff-performance\",\n                label: \"Performance\",\n                icon: \"\\uD83D\\uDCCA\",\n                href: \"/admin/staff/performance\",\n                roles: [\n                    \"DEV\",\n                    \"Admin\"\n                ]\n            }\n        ]\n    },\n    {\n        id: \"artists\",\n        label: \"Artists\",\n        icon: \"\\uD83C\\uDFA8\",\n        href: \"/admin/artists\",\n        roles: [\n            \"DEV\",\n            \"Admin\"\n        ]\n    },\n    {\n        id: \"tips\",\n        label: \"Tip Management\",\n        icon: \"\\uD83D\\uDCB0\",\n        href: \"/admin/tips\",\n        roles: [\n            \"DEV\",\n            \"Admin\"\n        ]\n    },\n    {\n        id: \"receipts\",\n        label: \"Receipts\",\n        icon: \"\\uD83E\\uDDFE\",\n        href: \"/admin/receipts\",\n        roles: [\n            \"DEV\",\n            \"Admin\"\n        ]\n    },\n    {\n        id: \"reports\",\n        label: \"Reports\",\n        icon: \"\\uD83D\\uDCC8\",\n        href: \"/admin/reports\",\n        roles: [\n            \"DEV\",\n            \"Admin\"\n        ]\n    },\n    {\n        id: \"communications\",\n        label: \"Communications\",\n        icon: \"\\uD83D\\uDCE7\",\n        href: \"/admin/communications\",\n        roles: [\n            \"DEV\",\n            \"Admin\"\n        ],\n        children: [\n            {\n                id: \"email-templates\",\n                label: \"Email Templates\",\n                icon: \"\\uD83D\\uDCDD\",\n                href: \"/admin/email-templates\",\n                roles: [\n                    \"DEV\",\n                    \"Admin\"\n                ]\n            },\n            {\n                id: \"communications-log\",\n                label: \"Communications Log\",\n                icon: \"\\uD83D\\uDCCB\",\n                href: \"/admin/communications\",\n                roles: [\n                    \"DEV\",\n                    \"Admin\"\n                ]\n            },\n            {\n                id: \"feedback\",\n                label: \"Customer Feedback\",\n                icon: \"⭐\",\n                href: \"/admin/feedback\",\n                roles: [\n                    \"DEV\",\n                    \"Admin\"\n                ]\n            }\n        ]\n    },\n    {\n        id: \"notifications\",\n        label: \"Notifications\",\n        icon: \"\\uD83D\\uDD14\",\n        href: \"/admin/notifications\",\n        roles: [\n            \"DEV\",\n            \"Admin\"\n        ]\n    },\n    {\n        id: \"settings\",\n        label: \"Settings\",\n        icon: \"⚙️\",\n        href: \"/admin/settings\",\n        roles: [\n            \"DEV\",\n            \"Admin\"\n        ]\n    }\n];\nfunction AdminSidebar({ user, collapsed, onToggle, isMobile }) {\n    const router = (0,next_router__WEBPACK_IMPORTED_MODULE_3__.useRouter)();\n    const [expandedItems, setExpandedItems] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const toggleExpanded = (itemId)=>{\n        setExpandedItems((prev)=>prev.includes(itemId) ? prev.filter((id)=>id !== itemId) : [\n                ...prev,\n                itemId\n            ]);\n    };\n    const hasAccess = (roles)=>{\n        return roles.includes(user.role);\n    };\n    const isActive = (href)=>{\n        return router.pathname === href || router.pathname.startsWith(href + \"/\");\n    };\n    const filteredMenuItems = MENU_ITEMS.filter((item)=>hasAccess(item.roles));\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"aside\", {\n        className: `${(_styles_admin_AdminSidebar_module_css__WEBPACK_IMPORTED_MODULE_4___default().sidebar)} ${collapsed ? (_styles_admin_AdminSidebar_module_css__WEBPACK_IMPORTED_MODULE_4___default().collapsed) : \"\"} ${isMobile ? (_styles_admin_AdminSidebar_module_css__WEBPACK_IMPORTED_MODULE_4___default().mobile) : \"\"}`,\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: (_styles_admin_AdminSidebar_module_css__WEBPACK_IMPORTED_MODULE_4___default().sidebarHeader),\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: (_styles_admin_AdminSidebar_module_css__WEBPACK_IMPORTED_MODULE_4___default().logo),\n                        children: [\n                            !collapsed && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: (_styles_admin_AdminSidebar_module_css__WEBPACK_IMPORTED_MODULE_4___default().logoIcon),\n                                        children: \"\\uD83C\\uDF0A\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\connectplusapps website\\\\admin.oceansoulsparkles.com.au\\\\oceansoulsparkles-admin\\\\components\\\\admin\\\\AdminSidebar.tsx\",\n                                        lineNumber: 204,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: (_styles_admin_AdminSidebar_module_css__WEBPACK_IMPORTED_MODULE_4___default().logoText),\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: (_styles_admin_AdminSidebar_module_css__WEBPACK_IMPORTED_MODULE_4___default().logoTitle),\n                                                children: \"Ocean Soul\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\connectplusapps website\\\\admin.oceansoulsparkles.com.au\\\\oceansoulsparkles-admin\\\\components\\\\admin\\\\AdminSidebar.tsx\",\n                                                lineNumber: 206,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: (_styles_admin_AdminSidebar_module_css__WEBPACK_IMPORTED_MODULE_4___default().logoSubtitle),\n                                                children: \"Admin\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\connectplusapps website\\\\admin.oceansoulsparkles.com.au\\\\oceansoulsparkles-admin\\\\components\\\\admin\\\\AdminSidebar.tsx\",\n                                                lineNumber: 207,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\connectplusapps website\\\\admin.oceansoulsparkles.com.au\\\\oceansoulsparkles-admin\\\\components\\\\admin\\\\AdminSidebar.tsx\",\n                                        lineNumber: 205,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true),\n                            collapsed && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: (_styles_admin_AdminSidebar_module_css__WEBPACK_IMPORTED_MODULE_4___default().logoIconOnly),\n                                children: \"\\uD83C\\uDF0A\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\connectplusapps website\\\\admin.oceansoulsparkles.com.au\\\\oceansoulsparkles-admin\\\\components\\\\admin\\\\AdminSidebar.tsx\",\n                                lineNumber: 212,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\connectplusapps website\\\\admin.oceansoulsparkles.com.au\\\\oceansoulsparkles-admin\\\\components\\\\admin\\\\AdminSidebar.tsx\",\n                        lineNumber: 201,\n                        columnNumber: 9\n                    }, this),\n                    !isMobile && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                        className: (_styles_admin_AdminSidebar_module_css__WEBPACK_IMPORTED_MODULE_4___default().toggleButton),\n                        onClick: onToggle,\n                        title: collapsed ? \"Expand sidebar\" : \"Collapse sidebar\",\n                        children: collapsed ? \"→\" : \"←\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\connectplusapps website\\\\admin.oceansoulsparkles.com.au\\\\oceansoulsparkles-admin\\\\components\\\\admin\\\\AdminSidebar.tsx\",\n                        lineNumber: 217,\n                        columnNumber: 11\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\connectplusapps website\\\\admin.oceansoulsparkles.com.au\\\\oceansoulsparkles-admin\\\\components\\\\admin\\\\AdminSidebar.tsx\",\n                lineNumber: 200,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: (_styles_admin_AdminSidebar_module_css__WEBPACK_IMPORTED_MODULE_4___default().userInfo),\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: (_styles_admin_AdminSidebar_module_css__WEBPACK_IMPORTED_MODULE_4___default().userAvatar),\n                        children: [\n                            user.firstName.charAt(0),\n                            user.lastName.charAt(0)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\connectplusapps website\\\\admin.oceansoulsparkles.com.au\\\\oceansoulsparkles-admin\\\\components\\\\admin\\\\AdminSidebar.tsx\",\n                        lineNumber: 229,\n                        columnNumber: 9\n                    }, this),\n                    !collapsed && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: (_styles_admin_AdminSidebar_module_css__WEBPACK_IMPORTED_MODULE_4___default().userDetails),\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: (_styles_admin_AdminSidebar_module_css__WEBPACK_IMPORTED_MODULE_4___default().userName),\n                                children: [\n                                    user.firstName,\n                                    \" \",\n                                    user.lastName\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\connectplusapps website\\\\admin.oceansoulsparkles.com.au\\\\oceansoulsparkles-admin\\\\components\\\\admin\\\\AdminSidebar.tsx\",\n                                lineNumber: 234,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: (_styles_admin_AdminSidebar_module_css__WEBPACK_IMPORTED_MODULE_4___default().userRole),\n                                children: user.role\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\connectplusapps website\\\\admin.oceansoulsparkles.com.au\\\\oceansoulsparkles-admin\\\\components\\\\admin\\\\AdminSidebar.tsx\",\n                                lineNumber: 237,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\connectplusapps website\\\\admin.oceansoulsparkles.com.au\\\\oceansoulsparkles-admin\\\\components\\\\admin\\\\AdminSidebar.tsx\",\n                        lineNumber: 233,\n                        columnNumber: 11\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\connectplusapps website\\\\admin.oceansoulsparkles.com.au\\\\oceansoulsparkles-admin\\\\components\\\\admin\\\\AdminSidebar.tsx\",\n                lineNumber: 228,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"nav\", {\n                className: (_styles_admin_AdminSidebar_module_css__WEBPACK_IMPORTED_MODULE_4___default().navigation),\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"ul\", {\n                    className: (_styles_admin_AdminSidebar_module_css__WEBPACK_IMPORTED_MODULE_4___default().menuList),\n                    children: filteredMenuItems.map((item)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                            className: (_styles_admin_AdminSidebar_module_css__WEBPACK_IMPORTED_MODULE_4___default().menuItem),\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_2___default()), {\n                                    href: item.href,\n                                    className: `${(_styles_admin_AdminSidebar_module_css__WEBPACK_IMPORTED_MODULE_4___default().menuLink)} ${isActive(item.href) ? (_styles_admin_AdminSidebar_module_css__WEBPACK_IMPORTED_MODULE_4___default().active) : \"\"}`,\n                                    title: collapsed ? item.label : undefined,\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                            className: (_styles_admin_AdminSidebar_module_css__WEBPACK_IMPORTED_MODULE_4___default().menuIcon),\n                                            children: item.icon\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\connectplusapps website\\\\admin.oceansoulsparkles.com.au\\\\oceansoulsparkles-admin\\\\components\\\\admin\\\\AdminSidebar.tsx\",\n                                            lineNumber: 254,\n                                            columnNumber: 17\n                                        }, this),\n                                        !collapsed && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                            className: (_styles_admin_AdminSidebar_module_css__WEBPACK_IMPORTED_MODULE_4___default().menuLabel),\n                                            children: item.label\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\connectplusapps website\\\\admin.oceansoulsparkles.com.au\\\\oceansoulsparkles-admin\\\\components\\\\admin\\\\AdminSidebar.tsx\",\n                                            lineNumber: 256,\n                                            columnNumber: 19\n                                        }, this),\n                                        !collapsed && item.children && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                            className: (_styles_admin_AdminSidebar_module_css__WEBPACK_IMPORTED_MODULE_4___default().expandButton),\n                                            onClick: (e)=>{\n                                                e.preventDefault();\n                                                toggleExpanded(item.id);\n                                            },\n                                            children: expandedItems.includes(item.id) ? \"▼\" : \"▶\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\connectplusapps website\\\\admin.oceansoulsparkles.com.au\\\\oceansoulsparkles-admin\\\\components\\\\admin\\\\AdminSidebar.tsx\",\n                                            lineNumber: 259,\n                                            columnNumber: 19\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\connectplusapps website\\\\admin.oceansoulsparkles.com.au\\\\oceansoulsparkles-admin\\\\components\\\\admin\\\\AdminSidebar.tsx\",\n                                    lineNumber: 249,\n                                    columnNumber: 15\n                                }, this),\n                                !collapsed && item.children && expandedItems.includes(item.id) && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"ul\", {\n                                    className: (_styles_admin_AdminSidebar_module_css__WEBPACK_IMPORTED_MODULE_4___default().submenu),\n                                    children: item.children.filter((child)=>hasAccess(child.roles)).map((child)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                            className: (_styles_admin_AdminSidebar_module_css__WEBPACK_IMPORTED_MODULE_4___default().submenuItem),\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_2___default()), {\n                                                href: child.href,\n                                                className: `${(_styles_admin_AdminSidebar_module_css__WEBPACK_IMPORTED_MODULE_4___default().submenuLink)} ${isActive(child.href) ? (_styles_admin_AdminSidebar_module_css__WEBPACK_IMPORTED_MODULE_4___default().active) : \"\"}`,\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        className: (_styles_admin_AdminSidebar_module_css__WEBPACK_IMPORTED_MODULE_4___default().submenuIcon),\n                                                        children: child.icon\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\connectplusapps website\\\\admin.oceansoulsparkles.com.au\\\\oceansoulsparkles-admin\\\\components\\\\admin\\\\AdminSidebar.tsx\",\n                                                        lineNumber: 279,\n                                                        columnNumber: 25\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        className: (_styles_admin_AdminSidebar_module_css__WEBPACK_IMPORTED_MODULE_4___default().submenuLabel),\n                                                        children: child.label\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\connectplusapps website\\\\admin.oceansoulsparkles.com.au\\\\oceansoulsparkles-admin\\\\components\\\\admin\\\\AdminSidebar.tsx\",\n                                                        lineNumber: 280,\n                                                        columnNumber: 25\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\connectplusapps website\\\\admin.oceansoulsparkles.com.au\\\\oceansoulsparkles-admin\\\\components\\\\admin\\\\AdminSidebar.tsx\",\n                                                lineNumber: 275,\n                                                columnNumber: 23\n                                            }, this)\n                                        }, child.id, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\connectplusapps website\\\\admin.oceansoulsparkles.com.au\\\\oceansoulsparkles-admin\\\\components\\\\admin\\\\AdminSidebar.tsx\",\n                                            lineNumber: 274,\n                                            columnNumber: 21\n                                        }, this))\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\connectplusapps website\\\\admin.oceansoulsparkles.com.au\\\\oceansoulsparkles-admin\\\\components\\\\admin\\\\AdminSidebar.tsx\",\n                                    lineNumber: 272,\n                                    columnNumber: 17\n                                }, this)\n                            ]\n                        }, item.id, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\connectplusapps website\\\\admin.oceansoulsparkles.com.au\\\\oceansoulsparkles-admin\\\\components\\\\admin\\\\AdminSidebar.tsx\",\n                            lineNumber: 248,\n                            columnNumber: 13\n                        }, this))\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\connectplusapps website\\\\admin.oceansoulsparkles.com.au\\\\oceansoulsparkles-admin\\\\components\\\\admin\\\\AdminSidebar.tsx\",\n                    lineNumber: 246,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\connectplusapps website\\\\admin.oceansoulsparkles.com.au\\\\oceansoulsparkles-admin\\\\components\\\\admin\\\\AdminSidebar.tsx\",\n                lineNumber: 245,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: (_styles_admin_AdminSidebar_module_css__WEBPACK_IMPORTED_MODULE_4___default().sidebarFooter),\n                children: [\n                    !collapsed && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: (_styles_admin_AdminSidebar_module_css__WEBPACK_IMPORTED_MODULE_4___default().footerContent),\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: (_styles_admin_AdminSidebar_module_css__WEBPACK_IMPORTED_MODULE_4___default().versionInfo),\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: (_styles_admin_AdminSidebar_module_css__WEBPACK_IMPORTED_MODULE_4___default().version),\n                                    children: \"v1.0.0\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\connectplusapps website\\\\admin.oceansoulsparkles.com.au\\\\oceansoulsparkles-admin\\\\components\\\\admin\\\\AdminSidebar.tsx\",\n                                    lineNumber: 296,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: (_styles_admin_AdminSidebar_module_css__WEBPACK_IMPORTED_MODULE_4___default().environment),\n                                    children:  true ? \"DEV\" : 0\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\connectplusapps website\\\\admin.oceansoulsparkles.com.au\\\\oceansoulsparkles-admin\\\\components\\\\admin\\\\AdminSidebar.tsx\",\n                                    lineNumber: 297,\n                                    columnNumber: 15\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\connectplusapps website\\\\admin.oceansoulsparkles.com.au\\\\oceansoulsparkles-admin\\\\components\\\\admin\\\\AdminSidebar.tsx\",\n                            lineNumber: 295,\n                            columnNumber: 13\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\connectplusapps website\\\\admin.oceansoulsparkles.com.au\\\\oceansoulsparkles-admin\\\\components\\\\admin\\\\AdminSidebar.tsx\",\n                        lineNumber: 294,\n                        columnNumber: 11\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: (_styles_admin_AdminSidebar_module_css__WEBPACK_IMPORTED_MODULE_4___default().securityIndicator),\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: (_styles_admin_AdminSidebar_module_css__WEBPACK_IMPORTED_MODULE_4___default().securityIcon),\n                                children: \"\\uD83D\\uDD12\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\connectplusapps website\\\\admin.oceansoulsparkles.com.au\\\\oceansoulsparkles-admin\\\\components\\\\admin\\\\AdminSidebar.tsx\",\n                                lineNumber: 305,\n                                columnNumber: 11\n                            }, this),\n                            !collapsed && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: (_styles_admin_AdminSidebar_module_css__WEBPACK_IMPORTED_MODULE_4___default().securityText),\n                                children: \"Secure Portal\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\connectplusapps website\\\\admin.oceansoulsparkles.com.au\\\\oceansoulsparkles-admin\\\\components\\\\admin\\\\AdminSidebar.tsx\",\n                                lineNumber: 307,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\connectplusapps website\\\\admin.oceansoulsparkles.com.au\\\\oceansoulsparkles-admin\\\\components\\\\admin\\\\AdminSidebar.tsx\",\n                        lineNumber: 304,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\connectplusapps website\\\\admin.oceansoulsparkles.com.au\\\\oceansoulsparkles-admin\\\\components\\\\admin\\\\AdminSidebar.tsx\",\n                lineNumber: 292,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\connectplusapps website\\\\admin.oceansoulsparkles.com.au\\\\oceansoulsparkles-admin\\\\components\\\\admin\\\\AdminSidebar.tsx\",\n        lineNumber: 198,\n        columnNumber: 5\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///./components/admin/AdminSidebar.tsx\n");

/***/ }),

/***/ "./components/admin/pos/ReceiptCustomizer.js":
/*!***************************************************!*\
  !*** ./components/admin/pos/ReceiptCustomizer.js ***!
  \***************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ ReceiptCustomizer)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"react/jsx-dev-runtime\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"react\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _styles_admin_ReceiptCustomizer_module_css__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/styles/admin/ReceiptCustomizer.module.css */ \"./styles/admin/ReceiptCustomizer.module.css\");\n/* harmony import */ var _styles_admin_ReceiptCustomizer_module_css__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(_styles_admin_ReceiptCustomizer_module_css__WEBPACK_IMPORTED_MODULE_2__);\n\n\n\n/**\n * Receipt Customizer Component\n * Allows customization of receipt templates with live preview\n */ function ReceiptCustomizer({ onTemplateSelect, selectedTemplate, showPreview = true }) {\n    const [templates, setTemplates] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [loading, setLoading] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(true);\n    const [error, setError] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [previewData, setPreviewData] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [showCreateModal, setShowCreateModal] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [showEditModal, setShowEditModal] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [editingTemplate, setEditingTemplate] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        loadTemplates();\n        generatePreviewData();\n    }, []);\n    const loadTemplates = async ()=>{\n        try {\n            setLoading(true);\n            const response = await fetch(\"/api/admin/receipts\", {\n                headers: {\n                    \"Authorization\": `Bearer ${localStorage.getItem(\"admin-token\")}`\n                }\n            });\n            if (!response.ok) {\n                throw new Error(\"Failed to load receipt templates\");\n            }\n            const data = await response.json();\n            setTemplates(data.templates || []);\n            // Select default template if none selected\n            if (!selectedTemplate && data.templates?.length > 0) {\n                const defaultTemplate = data.templates.find((t)=>t.is_default) || data.templates[0];\n                onTemplateSelect?.(defaultTemplate);\n            }\n        } catch (err) {\n            console.error(\"Error loading templates:\", err);\n            setError(err.message);\n        } finally{\n            setLoading(false);\n        }\n    };\n    const generatePreviewData = ()=>{\n        setPreviewData({\n            receipt_number: `OSS-${Date.now()}`,\n            customer_name: \"Sarah Johnson\",\n            customer_email: \"<EMAIL>\",\n            customer_phone: \"+61 400 123 456\",\n            service_name: \"Festival Face Paint\",\n            tier_name: \"Premium\",\n            artist_name: \"Emma Wilson\",\n            start_time: new Date().toISOString(),\n            duration: 90,\n            total_amount: 120.00,\n            tip_amount: 18.00,\n            payment_method: \"Card\",\n            notes: \"Customer requested glitter accents\"\n        });\n    };\n    const handleTemplateSelect = (template)=>{\n        onTemplateSelect?.(template);\n    };\n    const handleCreateTemplate = ()=>{\n        setShowCreateModal(true);\n    };\n    const handleEditTemplate = (template)=>{\n        setEditingTemplate(template);\n        setShowEditModal(true);\n    };\n    const handleDeleteTemplate = async (template)=>{\n        if (template.is_default) {\n            alert(\"Cannot delete the default template\");\n            return;\n        }\n        if (!confirm(`Are you sure you want to delete \"${template.name}\"?`)) {\n            return;\n        }\n        try {\n            const response = await fetch(`/api/admin/receipts?id=${template.id}`, {\n                method: \"DELETE\",\n                headers: {\n                    \"Authorization\": `Bearer ${localStorage.getItem(\"admin-token\")}`\n                }\n            });\n            if (!response.ok) {\n                throw new Error(\"Failed to delete template\");\n            }\n            // Reload templates\n            await loadTemplates();\n            // If deleted template was selected, select default\n            if (selectedTemplate?.id === template.id) {\n                const defaultTemplate = templates.find((t)=>t.is_default) || templates[0];\n                onTemplateSelect?.(defaultTemplate);\n            }\n        } catch (error) {\n            console.error(\"Error deleting template:\", error);\n            alert(\"Failed to delete template. Please try again.\");\n        }\n    };\n    const handleSaveTemplate = async (templateData)=>{\n        try {\n            const url = editingTemplate ? `/api/admin/receipts?id=${editingTemplate.id}` : \"/api/admin/receipts\";\n            const method = editingTemplate ? \"PUT\" : \"POST\";\n            const response = await fetch(url, {\n                method,\n                headers: {\n                    \"Content-Type\": \"application/json\",\n                    \"Authorization\": `Bearer ${localStorage.getItem(\"admin-token\")}`\n                },\n                body: JSON.stringify(templateData)\n            });\n            if (!response.ok) {\n                throw new Error(\"Failed to save template\");\n            }\n            // Reload templates\n            await loadTemplates();\n            // Close modals\n            setShowCreateModal(false);\n            setShowEditModal(false);\n            setEditingTemplate(null);\n        } catch (error) {\n            console.error(\"Error saving template:\", error);\n            alert(\"Failed to save template. Please try again.\");\n        }\n    };\n    const getTemplateTypeIcon = (type)=>{\n        switch(type){\n            case \"compact\":\n                return \"\\uD83D\\uDCC4\";\n            case \"detailed\":\n                return \"\\uD83D\\uDCCB\";\n            default:\n                return \"\\uD83E\\uDDFE\";\n        }\n    };\n    const getTemplateTypeDescription = (type)=>{\n        switch(type){\n            case \"compact\":\n                return \"Minimal receipt with essential information only\";\n            case \"detailed\":\n                return \"Comprehensive receipt with all available details\";\n            default:\n                return \"Standard receipt with balanced information\";\n        }\n    };\n    if (loading) {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: (_styles_admin_ReceiptCustomizer_module_css__WEBPACK_IMPORTED_MODULE_2___default().loading),\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: (_styles_admin_ReceiptCustomizer_module_css__WEBPACK_IMPORTED_MODULE_2___default().loadingSpinner)\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\connectplusapps website\\\\admin.oceansoulsparkles.com.au\\\\oceansoulsparkles-admin\\\\components\\\\admin\\\\pos\\\\ReceiptCustomizer.js\",\n                    lineNumber: 171,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                    children: \"Loading receipt templates...\"\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\connectplusapps website\\\\admin.oceansoulsparkles.com.au\\\\oceansoulsparkles-admin\\\\components\\\\admin\\\\pos\\\\ReceiptCustomizer.js\",\n                    lineNumber: 172,\n                    columnNumber: 9\n                }, this)\n            ]\n        }, void 0, true, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\connectplusapps website\\\\admin.oceansoulsparkles.com.au\\\\oceansoulsparkles-admin\\\\components\\\\admin\\\\pos\\\\ReceiptCustomizer.js\",\n            lineNumber: 170,\n            columnNumber: 7\n        }, this);\n    }\n    if (error) {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: (_styles_admin_ReceiptCustomizer_module_css__WEBPACK_IMPORTED_MODULE_2___default().error),\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                    children: [\n                        \"Error loading templates: \",\n                        error\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\connectplusapps website\\\\admin.oceansoulsparkles.com.au\\\\oceansoulsparkles-admin\\\\components\\\\admin\\\\pos\\\\ReceiptCustomizer.js\",\n                    lineNumber: 180,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                    onClick: loadTemplates,\n                    className: (_styles_admin_ReceiptCustomizer_module_css__WEBPACK_IMPORTED_MODULE_2___default().retryBtn),\n                    children: \"Try Again\"\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\connectplusapps website\\\\admin.oceansoulsparkles.com.au\\\\oceansoulsparkles-admin\\\\components\\\\admin\\\\pos\\\\ReceiptCustomizer.js\",\n                    lineNumber: 181,\n                    columnNumber: 9\n                }, this)\n            ]\n        }, void 0, true, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\connectplusapps website\\\\admin.oceansoulsparkles.com.au\\\\oceansoulsparkles-admin\\\\components\\\\admin\\\\pos\\\\ReceiptCustomizer.js\",\n            lineNumber: 179,\n            columnNumber: 7\n        }, this);\n    }\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: (_styles_admin_ReceiptCustomizer_module_css__WEBPACK_IMPORTED_MODULE_2___default().receiptCustomizer),\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: (_styles_admin_ReceiptCustomizer_module_css__WEBPACK_IMPORTED_MODULE_2___default().templatesSection),\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: (_styles_admin_ReceiptCustomizer_module_css__WEBPACK_IMPORTED_MODULE_2___default().sectionHeader),\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                        children: \"Receipt Templates\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\connectplusapps website\\\\admin.oceansoulsparkles.com.au\\\\oceansoulsparkles-admin\\\\components\\\\admin\\\\pos\\\\ReceiptCustomizer.js\",\n                                        lineNumber: 193,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                        className: (_styles_admin_ReceiptCustomizer_module_css__WEBPACK_IMPORTED_MODULE_2___default().description),\n                                        children: \"Choose a receipt template for your transactions. You can customize these templates in the admin settings.\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\connectplusapps website\\\\admin.oceansoulsparkles.com.au\\\\oceansoulsparkles-admin\\\\components\\\\admin\\\\pos\\\\ReceiptCustomizer.js\",\n                                        lineNumber: 194,\n                                        columnNumber: 13\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\connectplusapps website\\\\admin.oceansoulsparkles.com.au\\\\oceansoulsparkles-admin\\\\components\\\\admin\\\\pos\\\\ReceiptCustomizer.js\",\n                                lineNumber: 192,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                onClick: handleCreateTemplate,\n                                className: (_styles_admin_ReceiptCustomizer_module_css__WEBPACK_IMPORTED_MODULE_2___default().createButton),\n                                children: \"➕ Create Template\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\connectplusapps website\\\\admin.oceansoulsparkles.com.au\\\\oceansoulsparkles-admin\\\\components\\\\admin\\\\pos\\\\ReceiptCustomizer.js\",\n                                lineNumber: 198,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\connectplusapps website\\\\admin.oceansoulsparkles.com.au\\\\oceansoulsparkles-admin\\\\components\\\\admin\\\\pos\\\\ReceiptCustomizer.js\",\n                        lineNumber: 191,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: (_styles_admin_ReceiptCustomizer_module_css__WEBPACK_IMPORTED_MODULE_2___default().templateGrid),\n                        children: templates.map((template)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: `${(_styles_admin_ReceiptCustomizer_module_css__WEBPACK_IMPORTED_MODULE_2___default().templateCard)} ${selectedTemplate?.id === template.id ? (_styles_admin_ReceiptCustomizer_module_css__WEBPACK_IMPORTED_MODULE_2___default().selected) : \"\"}`,\n                                onClick: ()=>handleTemplateSelect(template),\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: (_styles_admin_ReceiptCustomizer_module_css__WEBPACK_IMPORTED_MODULE_2___default().templateHeader),\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                className: (_styles_admin_ReceiptCustomizer_module_css__WEBPACK_IMPORTED_MODULE_2___default().templateIcon),\n                                                children: getTemplateTypeIcon(template.template_type)\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\connectplusapps website\\\\admin.oceansoulsparkles.com.au\\\\oceansoulsparkles-admin\\\\components\\\\admin\\\\pos\\\\ReceiptCustomizer.js\",\n                                                lineNumber: 216,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: (_styles_admin_ReceiptCustomizer_module_css__WEBPACK_IMPORTED_MODULE_2___default().templateInfo),\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n                                                        className: (_styles_admin_ReceiptCustomizer_module_css__WEBPACK_IMPORTED_MODULE_2___default().templateName),\n                                                        children: template.name\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\connectplusapps website\\\\admin.oceansoulsparkles.com.au\\\\oceansoulsparkles-admin\\\\components\\\\admin\\\\pos\\\\ReceiptCustomizer.js\",\n                                                        lineNumber: 220,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    template.is_default && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        className: (_styles_admin_ReceiptCustomizer_module_css__WEBPACK_IMPORTED_MODULE_2___default().defaultBadge),\n                                                        children: \"Default\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\connectplusapps website\\\\admin.oceansoulsparkles.com.au\\\\oceansoulsparkles-admin\\\\components\\\\admin\\\\pos\\\\ReceiptCustomizer.js\",\n                                                        lineNumber: 222,\n                                                        columnNumber: 21\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\connectplusapps website\\\\admin.oceansoulsparkles.com.au\\\\oceansoulsparkles-admin\\\\components\\\\admin\\\\pos\\\\ReceiptCustomizer.js\",\n                                                lineNumber: 219,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\connectplusapps website\\\\admin.oceansoulsparkles.com.au\\\\oceansoulsparkles-admin\\\\components\\\\admin\\\\pos\\\\ReceiptCustomizer.js\",\n                                        lineNumber: 215,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                        className: (_styles_admin_ReceiptCustomizer_module_css__WEBPACK_IMPORTED_MODULE_2___default().templateDescription),\n                                        children: template.description || getTemplateTypeDescription(template.template_type)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\connectplusapps website\\\\admin.oceansoulsparkles.com.au\\\\oceansoulsparkles-admin\\\\components\\\\admin\\\\pos\\\\ReceiptCustomizer.js\",\n                                        lineNumber: 227,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: (_styles_admin_ReceiptCustomizer_module_css__WEBPACK_IMPORTED_MODULE_2___default().templateFeatures),\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: (_styles_admin_ReceiptCustomizer_module_css__WEBPACK_IMPORTED_MODULE_2___default().featureList),\n                                            children: [\n                                                template.show_customer_details && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                    className: (_styles_admin_ReceiptCustomizer_module_css__WEBPACK_IMPORTED_MODULE_2___default().feature),\n                                                    children: \"\\uD83D\\uDC64 Customer Details\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\connectplusapps website\\\\admin.oceansoulsparkles.com.au\\\\oceansoulsparkles-admin\\\\components\\\\admin\\\\pos\\\\ReceiptCustomizer.js\",\n                                                    lineNumber: 234,\n                                                    columnNumber: 21\n                                                }, this),\n                                                template.show_service_details && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                    className: (_styles_admin_ReceiptCustomizer_module_css__WEBPACK_IMPORTED_MODULE_2___default().feature),\n                                                    children: \"\\uD83C\\uDFA8 Service Info\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\connectplusapps website\\\\admin.oceansoulsparkles.com.au\\\\oceansoulsparkles-admin\\\\components\\\\admin\\\\pos\\\\ReceiptCustomizer.js\",\n                                                    lineNumber: 237,\n                                                    columnNumber: 21\n                                                }, this),\n                                                template.show_artist_details && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                    className: (_styles_admin_ReceiptCustomizer_module_css__WEBPACK_IMPORTED_MODULE_2___default().feature),\n                                                    children: \"✨ Artist Info\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\connectplusapps website\\\\admin.oceansoulsparkles.com.au\\\\oceansoulsparkles-admin\\\\components\\\\admin\\\\pos\\\\ReceiptCustomizer.js\",\n                                                    lineNumber: 240,\n                                                    columnNumber: 21\n                                                }, this),\n                                                template.show_payment_details && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                    className: (_styles_admin_ReceiptCustomizer_module_css__WEBPACK_IMPORTED_MODULE_2___default().feature),\n                                                    children: \"\\uD83D\\uDCB3 Payment Details\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\connectplusapps website\\\\admin.oceansoulsparkles.com.au\\\\oceansoulsparkles-admin\\\\components\\\\admin\\\\pos\\\\ReceiptCustomizer.js\",\n                                                    lineNumber: 243,\n                                                    columnNumber: 21\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\connectplusapps website\\\\admin.oceansoulsparkles.com.au\\\\oceansoulsparkles-admin\\\\components\\\\admin\\\\pos\\\\ReceiptCustomizer.js\",\n                                            lineNumber: 232,\n                                            columnNumber: 17\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\connectplusapps website\\\\admin.oceansoulsparkles.com.au\\\\oceansoulsparkles-admin\\\\components\\\\admin\\\\pos\\\\ReceiptCustomizer.js\",\n                                        lineNumber: 231,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: (_styles_admin_ReceiptCustomizer_module_css__WEBPACK_IMPORTED_MODULE_2___default().templateMeta),\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                className: (_styles_admin_ReceiptCustomizer_module_css__WEBPACK_IMPORTED_MODULE_2___default().templateType),\n                                                children: template.template_type.charAt(0).toUpperCase() + template.template_type.slice(1)\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\connectplusapps website\\\\admin.oceansoulsparkles.com.au\\\\oceansoulsparkles-admin\\\\components\\\\admin\\\\pos\\\\ReceiptCustomizer.js\",\n                                                lineNumber: 249,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                className: (_styles_admin_ReceiptCustomizer_module_css__WEBPACK_IMPORTED_MODULE_2___default().businessName),\n                                                children: template.business_name\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\connectplusapps website\\\\admin.oceansoulsparkles.com.au\\\\oceansoulsparkles-admin\\\\components\\\\admin\\\\pos\\\\ReceiptCustomizer.js\",\n                                                lineNumber: 252,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\connectplusapps website\\\\admin.oceansoulsparkles.com.au\\\\oceansoulsparkles-admin\\\\components\\\\admin\\\\pos\\\\ReceiptCustomizer.js\",\n                                        lineNumber: 248,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: (_styles_admin_ReceiptCustomizer_module_css__WEBPACK_IMPORTED_MODULE_2___default().templateActions),\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                onClick: (e)=>{\n                                                    e.stopPropagation();\n                                                    handleEditTemplate(template);\n                                                },\n                                                className: (_styles_admin_ReceiptCustomizer_module_css__WEBPACK_IMPORTED_MODULE_2___default().actionButton),\n                                                title: \"Edit Template\",\n                                                children: \"✏️\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\connectplusapps website\\\\admin.oceansoulsparkles.com.au\\\\oceansoulsparkles-admin\\\\components\\\\admin\\\\pos\\\\ReceiptCustomizer.js\",\n                                                lineNumber: 258,\n                                                columnNumber: 17\n                                            }, this),\n                                            !template.is_default && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                onClick: (e)=>{\n                                                    e.stopPropagation();\n                                                    handleDeleteTemplate(template);\n                                                },\n                                                className: `${(_styles_admin_ReceiptCustomizer_module_css__WEBPACK_IMPORTED_MODULE_2___default().actionButton)} ${(_styles_admin_ReceiptCustomizer_module_css__WEBPACK_IMPORTED_MODULE_2___default().deleteButton)}`,\n                                                title: \"Delete Template\",\n                                                children: \"\\uD83D\\uDDD1️\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\connectplusapps website\\\\admin.oceansoulsparkles.com.au\\\\oceansoulsparkles-admin\\\\components\\\\admin\\\\pos\\\\ReceiptCustomizer.js\",\n                                                lineNumber: 269,\n                                                columnNumber: 19\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\connectplusapps website\\\\admin.oceansoulsparkles.com.au\\\\oceansoulsparkles-admin\\\\components\\\\admin\\\\pos\\\\ReceiptCustomizer.js\",\n                                        lineNumber: 257,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, template.id, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\connectplusapps website\\\\admin.oceansoulsparkles.com.au\\\\oceansoulsparkles-admin\\\\components\\\\admin\\\\pos\\\\ReceiptCustomizer.js\",\n                                lineNumber: 208,\n                                columnNumber: 13\n                            }, this))\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\connectplusapps website\\\\admin.oceansoulsparkles.com.au\\\\oceansoulsparkles-admin\\\\components\\\\admin\\\\pos\\\\ReceiptCustomizer.js\",\n                        lineNumber: 206,\n                        columnNumber: 9\n                    }, this),\n                    templates.length === 0 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: (_styles_admin_ReceiptCustomizer_module_css__WEBPACK_IMPORTED_MODULE_2___default().noTemplates),\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                children: \"No receipt templates found.\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\connectplusapps website\\\\admin.oceansoulsparkles.com.au\\\\oceansoulsparkles-admin\\\\components\\\\admin\\\\pos\\\\ReceiptCustomizer.js\",\n                                lineNumber: 287,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                children: \"Default templates will be created automatically.\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\connectplusapps website\\\\admin.oceansoulsparkles.com.au\\\\oceansoulsparkles-admin\\\\components\\\\admin\\\\pos\\\\ReceiptCustomizer.js\",\n                                lineNumber: 288,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\connectplusapps website\\\\admin.oceansoulsparkles.com.au\\\\oceansoulsparkles-admin\\\\components\\\\admin\\\\pos\\\\ReceiptCustomizer.js\",\n                        lineNumber: 286,\n                        columnNumber: 11\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\connectplusapps website\\\\admin.oceansoulsparkles.com.au\\\\oceansoulsparkles-admin\\\\components\\\\admin\\\\pos\\\\ReceiptCustomizer.js\",\n                lineNumber: 190,\n                columnNumber: 7\n            }, this),\n            showPreview && selectedTemplate && previewData && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: (_styles_admin_ReceiptCustomizer_module_css__WEBPACK_IMPORTED_MODULE_2___default().previewSection),\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                        children: \"Receipt Preview\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\connectplusapps website\\\\admin.oceansoulsparkles.com.au\\\\oceansoulsparkles-admin\\\\components\\\\admin\\\\pos\\\\ReceiptCustomizer.js\",\n                        lineNumber: 295,\n                        columnNumber: 11\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: (_styles_admin_ReceiptCustomizer_module_css__WEBPACK_IMPORTED_MODULE_2___default().previewContainer),\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(ReceiptPreview, {\n                            template: selectedTemplate,\n                            data: previewData\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\connectplusapps website\\\\admin.oceansoulsparkles.com.au\\\\oceansoulsparkles-admin\\\\components\\\\admin\\\\pos\\\\ReceiptCustomizer.js\",\n                            lineNumber: 297,\n                            columnNumber: 13\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\connectplusapps website\\\\admin.oceansoulsparkles.com.au\\\\oceansoulsparkles-admin\\\\components\\\\admin\\\\pos\\\\ReceiptCustomizer.js\",\n                        lineNumber: 296,\n                        columnNumber: 11\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\connectplusapps website\\\\admin.oceansoulsparkles.com.au\\\\oceansoulsparkles-admin\\\\components\\\\admin\\\\pos\\\\ReceiptCustomizer.js\",\n                lineNumber: 294,\n                columnNumber: 9\n            }, this),\n            (showCreateModal || showEditModal) && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(TemplateModal, {\n                template: editingTemplate,\n                onSave: handleSaveTemplate,\n                onClose: ()=>{\n                    setShowCreateModal(false);\n                    setShowEditModal(false);\n                    setEditingTemplate(null);\n                }\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\connectplusapps website\\\\admin.oceansoulsparkles.com.au\\\\oceansoulsparkles-admin\\\\components\\\\admin\\\\pos\\\\ReceiptCustomizer.js\",\n                lineNumber: 307,\n                columnNumber: 9\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\connectplusapps website\\\\admin.oceansoulsparkles.com.au\\\\oceansoulsparkles-admin\\\\components\\\\admin\\\\pos\\\\ReceiptCustomizer.js\",\n        lineNumber: 189,\n        columnNumber: 5\n    }, this);\n}\n/**\n * Receipt Preview Component\n * Shows a live preview of how the receipt will look\n */ function ReceiptPreview({ template, data }) {\n    const [previewHtml, setPreviewHtml] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"\");\n    const [loading, setLoading] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(true);\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        generatePreview();\n    }, [\n        template,\n        data\n    ]);\n    const generatePreview = async ()=>{\n        try {\n            setLoading(true);\n            // Generate preview HTML using the receipt generator\n            const response = await fetch(\"/api/admin/receipts/preview\", {\n                method: \"POST\",\n                headers: {\n                    \"Content-Type\": \"application/json\",\n                    \"Authorization\": `Bearer ${localStorage.getItem(\"admin-token\")}`\n                },\n                body: JSON.stringify({\n                    templateId: template.id,\n                    bookingData: data\n                })\n            });\n            if (!response.ok) {\n                throw new Error(\"Failed to generate preview\");\n            }\n            const result = await response.json();\n            setPreviewHtml(result.html || \"\");\n        } catch (error) {\n            console.error(\"Error generating preview:\", error);\n            setPreviewHtml(\"<p>Preview not available</p>\");\n        } finally{\n            setLoading(false);\n        }\n    };\n    if (loading) {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: (_styles_admin_ReceiptCustomizer_module_css__WEBPACK_IMPORTED_MODULE_2___default().previewLoading),\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: (_styles_admin_ReceiptCustomizer_module_css__WEBPACK_IMPORTED_MODULE_2___default().loadingSpinner)\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\connectplusapps website\\\\admin.oceansoulsparkles.com.au\\\\oceansoulsparkles-admin\\\\components\\\\admin\\\\pos\\\\ReceiptCustomizer.js\",\n                    lineNumber: 367,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                    children: \"Generating preview...\"\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\connectplusapps website\\\\admin.oceansoulsparkles.com.au\\\\oceansoulsparkles-admin\\\\components\\\\admin\\\\pos\\\\ReceiptCustomizer.js\",\n                    lineNumber: 368,\n                    columnNumber: 9\n                }, this)\n            ]\n        }, void 0, true, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\connectplusapps website\\\\admin.oceansoulsparkles.com.au\\\\oceansoulsparkles-admin\\\\components\\\\admin\\\\pos\\\\ReceiptCustomizer.js\",\n            lineNumber: 366,\n            columnNumber: 7\n        }, this);\n    }\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: (_styles_admin_ReceiptCustomizer_module_css__WEBPACK_IMPORTED_MODULE_2___default().receiptPreview),\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: (_styles_admin_ReceiptCustomizer_module_css__WEBPACK_IMPORTED_MODULE_2___default().previewHeader),\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                        className: (_styles_admin_ReceiptCustomizer_module_css__WEBPACK_IMPORTED_MODULE_2___default().previewLabel),\n                        children: \"Preview\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\connectplusapps website\\\\admin.oceansoulsparkles.com.au\\\\oceansoulsparkles-admin\\\\components\\\\admin\\\\pos\\\\ReceiptCustomizer.js\",\n                        lineNumber: 376,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                        onClick: generatePreview,\n                        className: (_styles_admin_ReceiptCustomizer_module_css__WEBPACK_IMPORTED_MODULE_2___default().refreshBtn),\n                        title: \"Refresh Preview\",\n                        children: \"\\uD83D\\uDD04\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\connectplusapps website\\\\admin.oceansoulsparkles.com.au\\\\oceansoulsparkles-admin\\\\components\\\\admin\\\\pos\\\\ReceiptCustomizer.js\",\n                        lineNumber: 377,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\connectplusapps website\\\\admin.oceansoulsparkles.com.au\\\\oceansoulsparkles-admin\\\\components\\\\admin\\\\pos\\\\ReceiptCustomizer.js\",\n                lineNumber: 375,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: (_styles_admin_ReceiptCustomizer_module_css__WEBPACK_IMPORTED_MODULE_2___default().previewContent),\n                dangerouslySetInnerHTML: {\n                    __html: previewHtml\n                }\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\connectplusapps website\\\\admin.oceansoulsparkles.com.au\\\\oceansoulsparkles-admin\\\\components\\\\admin\\\\pos\\\\ReceiptCustomizer.js\",\n                lineNumber: 385,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\connectplusapps website\\\\admin.oceansoulsparkles.com.au\\\\oceansoulsparkles-admin\\\\components\\\\admin\\\\pos\\\\ReceiptCustomizer.js\",\n        lineNumber: 374,\n        columnNumber: 5\n    }, this);\n}\n/**\n * Template Creation/Editing Modal\n */ function TemplateModal({ template, onSave, onClose }) {\n    const [formData, setFormData] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)({\n        name: template?.name || \"\",\n        description: template?.description || \"\",\n        template_type: template?.template_type || \"standard\",\n        business_name: template?.business_name || \"Ocean Soul Sparkles\",\n        business_address: template?.business_address || \"\",\n        business_phone: template?.business_phone || \"\",\n        business_email: template?.business_email || \"\",\n        business_website: template?.business_website || \"\",\n        business_abn: template?.business_abn || \"\",\n        show_logo: template?.show_logo ?? true,\n        logo_position: template?.logo_position || \"center\",\n        header_color: template?.header_color || \"#667eea\",\n        text_color: template?.text_color || \"#333333\",\n        font_family: template?.font_family || \"Arial\",\n        font_size: template?.font_size || 12,\n        show_customer_details: template?.show_customer_details ?? true,\n        show_service_details: template?.show_service_details ?? true,\n        show_artist_details: template?.show_artist_details ?? true,\n        show_payment_details: template?.show_payment_details ?? true,\n        show_booking_notes: template?.show_booking_notes ?? false,\n        show_terms_conditions: template?.show_terms_conditions ?? true,\n        footer_message: template?.footer_message || \"Thank you for choosing Ocean Soul Sparkles!\",\n        show_social_media: template?.show_social_media ?? false\n    });\n    const handleSubmit = (e)=>{\n        e.preventDefault();\n        onSave(formData);\n    };\n    const handleChange = (field, value)=>{\n        setFormData((prev)=>({\n                ...prev,\n                [field]: value\n            }));\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: (_styles_admin_ReceiptCustomizer_module_css__WEBPACK_IMPORTED_MODULE_2___default().modalOverlay),\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: (_styles_admin_ReceiptCustomizer_module_css__WEBPACK_IMPORTED_MODULE_2___default().modal),\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: (_styles_admin_ReceiptCustomizer_module_css__WEBPACK_IMPORTED_MODULE_2___default().modalHeader),\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                            children: template ? \"Edit Template\" : \"Create Template\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\connectplusapps website\\\\admin.oceansoulsparkles.com.au\\\\oceansoulsparkles-admin\\\\components\\\\admin\\\\pos\\\\ReceiptCustomizer.js\",\n                            lineNumber: 439,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                            onClick: onClose,\n                            className: (_styles_admin_ReceiptCustomizer_module_css__WEBPACK_IMPORTED_MODULE_2___default().closeButton),\n                            children: \"✕\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\connectplusapps website\\\\admin.oceansoulsparkles.com.au\\\\oceansoulsparkles-admin\\\\components\\\\admin\\\\pos\\\\ReceiptCustomizer.js\",\n                            lineNumber: 440,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\connectplusapps website\\\\admin.oceansoulsparkles.com.au\\\\oceansoulsparkles-admin\\\\components\\\\admin\\\\pos\\\\ReceiptCustomizer.js\",\n                    lineNumber: 438,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"form\", {\n                    onSubmit: handleSubmit,\n                    className: (_styles_admin_ReceiptCustomizer_module_css__WEBPACK_IMPORTED_MODULE_2___default().modalForm),\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: (_styles_admin_ReceiptCustomizer_module_css__WEBPACK_IMPORTED_MODULE_2___default().formGrid),\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: (_styles_admin_ReceiptCustomizer_module_css__WEBPACK_IMPORTED_MODULE_2___default().formSection),\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n                                            children: \"Basic Information\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\connectplusapps website\\\\admin.oceansoulsparkles.com.au\\\\oceansoulsparkles-admin\\\\components\\\\admin\\\\pos\\\\ReceiptCustomizer.js\",\n                                            lineNumber: 446,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: (_styles_admin_ReceiptCustomizer_module_css__WEBPACK_IMPORTED_MODULE_2___default().formGroup),\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                    children: \"Template Name\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\connectplusapps website\\\\admin.oceansoulsparkles.com.au\\\\oceansoulsparkles-admin\\\\components\\\\admin\\\\pos\\\\ReceiptCustomizer.js\",\n                                                    lineNumber: 448,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                    type: \"text\",\n                                                    value: formData.name,\n                                                    onChange: (e)=>handleChange(\"name\", e.target.value),\n                                                    required: true\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\connectplusapps website\\\\admin.oceansoulsparkles.com.au\\\\oceansoulsparkles-admin\\\\components\\\\admin\\\\pos\\\\ReceiptCustomizer.js\",\n                                                    lineNumber: 449,\n                                                    columnNumber: 17\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\connectplusapps website\\\\admin.oceansoulsparkles.com.au\\\\oceansoulsparkles-admin\\\\components\\\\admin\\\\pos\\\\ReceiptCustomizer.js\",\n                                            lineNumber: 447,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: (_styles_admin_ReceiptCustomizer_module_css__WEBPACK_IMPORTED_MODULE_2___default().formGroup),\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                    children: \"Description\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\connectplusapps website\\\\admin.oceansoulsparkles.com.au\\\\oceansoulsparkles-admin\\\\components\\\\admin\\\\pos\\\\ReceiptCustomizer.js\",\n                                                    lineNumber: 458,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"textarea\", {\n                                                    value: formData.description,\n                                                    onChange: (e)=>handleChange(\"description\", e.target.value),\n                                                    rows: 3\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\connectplusapps website\\\\admin.oceansoulsparkles.com.au\\\\oceansoulsparkles-admin\\\\components\\\\admin\\\\pos\\\\ReceiptCustomizer.js\",\n                                                    lineNumber: 459,\n                                                    columnNumber: 17\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\connectplusapps website\\\\admin.oceansoulsparkles.com.au\\\\oceansoulsparkles-admin\\\\components\\\\admin\\\\pos\\\\ReceiptCustomizer.js\",\n                                            lineNumber: 457,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: (_styles_admin_ReceiptCustomizer_module_css__WEBPACK_IMPORTED_MODULE_2___default().formGroup),\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                    children: \"Template Type\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\connectplusapps website\\\\admin.oceansoulsparkles.com.au\\\\oceansoulsparkles-admin\\\\components\\\\admin\\\\pos\\\\ReceiptCustomizer.js\",\n                                                    lineNumber: 467,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"select\", {\n                                                    value: formData.template_type,\n                                                    onChange: (e)=>handleChange(\"template_type\", e.target.value),\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                            value: \"standard\",\n                                                            children: \"Standard\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\connectplusapps website\\\\admin.oceansoulsparkles.com.au\\\\oceansoulsparkles-admin\\\\components\\\\admin\\\\pos\\\\ReceiptCustomizer.js\",\n                                                            lineNumber: 472,\n                                                            columnNumber: 19\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                            value: \"compact\",\n                                                            children: \"Compact\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\connectplusapps website\\\\admin.oceansoulsparkles.com.au\\\\oceansoulsparkles-admin\\\\components\\\\admin\\\\pos\\\\ReceiptCustomizer.js\",\n                                                            lineNumber: 473,\n                                                            columnNumber: 19\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                            value: \"detailed\",\n                                                            children: \"Detailed\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\connectplusapps website\\\\admin.oceansoulsparkles.com.au\\\\oceansoulsparkles-admin\\\\components\\\\admin\\\\pos\\\\ReceiptCustomizer.js\",\n                                                            lineNumber: 474,\n                                                            columnNumber: 19\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\connectplusapps website\\\\admin.oceansoulsparkles.com.au\\\\oceansoulsparkles-admin\\\\components\\\\admin\\\\pos\\\\ReceiptCustomizer.js\",\n                                                    lineNumber: 468,\n                                                    columnNumber: 17\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\connectplusapps website\\\\admin.oceansoulsparkles.com.au\\\\oceansoulsparkles-admin\\\\components\\\\admin\\\\pos\\\\ReceiptCustomizer.js\",\n                                            lineNumber: 466,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\connectplusapps website\\\\admin.oceansoulsparkles.com.au\\\\oceansoulsparkles-admin\\\\components\\\\admin\\\\pos\\\\ReceiptCustomizer.js\",\n                                    lineNumber: 445,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: (_styles_admin_ReceiptCustomizer_module_css__WEBPACK_IMPORTED_MODULE_2___default().formSection),\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n                                            children: \"Business Information\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\connectplusapps website\\\\admin.oceansoulsparkles.com.au\\\\oceansoulsparkles-admin\\\\components\\\\admin\\\\pos\\\\ReceiptCustomizer.js\",\n                                            lineNumber: 480,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: (_styles_admin_ReceiptCustomizer_module_css__WEBPACK_IMPORTED_MODULE_2___default().formGroup),\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                    children: \"Business Name\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\connectplusapps website\\\\admin.oceansoulsparkles.com.au\\\\oceansoulsparkles-admin\\\\components\\\\admin\\\\pos\\\\ReceiptCustomizer.js\",\n                                                    lineNumber: 482,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                    type: \"text\",\n                                                    value: formData.business_name,\n                                                    onChange: (e)=>handleChange(\"business_name\", e.target.value)\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\connectplusapps website\\\\admin.oceansoulsparkles.com.au\\\\oceansoulsparkles-admin\\\\components\\\\admin\\\\pos\\\\ReceiptCustomizer.js\",\n                                                    lineNumber: 483,\n                                                    columnNumber: 17\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\connectplusapps website\\\\admin.oceansoulsparkles.com.au\\\\oceansoulsparkles-admin\\\\components\\\\admin\\\\pos\\\\ReceiptCustomizer.js\",\n                                            lineNumber: 481,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: (_styles_admin_ReceiptCustomizer_module_css__WEBPACK_IMPORTED_MODULE_2___default().formGroup),\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                    children: \"Address\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\connectplusapps website\\\\admin.oceansoulsparkles.com.au\\\\oceansoulsparkles-admin\\\\components\\\\admin\\\\pos\\\\ReceiptCustomizer.js\",\n                                                    lineNumber: 491,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"textarea\", {\n                                                    value: formData.business_address,\n                                                    onChange: (e)=>handleChange(\"business_address\", e.target.value),\n                                                    rows: 2\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\connectplusapps website\\\\admin.oceansoulsparkles.com.au\\\\oceansoulsparkles-admin\\\\components\\\\admin\\\\pos\\\\ReceiptCustomizer.js\",\n                                                    lineNumber: 492,\n                                                    columnNumber: 17\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\connectplusapps website\\\\admin.oceansoulsparkles.com.au\\\\oceansoulsparkles-admin\\\\components\\\\admin\\\\pos\\\\ReceiptCustomizer.js\",\n                                            lineNumber: 490,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: (_styles_admin_ReceiptCustomizer_module_css__WEBPACK_IMPORTED_MODULE_2___default().formGroup),\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                    children: \"Phone\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\connectplusapps website\\\\admin.oceansoulsparkles.com.au\\\\oceansoulsparkles-admin\\\\components\\\\admin\\\\pos\\\\ReceiptCustomizer.js\",\n                                                    lineNumber: 500,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                    type: \"text\",\n                                                    value: formData.business_phone,\n                                                    onChange: (e)=>handleChange(\"business_phone\", e.target.value)\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\connectplusapps website\\\\admin.oceansoulsparkles.com.au\\\\oceansoulsparkles-admin\\\\components\\\\admin\\\\pos\\\\ReceiptCustomizer.js\",\n                                                    lineNumber: 501,\n                                                    columnNumber: 17\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\connectplusapps website\\\\admin.oceansoulsparkles.com.au\\\\oceansoulsparkles-admin\\\\components\\\\admin\\\\pos\\\\ReceiptCustomizer.js\",\n                                            lineNumber: 499,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: (_styles_admin_ReceiptCustomizer_module_css__WEBPACK_IMPORTED_MODULE_2___default().formGroup),\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                    children: \"Email\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\connectplusapps website\\\\admin.oceansoulsparkles.com.au\\\\oceansoulsparkles-admin\\\\components\\\\admin\\\\pos\\\\ReceiptCustomizer.js\",\n                                                    lineNumber: 509,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                    type: \"email\",\n                                                    value: formData.business_email,\n                                                    onChange: (e)=>handleChange(\"business_email\", e.target.value)\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\connectplusapps website\\\\admin.oceansoulsparkles.com.au\\\\oceansoulsparkles-admin\\\\components\\\\admin\\\\pos\\\\ReceiptCustomizer.js\",\n                                                    lineNumber: 510,\n                                                    columnNumber: 17\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\connectplusapps website\\\\admin.oceansoulsparkles.com.au\\\\oceansoulsparkles-admin\\\\components\\\\admin\\\\pos\\\\ReceiptCustomizer.js\",\n                                            lineNumber: 508,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\connectplusapps website\\\\admin.oceansoulsparkles.com.au\\\\oceansoulsparkles-admin\\\\components\\\\admin\\\\pos\\\\ReceiptCustomizer.js\",\n                                    lineNumber: 479,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: (_styles_admin_ReceiptCustomizer_module_css__WEBPACK_IMPORTED_MODULE_2___default().formSection),\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n                                            children: \"Display Options\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\connectplusapps website\\\\admin.oceansoulsparkles.com.au\\\\oceansoulsparkles-admin\\\\components\\\\admin\\\\pos\\\\ReceiptCustomizer.js\",\n                                            lineNumber: 519,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: (_styles_admin_ReceiptCustomizer_module_css__WEBPACK_IMPORTED_MODULE_2___default().checkboxGroup),\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                    className: (_styles_admin_ReceiptCustomizer_module_css__WEBPACK_IMPORTED_MODULE_2___default().checkboxLabel),\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                            type: \"checkbox\",\n                                                            checked: formData.show_customer_details,\n                                                            onChange: (e)=>handleChange(\"show_customer_details\", e.target.checked)\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\connectplusapps website\\\\admin.oceansoulsparkles.com.au\\\\oceansoulsparkles-admin\\\\components\\\\admin\\\\pos\\\\ReceiptCustomizer.js\",\n                                                            lineNumber: 522,\n                                                            columnNumber: 19\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                            children: \"Show Customer Details\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\connectplusapps website\\\\admin.oceansoulsparkles.com.au\\\\oceansoulsparkles-admin\\\\components\\\\admin\\\\pos\\\\ReceiptCustomizer.js\",\n                                                            lineNumber: 527,\n                                                            columnNumber: 19\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\connectplusapps website\\\\admin.oceansoulsparkles.com.au\\\\oceansoulsparkles-admin\\\\components\\\\admin\\\\pos\\\\ReceiptCustomizer.js\",\n                                                    lineNumber: 521,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                    className: (_styles_admin_ReceiptCustomizer_module_css__WEBPACK_IMPORTED_MODULE_2___default().checkboxLabel),\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                            type: \"checkbox\",\n                                                            checked: formData.show_service_details,\n                                                            onChange: (e)=>handleChange(\"show_service_details\", e.target.checked)\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\connectplusapps website\\\\admin.oceansoulsparkles.com.au\\\\oceansoulsparkles-admin\\\\components\\\\admin\\\\pos\\\\ReceiptCustomizer.js\",\n                                                            lineNumber: 531,\n                                                            columnNumber: 19\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                            children: \"Show Service Details\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\connectplusapps website\\\\admin.oceansoulsparkles.com.au\\\\oceansoulsparkles-admin\\\\components\\\\admin\\\\pos\\\\ReceiptCustomizer.js\",\n                                                            lineNumber: 536,\n                                                            columnNumber: 19\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\connectplusapps website\\\\admin.oceansoulsparkles.com.au\\\\oceansoulsparkles-admin\\\\components\\\\admin\\\\pos\\\\ReceiptCustomizer.js\",\n                                                    lineNumber: 530,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                    className: (_styles_admin_ReceiptCustomizer_module_css__WEBPACK_IMPORTED_MODULE_2___default().checkboxLabel),\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                            type: \"checkbox\",\n                                                            checked: formData.show_artist_details,\n                                                            onChange: (e)=>handleChange(\"show_artist_details\", e.target.checked)\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\connectplusapps website\\\\admin.oceansoulsparkles.com.au\\\\oceansoulsparkles-admin\\\\components\\\\admin\\\\pos\\\\ReceiptCustomizer.js\",\n                                                            lineNumber: 540,\n                                                            columnNumber: 19\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                            children: \"Show Artist Details\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\connectplusapps website\\\\admin.oceansoulsparkles.com.au\\\\oceansoulsparkles-admin\\\\components\\\\admin\\\\pos\\\\ReceiptCustomizer.js\",\n                                                            lineNumber: 545,\n                                                            columnNumber: 19\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\connectplusapps website\\\\admin.oceansoulsparkles.com.au\\\\oceansoulsparkles-admin\\\\components\\\\admin\\\\pos\\\\ReceiptCustomizer.js\",\n                                                    lineNumber: 539,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                    className: (_styles_admin_ReceiptCustomizer_module_css__WEBPACK_IMPORTED_MODULE_2___default().checkboxLabel),\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                            type: \"checkbox\",\n                                                            checked: formData.show_payment_details,\n                                                            onChange: (e)=>handleChange(\"show_payment_details\", e.target.checked)\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\connectplusapps website\\\\admin.oceansoulsparkles.com.au\\\\oceansoulsparkles-admin\\\\components\\\\admin\\\\pos\\\\ReceiptCustomizer.js\",\n                                                            lineNumber: 549,\n                                                            columnNumber: 19\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                            children: \"Show Payment Details\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\connectplusapps website\\\\admin.oceansoulsparkles.com.au\\\\oceansoulsparkles-admin\\\\components\\\\admin\\\\pos\\\\ReceiptCustomizer.js\",\n                                                            lineNumber: 554,\n                                                            columnNumber: 19\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\connectplusapps website\\\\admin.oceansoulsparkles.com.au\\\\oceansoulsparkles-admin\\\\components\\\\admin\\\\pos\\\\ReceiptCustomizer.js\",\n                                                    lineNumber: 548,\n                                                    columnNumber: 17\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\connectplusapps website\\\\admin.oceansoulsparkles.com.au\\\\oceansoulsparkles-admin\\\\components\\\\admin\\\\pos\\\\ReceiptCustomizer.js\",\n                                            lineNumber: 520,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\connectplusapps website\\\\admin.oceansoulsparkles.com.au\\\\oceansoulsparkles-admin\\\\components\\\\admin\\\\pos\\\\ReceiptCustomizer.js\",\n                                    lineNumber: 518,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\connectplusapps website\\\\admin.oceansoulsparkles.com.au\\\\oceansoulsparkles-admin\\\\components\\\\admin\\\\pos\\\\ReceiptCustomizer.js\",\n                            lineNumber: 444,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: (_styles_admin_ReceiptCustomizer_module_css__WEBPACK_IMPORTED_MODULE_2___default().modalActions),\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                    type: \"button\",\n                                    onClick: onClose,\n                                    className: (_styles_admin_ReceiptCustomizer_module_css__WEBPACK_IMPORTED_MODULE_2___default().cancelButton),\n                                    children: \"Cancel\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\connectplusapps website\\\\admin.oceansoulsparkles.com.au\\\\oceansoulsparkles-admin\\\\components\\\\admin\\\\pos\\\\ReceiptCustomizer.js\",\n                                    lineNumber: 561,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                    type: \"submit\",\n                                    className: (_styles_admin_ReceiptCustomizer_module_css__WEBPACK_IMPORTED_MODULE_2___default().saveButton),\n                                    children: template ? \"Update Template\" : \"Create Template\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\connectplusapps website\\\\admin.oceansoulsparkles.com.au\\\\oceansoulsparkles-admin\\\\components\\\\admin\\\\pos\\\\ReceiptCustomizer.js\",\n                                    lineNumber: 564,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\connectplusapps website\\\\admin.oceansoulsparkles.com.au\\\\oceansoulsparkles-admin\\\\components\\\\admin\\\\pos\\\\ReceiptCustomizer.js\",\n                            lineNumber: 560,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\connectplusapps website\\\\admin.oceansoulsparkles.com.au\\\\oceansoulsparkles-admin\\\\components\\\\admin\\\\pos\\\\ReceiptCustomizer.js\",\n                    lineNumber: 443,\n                    columnNumber: 9\n                }, this)\n            ]\n        }, void 0, true, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\connectplusapps website\\\\admin.oceansoulsparkles.com.au\\\\oceansoulsparkles-admin\\\\components\\\\admin\\\\pos\\\\ReceiptCustomizer.js\",\n            lineNumber: 437,\n            columnNumber: 7\n        }, this)\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\connectplusapps website\\\\admin.oceansoulsparkles.com.au\\\\oceansoulsparkles-admin\\\\components\\\\admin\\\\pos\\\\ReceiptCustomizer.js\",\n        lineNumber: 436,\n        columnNumber: 5\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiLi9jb21wb25lbnRzL2FkbWluL3Bvcy9SZWNlaXB0Q3VzdG9taXplci5qcyIsIm1hcHBpbmdzIjoiOzs7Ozs7Ozs7OztBQUEyQztBQUNxQjtBQUVoRTs7O0NBR0MsR0FDYyxTQUFTRyxrQkFBa0IsRUFBRUMsZ0JBQWdCLEVBQUVDLGdCQUFnQixFQUFFQyxjQUFjLElBQUksRUFBRTtJQUNsRyxNQUFNLENBQUNDLFdBQVdDLGFBQWEsR0FBR1IsK0NBQVFBLENBQUMsRUFBRTtJQUM3QyxNQUFNLENBQUNTLFNBQVNDLFdBQVcsR0FBR1YsK0NBQVFBLENBQUM7SUFDdkMsTUFBTSxDQUFDVyxPQUFPQyxTQUFTLEdBQUdaLCtDQUFRQSxDQUFDO0lBQ25DLE1BQU0sQ0FBQ2EsYUFBYUMsZUFBZSxHQUFHZCwrQ0FBUUEsQ0FBQztJQUMvQyxNQUFNLENBQUNlLGlCQUFpQkMsbUJBQW1CLEdBQUdoQiwrQ0FBUUEsQ0FBQztJQUN2RCxNQUFNLENBQUNpQixlQUFlQyxpQkFBaUIsR0FBR2xCLCtDQUFRQSxDQUFDO0lBQ25ELE1BQU0sQ0FBQ21CLGlCQUFpQkMsbUJBQW1CLEdBQUdwQiwrQ0FBUUEsQ0FBQztJQUV2REMsZ0RBQVNBLENBQUM7UUFDUm9CO1FBQ0FDO0lBQ0YsR0FBRyxFQUFFO0lBRUwsTUFBTUQsZ0JBQWdCO1FBQ3BCLElBQUk7WUFDRlgsV0FBVztZQUNYLE1BQU1hLFdBQVcsTUFBTUMsTUFBTSx1QkFBdUI7Z0JBQ2xEQyxTQUFTO29CQUNQLGlCQUFpQixDQUFDLE9BQU8sRUFBRUMsYUFBYUMsT0FBTyxDQUFDLGVBQWUsQ0FBQztnQkFDbEU7WUFDRjtZQUVBLElBQUksQ0FBQ0osU0FBU0ssRUFBRSxFQUFFO2dCQUNoQixNQUFNLElBQUlDLE1BQU07WUFDbEI7WUFFQSxNQUFNQyxPQUFPLE1BQU1QLFNBQVNRLElBQUk7WUFDaEN2QixhQUFhc0IsS0FBS3ZCLFNBQVMsSUFBSSxFQUFFO1lBRWpDLDJDQUEyQztZQUMzQyxJQUFJLENBQUNGLG9CQUFvQnlCLEtBQUt2QixTQUFTLEVBQUV5QixTQUFTLEdBQUc7Z0JBQ25ELE1BQU1DLGtCQUFrQkgsS0FBS3ZCLFNBQVMsQ0FBQzJCLElBQUksQ0FBQ0MsQ0FBQUEsSUFBS0EsRUFBRUMsVUFBVSxLQUFLTixLQUFLdkIsU0FBUyxDQUFDLEVBQUU7Z0JBQ25GSCxtQkFBbUI2QjtZQUNyQjtRQUNGLEVBQUUsT0FBT0ksS0FBSztZQUNaQyxRQUFRM0IsS0FBSyxDQUFDLDRCQUE0QjBCO1lBQzFDekIsU0FBU3lCLElBQUlFLE9BQU87UUFDdEIsU0FBVTtZQUNSN0IsV0FBVztRQUNiO0lBQ0Y7SUFFQSxNQUFNWSxzQkFBc0I7UUFDMUJSLGVBQWU7WUFDYjBCLGdCQUFnQixDQUFDLElBQUksRUFBRUMsS0FBS0MsR0FBRyxHQUFHLENBQUM7WUFDbkNDLGVBQWU7WUFDZkMsZ0JBQWdCO1lBQ2hCQyxnQkFBZ0I7WUFDaEJDLGNBQWM7WUFDZEMsV0FBVztZQUNYQyxhQUFhO1lBQ2JDLFlBQVksSUFBSVIsT0FBT1MsV0FBVztZQUNsQ0MsVUFBVTtZQUNWQyxjQUFjO1lBQ2RDLFlBQVk7WUFDWkMsZ0JBQWdCO1lBQ2hCQyxPQUFPO1FBQ1Q7SUFDRjtJQUVBLE1BQU1DLHVCQUF1QixDQUFDQztRQUM1QnJELG1CQUFtQnFEO0lBQ3JCO0lBRUEsTUFBTUMsdUJBQXVCO1FBQzNCMUMsbUJBQW1CO0lBQ3JCO0lBRUEsTUFBTTJDLHFCQUFxQixDQUFDRjtRQUMxQnJDLG1CQUFtQnFDO1FBQ25CdkMsaUJBQWlCO0lBQ25CO0lBRUEsTUFBTTBDLHVCQUF1QixPQUFPSDtRQUNsQyxJQUFJQSxTQUFTckIsVUFBVSxFQUFFO1lBQ3ZCeUIsTUFBTTtZQUNOO1FBQ0Y7UUFFQSxJQUFJLENBQUNDLFFBQVEsQ0FBQyxpQ0FBaUMsRUFBRUwsU0FBU00sSUFBSSxDQUFDLEVBQUUsQ0FBQyxHQUFHO1lBQ25FO1FBQ0Y7UUFFQSxJQUFJO1lBQ0YsTUFBTXhDLFdBQVcsTUFBTUMsTUFBTSxDQUFDLHVCQUF1QixFQUFFaUMsU0FBU08sRUFBRSxDQUFDLENBQUMsRUFBRTtnQkFDcEVDLFFBQVE7Z0JBQ1J4QyxTQUFTO29CQUNQLGlCQUFpQixDQUFDLE9BQU8sRUFBRUMsYUFBYUMsT0FBTyxDQUFDLGVBQWUsQ0FBQztnQkFDbEU7WUFDRjtZQUVBLElBQUksQ0FBQ0osU0FBU0ssRUFBRSxFQUFFO2dCQUNoQixNQUFNLElBQUlDLE1BQU07WUFDbEI7WUFFQSxtQkFBbUI7WUFDbkIsTUFBTVI7WUFFTixtREFBbUQ7WUFDbkQsSUFBSWhCLGtCQUFrQjJELE9BQU9QLFNBQVNPLEVBQUUsRUFBRTtnQkFDeEMsTUFBTS9CLGtCQUFrQjFCLFVBQVUyQixJQUFJLENBQUNDLENBQUFBLElBQUtBLEVBQUVDLFVBQVUsS0FBSzdCLFNBQVMsQ0FBQyxFQUFFO2dCQUN6RUgsbUJBQW1CNkI7WUFDckI7UUFDRixFQUFFLE9BQU90QixPQUFPO1lBQ2QyQixRQUFRM0IsS0FBSyxDQUFDLDRCQUE0QkE7WUFDMUNrRCxNQUFNO1FBQ1I7SUFDRjtJQUVBLE1BQU1LLHFCQUFxQixPQUFPQztRQUNoQyxJQUFJO1lBQ0YsTUFBTUMsTUFBTWpELGtCQUNSLENBQUMsdUJBQXVCLEVBQUVBLGdCQUFnQjZDLEVBQUUsQ0FBQyxDQUFDLEdBQzlDO1lBRUosTUFBTUMsU0FBUzlDLGtCQUFrQixRQUFRO1lBRXpDLE1BQU1JLFdBQVcsTUFBTUMsTUFBTTRDLEtBQUs7Z0JBQ2hDSDtnQkFDQXhDLFNBQVM7b0JBQ1AsZ0JBQWdCO29CQUNoQixpQkFBaUIsQ0FBQyxPQUFPLEVBQUVDLGFBQWFDLE9BQU8sQ0FBQyxlQUFlLENBQUM7Z0JBQ2xFO2dCQUNBMEMsTUFBTUMsS0FBS0MsU0FBUyxDQUFDSjtZQUN2QjtZQUVBLElBQUksQ0FBQzVDLFNBQVNLLEVBQUUsRUFBRTtnQkFDaEIsTUFBTSxJQUFJQyxNQUFNO1lBQ2xCO1lBRUEsbUJBQW1CO1lBQ25CLE1BQU1SO1lBRU4sZUFBZTtZQUNmTCxtQkFBbUI7WUFDbkJFLGlCQUFpQjtZQUNqQkUsbUJBQW1CO1FBQ3JCLEVBQUUsT0FBT1QsT0FBTztZQUNkMkIsUUFBUTNCLEtBQUssQ0FBQywwQkFBMEJBO1lBQ3hDa0QsTUFBTTtRQUNSO0lBQ0Y7SUFFQSxNQUFNVyxzQkFBc0IsQ0FBQ0M7UUFDM0IsT0FBUUE7WUFDTixLQUFLO2dCQUFXLE9BQU87WUFDdkIsS0FBSztnQkFBWSxPQUFPO1lBQ3hCO2dCQUFTLE9BQU87UUFDbEI7SUFDRjtJQUVBLE1BQU1DLDZCQUE2QixDQUFDRDtRQUNsQyxPQUFRQTtZQUNOLEtBQUs7Z0JBQVcsT0FBTztZQUN2QixLQUFLO2dCQUFZLE9BQU87WUFDeEI7Z0JBQVMsT0FBTztRQUNsQjtJQUNGO0lBRUEsSUFBSWhFLFNBQVM7UUFDWCxxQkFDRSw4REFBQ2tFO1lBQUlDLFdBQVcxRSwyRkFBYzs7OEJBQzVCLDhEQUFDeUU7b0JBQUlDLFdBQVcxRSxrR0FBcUI7Ozs7Ozs4QkFDckMsOERBQUM0RTs4QkFBRTs7Ozs7Ozs7Ozs7O0lBR1Q7SUFFQSxJQUFJbkUsT0FBTztRQUNULHFCQUNFLDhEQUFDZ0U7WUFBSUMsV0FBVzFFLHlGQUFZOzs4QkFDMUIsOERBQUM0RTs7d0JBQUU7d0JBQTBCbkU7Ozs7Ozs7OEJBQzdCLDhEQUFDb0U7b0JBQU9DLFNBQVMzRDtvQkFBZXVELFdBQVcxRSw0RkFBZTs4QkFBRTs7Ozs7Ozs7Ozs7O0lBS2xFO0lBRUEscUJBQ0UsOERBQUN5RTtRQUFJQyxXQUFXMUUscUdBQXdCOzswQkFDdEMsOERBQUN5RTtnQkFBSUMsV0FBVzFFLG9HQUF1Qjs7a0NBQ3JDLDhEQUFDeUU7d0JBQUlDLFdBQVcxRSxpR0FBb0I7OzBDQUNsQyw4REFBQ3lFOztrREFDQyw4REFBQ1U7a0RBQUc7Ozs7OztrREFDSiw4REFBQ1A7d0NBQUVGLFdBQVcxRSwrRkFBa0I7a0RBQUU7Ozs7Ozs7Ozs7OzswQ0FJcEMsOERBQUM2RTtnQ0FDQ0MsU0FBU3RCO2dDQUNUa0IsV0FBVzFFLGdHQUFtQjswQ0FDL0I7Ozs7Ozs7Ozs7OztrQ0FLSCw4REFBQ3lFO3dCQUFJQyxXQUFXMUUsZ0dBQW1CO2tDQUNoQ0ssVUFBVWtGLEdBQUcsQ0FBQyxDQUFDaEMseUJBQ2QsOERBQUNrQjtnQ0FFQ0MsV0FBVyxDQUFDLEVBQUUxRSxnR0FBbUIsQ0FBQyxDQUFDLEVBQ2pDRyxrQkFBa0IyRCxPQUFPUCxTQUFTTyxFQUFFLEdBQUc5RCw0RkFBZSxHQUFHLEdBQzFELENBQUM7Z0NBQ0Y4RSxTQUFTLElBQU14QixxQkFBcUJDOztrREFFcEMsOERBQUNrQjt3Q0FBSUMsV0FBVzFFLGtHQUFxQjs7MERBQ25DLDhEQUFDMkY7Z0RBQUtqQixXQUFXMUUsZ0dBQW1COzBEQUNqQ3NFLG9CQUFvQmYsU0FBU3NDLGFBQWE7Ozs7OzswREFFN0MsOERBQUNwQjtnREFBSUMsV0FBVzFFLGdHQUFtQjs7a0VBQ2pDLDhEQUFDK0Y7d0RBQUdyQixXQUFXMUUsZ0dBQW1CO2tFQUFHdUQsU0FBU00sSUFBSTs7Ozs7O29EQUNqRE4sU0FBU3JCLFVBQVUsa0JBQ2xCLDhEQUFDeUQ7d0RBQUtqQixXQUFXMUUsZ0dBQW1CO2tFQUFFOzs7Ozs7Ozs7Ozs7Ozs7Ozs7a0RBSzVDLDhEQUFDNEU7d0NBQUVGLFdBQVcxRSx1R0FBMEI7a0RBQ3JDdUQsU0FBUzZCLFdBQVcsSUFBSVosMkJBQTJCakIsU0FBU3NDLGFBQWE7Ozs7OztrREFHNUUsOERBQUNwQjt3Q0FBSUMsV0FBVzFFLG9HQUF1QjtrREFDckMsNEVBQUN5RTs0Q0FBSUMsV0FBVzFFLCtGQUFrQjs7Z0RBQy9CdUQsU0FBUzhDLHFCQUFxQixrQkFDN0IsOERBQUNWO29EQUFLakIsV0FBVzFFLDJGQUFjOzhEQUFFOzs7Ozs7Z0RBRWxDdUQsU0FBU2dELG9CQUFvQixrQkFDNUIsOERBQUNaO29EQUFLakIsV0FBVzFFLDJGQUFjOzhEQUFFOzs7Ozs7Z0RBRWxDdUQsU0FBU2lELG1CQUFtQixrQkFDM0IsOERBQUNiO29EQUFLakIsV0FBVzFFLDJGQUFjOzhEQUFFOzs7Ozs7Z0RBRWxDdUQsU0FBU2tELG9CQUFvQixrQkFDNUIsOERBQUNkO29EQUFLakIsV0FBVzFFLDJGQUFjOzhEQUFFOzs7Ozs7Ozs7Ozs7Ozs7OztrREFLdkMsOERBQUN5RTt3Q0FBSUMsV0FBVzFFLGdHQUFtQjs7MERBQ2pDLDhEQUFDMkY7Z0RBQUtqQixXQUFXMUUsZ0dBQW1COzBEQUNqQ3VELFNBQVNzQyxhQUFhLENBQUNlLE1BQU0sQ0FBQyxHQUFHQyxXQUFXLEtBQUt0RCxTQUFTc0MsYUFBYSxDQUFDaUIsS0FBSyxDQUFDOzs7Ozs7MERBRWpGLDhEQUFDbkI7Z0RBQUtqQixXQUFXMUUsZ0dBQW1COzBEQUNqQ3VELFNBQVN5RCxhQUFhOzs7Ozs7Ozs7Ozs7a0RBSTNCLDhEQUFDdkM7d0NBQUlDLFdBQVcxRSxtR0FBc0I7OzBEQUNwQyw4REFBQzZFO2dEQUNDQyxTQUFTLENBQUNvQztvREFDUkEsRUFBRUMsZUFBZTtvREFDakIxRCxtQkFBbUJGO2dEQUNyQjtnREFDQW1CLFdBQVcxRSxnR0FBbUI7Z0RBQzlCcUgsT0FBTTswREFDUDs7Ozs7OzRDQUdBLENBQUM5RCxTQUFTckIsVUFBVSxrQkFDbkIsOERBQUMyQztnREFDQ0MsU0FBUyxDQUFDb0M7b0RBQ1JBLEVBQUVDLGVBQWU7b0RBQ2pCekQscUJBQXFCSDtnREFDdkI7Z0RBQ0FtQixXQUFXLENBQUMsRUFBRTFFLGdHQUFtQixDQUFDLENBQUMsRUFBRUEsZ0dBQW1CLENBQUMsQ0FBQztnREFDMURxSCxPQUFNOzBEQUNQOzs7Ozs7Ozs7Ozs7OytCQW5FQTlELFNBQVNPLEVBQUU7Ozs7Ozs7Ozs7b0JBNEVyQnpELFVBQVV5QixNQUFNLEtBQUssbUJBQ3BCLDhEQUFDMkM7d0JBQUlDLFdBQVcxRSwrRkFBa0I7OzBDQUNoQyw4REFBQzRFOzBDQUFFOzs7Ozs7MENBQ0gsOERBQUNBOzBDQUFFOzs7Ozs7Ozs7Ozs7Ozs7Ozs7WUFLUnhFLGVBQWVELG9CQUFvQlEsNkJBQ2xDLDhEQUFDOEQ7Z0JBQUlDLFdBQVcxRSxrR0FBcUI7O2tDQUNuQyw4REFBQ21GO2tDQUFHOzs7Ozs7a0NBQ0osOERBQUNWO3dCQUFJQyxXQUFXMUUsb0dBQXVCO2tDQUNyQyw0RUFBQzBIOzRCQUNDbkUsVUFBVXBEOzRCQUNWeUIsTUFBTWpCOzs7Ozs7Ozs7Ozs7Ozs7OztZQU9aRSxDQUFBQSxtQkFBbUJFLGFBQVksbUJBQy9CLDhEQUFDNEc7Z0JBQ0NwRSxVQUFVdEM7Z0JBQ1YyRyxRQUFRNUQ7Z0JBQ1I2RCxTQUFTO29CQUNQL0csbUJBQW1CO29CQUNuQkUsaUJBQWlCO29CQUNqQkUsbUJBQW1CO2dCQUNyQjs7Ozs7Ozs7Ozs7O0FBS1Y7QUFFQTs7O0NBR0MsR0FDRCxTQUFTd0csZUFBZSxFQUFFbkUsUUFBUSxFQUFFM0IsSUFBSSxFQUFFO0lBQ3hDLE1BQU0sQ0FBQ2tHLGFBQWFDLGVBQWUsR0FBR2pJLCtDQUFRQSxDQUFDO0lBQy9DLE1BQU0sQ0FBQ1MsU0FBU0MsV0FBVyxHQUFHViwrQ0FBUUEsQ0FBQztJQUV2Q0MsZ0RBQVNBLENBQUM7UUFDUmlJO0lBQ0YsR0FBRztRQUFDekU7UUFBVTNCO0tBQUs7SUFFbkIsTUFBTW9HLGtCQUFrQjtRQUN0QixJQUFJO1lBQ0Z4SCxXQUFXO1lBRVgsb0RBQW9EO1lBQ3BELE1BQU1hLFdBQVcsTUFBTUMsTUFBTSwrQkFBK0I7Z0JBQzFEeUMsUUFBUTtnQkFDUnhDLFNBQVM7b0JBQ1AsZ0JBQWdCO29CQUNoQixpQkFBaUIsQ0FBQyxPQUFPLEVBQUVDLGFBQWFDLE9BQU8sQ0FBQyxlQUFlLENBQUM7Z0JBQ2xFO2dCQUNBMEMsTUFBTUMsS0FBS0MsU0FBUyxDQUFDO29CQUNuQjRELFlBQVkxRSxTQUFTTyxFQUFFO29CQUN2Qm9FLGFBQWF0RztnQkFDZjtZQUNGO1lBRUEsSUFBSSxDQUFDUCxTQUFTSyxFQUFFLEVBQUU7Z0JBQ2hCLE1BQU0sSUFBSUMsTUFBTTtZQUNsQjtZQUVBLE1BQU13RyxTQUFTLE1BQU05RyxTQUFTUSxJQUFJO1lBQ2xDa0csZUFBZUksT0FBT0MsSUFBSSxJQUFJO1FBQ2hDLEVBQUUsT0FBTzNILE9BQU87WUFDZDJCLFFBQVEzQixLQUFLLENBQUMsNkJBQTZCQTtZQUMzQ3NILGVBQWU7UUFDakIsU0FBVTtZQUNSdkgsV0FBVztRQUNiO0lBQ0Y7SUFFQSxJQUFJRCxTQUFTO1FBQ1gscUJBQ0UsOERBQUNrRTtZQUFJQyxXQUFXMUUsa0dBQXFCOzs4QkFDbkMsOERBQUN5RTtvQkFBSUMsV0FBVzFFLGtHQUFxQjs7Ozs7OzhCQUNyQyw4REFBQzRFOzhCQUFFOzs7Ozs7Ozs7Ozs7SUFHVDtJQUVBLHFCQUNFLDhEQUFDSDtRQUFJQyxXQUFXMUUsa0dBQXFCOzswQkFDbkMsOERBQUN5RTtnQkFBSUMsV0FBVzFFLGlHQUFvQjs7a0NBQ2xDLDhEQUFDMkY7d0JBQUtqQixXQUFXMUUsZ0dBQW1CO2tDQUFFOzs7Ozs7a0NBQ3RDLDhEQUFDNkU7d0JBQ0NDLFNBQVNrRDt3QkFDVHRELFdBQVcxRSw4RkFBaUI7d0JBQzVCcUgsT0FBTTtrQ0FDUDs7Ozs7Ozs7Ozs7OzBCQUlILDhEQUFDNUM7Z0JBQ0NDLFdBQVcxRSxrR0FBcUI7Z0JBQ2hDMkkseUJBQXlCO29CQUFFQyxRQUFRZDtnQkFBWTs7Ozs7Ozs7Ozs7O0FBSXZEO0FBRUE7O0NBRUMsR0FDRCxTQUFTSCxjQUFjLEVBQUVwRSxRQUFRLEVBQUVxRSxNQUFNLEVBQUVDLE9BQU8sRUFBRTtJQUNsRCxNQUFNLENBQUNnQixVQUFVQyxZQUFZLEdBQUdoSiwrQ0FBUUEsQ0FBQztRQUN2QytELE1BQU1OLFVBQVVNLFFBQVE7UUFDeEJ1QixhQUFhN0IsVUFBVTZCLGVBQWU7UUFDdENTLGVBQWV0QyxVQUFVc0MsaUJBQWlCO1FBQzFDbUIsZUFBZXpELFVBQVV5RCxpQkFBaUI7UUFDMUMrQixrQkFBa0J4RixVQUFVd0Ysb0JBQW9CO1FBQ2hEQyxnQkFBZ0J6RixVQUFVeUYsa0JBQWtCO1FBQzVDQyxnQkFBZ0IxRixVQUFVMEYsa0JBQWtCO1FBQzVDQyxrQkFBa0IzRixVQUFVMkYsb0JBQW9CO1FBQ2hEQyxjQUFjNUYsVUFBVTRGLGdCQUFnQjtRQUN4Q0MsV0FBVzdGLFVBQVU2RixhQUFhO1FBQ2xDQyxlQUFlOUYsVUFBVThGLGlCQUFpQjtRQUMxQ0MsY0FBYy9GLFVBQVUrRixnQkFBZ0I7UUFDeENDLFlBQVloRyxVQUFVZ0csY0FBYztRQUNwQ0MsYUFBYWpHLFVBQVVpRyxlQUFlO1FBQ3RDQyxXQUFXbEcsVUFBVWtHLGFBQWE7UUFDbENwRCx1QkFBdUI5QyxVQUFVOEMseUJBQXlCO1FBQzFERSxzQkFBc0JoRCxVQUFVZ0Qsd0JBQXdCO1FBQ3hEQyxxQkFBcUJqRCxVQUFVaUQsdUJBQXVCO1FBQ3REQyxzQkFBc0JsRCxVQUFVa0Qsd0JBQXdCO1FBQ3hEaUQsb0JBQW9CbkcsVUFBVW1HLHNCQUFzQjtRQUNwREMsdUJBQXVCcEcsVUFBVW9HLHlCQUF5QjtRQUMxREMsZ0JBQWdCckcsVUFBVXFHLGtCQUFrQjtRQUM1Q0MsbUJBQW1CdEcsVUFBVXNHLHFCQUFxQjtJQUNwRDtJQUVBLE1BQU1DLGVBQWUsQ0FBQzVDO1FBQ3BCQSxFQUFFNkMsY0FBYztRQUNoQm5DLE9BQU9pQjtJQUNUO0lBRUEsTUFBTW1CLGVBQWUsQ0FBQ0MsT0FBT0M7UUFDM0JwQixZQUFZcUIsQ0FBQUEsT0FBUztnQkFDbkIsR0FBR0EsSUFBSTtnQkFDUCxDQUFDRixNQUFNLEVBQUVDO1lBQ1g7SUFDRjtJQUVBLHFCQUNFLDhEQUFDekY7UUFBSUMsV0FBVzFFLGdHQUFtQjtrQkFDakMsNEVBQUN5RTtZQUFJQyxXQUFXMUUseUZBQVk7OzhCQUMxQiw4REFBQ3lFO29CQUFJQyxXQUFXMUUsK0ZBQWtCOztzQ0FDaEMsOERBQUNtRjtzQ0FBSTVCLFdBQVcsa0JBQWtCOzs7Ozs7c0NBQ2xDLDhEQUFDc0I7NEJBQU9DLFNBQVMrQzs0QkFBU25ELFdBQVcxRSwrRkFBa0I7c0NBQUU7Ozs7Ozs7Ozs7Ozs4QkFHM0QsOERBQUN3SztvQkFBS0MsVUFBVVg7b0JBQWNwRixXQUFXMUUsNkZBQWdCOztzQ0FDdkQsOERBQUN5RTs0QkFBSUMsV0FBVzFFLDRGQUFlOzs4Q0FDN0IsOERBQUN5RTtvQ0FBSUMsV0FBVzFFLCtGQUFrQjs7c0RBQ2hDLDhEQUFDK0Y7c0RBQUc7Ozs7OztzREFDSiw4REFBQ3RCOzRDQUFJQyxXQUFXMUUsNkZBQWdCOzs4REFDOUIsOERBQUM4Szs4REFBTTs7Ozs7OzhEQUNQLDhEQUFDQztvREFDQ3hHLE1BQUs7b0RBQ0wyRixPQUFPckIsU0FBU2hGLElBQUk7b0RBQ3BCbUgsVUFBVSxDQUFDOUQsSUFBTThDLGFBQWEsUUFBUTlDLEVBQUUrRCxNQUFNLENBQUNmLEtBQUs7b0RBQ3BEZ0IsUUFBUTs7Ozs7Ozs7Ozs7O3NEQUlaLDhEQUFDekc7NENBQUlDLFdBQVcxRSw2RkFBZ0I7OzhEQUM5Qiw4REFBQzhLOzhEQUFNOzs7Ozs7OERBQ1AsOERBQUNLO29EQUNDakIsT0FBT3JCLFNBQVN6RCxXQUFXO29EQUMzQjRGLFVBQVUsQ0FBQzlELElBQU04QyxhQUFhLGVBQWU5QyxFQUFFK0QsTUFBTSxDQUFDZixLQUFLO29EQUMzRGtCLE1BQU07Ozs7Ozs7Ozs7OztzREFJViw4REFBQzNHOzRDQUFJQyxXQUFXMUUsNkZBQWdCOzs4REFDOUIsOERBQUM4Szs4REFBTTs7Ozs7OzhEQUNQLDhEQUFDTztvREFDQ25CLE9BQU9yQixTQUFTaEQsYUFBYTtvREFDN0JtRixVQUFVLENBQUM5RCxJQUFNOEMsYUFBYSxpQkFBaUI5QyxFQUFFK0QsTUFBTSxDQUFDZixLQUFLOztzRUFFN0QsOERBQUNvQjs0REFBT3BCLE9BQU07c0VBQVc7Ozs7OztzRUFDekIsOERBQUNvQjs0REFBT3BCLE9BQU07c0VBQVU7Ozs7OztzRUFDeEIsOERBQUNvQjs0REFBT3BCLE9BQU07c0VBQVc7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs4Q0FLL0IsOERBQUN6RjtvQ0FBSUMsV0FBVzFFLCtGQUFrQjs7c0RBQ2hDLDhEQUFDK0Y7c0RBQUc7Ozs7OztzREFDSiw4REFBQ3RCOzRDQUFJQyxXQUFXMUUsNkZBQWdCOzs4REFDOUIsOERBQUM4Szs4REFBTTs7Ozs7OzhEQUNQLDhEQUFDQztvREFDQ3hHLE1BQUs7b0RBQ0wyRixPQUFPckIsU0FBUzdCLGFBQWE7b0RBQzdCZ0UsVUFBVSxDQUFDOUQsSUFBTThDLGFBQWEsaUJBQWlCOUMsRUFBRStELE1BQU0sQ0FBQ2YsS0FBSzs7Ozs7Ozs7Ozs7O3NEQUlqRSw4REFBQ3pGOzRDQUFJQyxXQUFXMUUsNkZBQWdCOzs4REFDOUIsOERBQUM4Szs4REFBTTs7Ozs7OzhEQUNQLDhEQUFDSztvREFDQ2pCLE9BQU9yQixTQUFTRSxnQkFBZ0I7b0RBQ2hDaUMsVUFBVSxDQUFDOUQsSUFBTThDLGFBQWEsb0JBQW9COUMsRUFBRStELE1BQU0sQ0FBQ2YsS0FBSztvREFDaEVrQixNQUFNOzs7Ozs7Ozs7Ozs7c0RBSVYsOERBQUMzRzs0Q0FBSUMsV0FBVzFFLDZGQUFnQjs7OERBQzlCLDhEQUFDOEs7OERBQU07Ozs7Ozs4REFDUCw4REFBQ0M7b0RBQ0N4RyxNQUFLO29EQUNMMkYsT0FBT3JCLFNBQVNHLGNBQWM7b0RBQzlCZ0MsVUFBVSxDQUFDOUQsSUFBTThDLGFBQWEsa0JBQWtCOUMsRUFBRStELE1BQU0sQ0FBQ2YsS0FBSzs7Ozs7Ozs7Ozs7O3NEQUlsRSw4REFBQ3pGOzRDQUFJQyxXQUFXMUUsNkZBQWdCOzs4REFDOUIsOERBQUM4Szs4REFBTTs7Ozs7OzhEQUNQLDhEQUFDQztvREFDQ3hHLE1BQUs7b0RBQ0wyRixPQUFPckIsU0FBU0ksY0FBYztvREFDOUIrQixVQUFVLENBQUM5RCxJQUFNOEMsYUFBYSxrQkFBa0I5QyxFQUFFK0QsTUFBTSxDQUFDZixLQUFLOzs7Ozs7Ozs7Ozs7Ozs7Ozs7OENBS3BFLDhEQUFDekY7b0NBQUlDLFdBQVcxRSwrRkFBa0I7O3NEQUNoQyw4REFBQytGO3NEQUFHOzs7Ozs7c0RBQ0osOERBQUN0Qjs0Q0FBSUMsV0FBVzFFLGlHQUFvQjs7OERBQ2xDLDhEQUFDOEs7b0RBQU1wRyxXQUFXMUUsaUdBQW9COztzRUFDcEMsOERBQUMrSzs0REFDQ3hHLE1BQUs7NERBQ0xrSCxTQUFTNUMsU0FBU3hDLHFCQUFxQjs0REFDdkMyRSxVQUFVLENBQUM5RCxJQUFNOEMsYUFBYSx5QkFBeUI5QyxFQUFFK0QsTUFBTSxDQUFDUSxPQUFPOzs7Ozs7c0VBRXpFLDhEQUFDOUY7c0VBQUs7Ozs7Ozs7Ozs7Ozs4REFHUiw4REFBQ21GO29EQUFNcEcsV0FBVzFFLGlHQUFvQjs7c0VBQ3BDLDhEQUFDK0s7NERBQ0N4RyxNQUFLOzREQUNMa0gsU0FBUzVDLFNBQVN0QyxvQkFBb0I7NERBQ3RDeUUsVUFBVSxDQUFDOUQsSUFBTThDLGFBQWEsd0JBQXdCOUMsRUFBRStELE1BQU0sQ0FBQ1EsT0FBTzs7Ozs7O3NFQUV4RSw4REFBQzlGO3NFQUFLOzs7Ozs7Ozs7Ozs7OERBR1IsOERBQUNtRjtvREFBTXBHLFdBQVcxRSxpR0FBb0I7O3NFQUNwQyw4REFBQytLOzREQUNDeEcsTUFBSzs0REFDTGtILFNBQVM1QyxTQUFTckMsbUJBQW1COzREQUNyQ3dFLFVBQVUsQ0FBQzlELElBQU04QyxhQUFhLHVCQUF1QjlDLEVBQUUrRCxNQUFNLENBQUNRLE9BQU87Ozs7OztzRUFFdkUsOERBQUM5RjtzRUFBSzs7Ozs7Ozs7Ozs7OzhEQUdSLDhEQUFDbUY7b0RBQU1wRyxXQUFXMUUsaUdBQW9COztzRUFDcEMsOERBQUMrSzs0REFDQ3hHLE1BQUs7NERBQ0xrSCxTQUFTNUMsU0FBU3BDLG9CQUFvQjs0REFDdEN1RSxVQUFVLENBQUM5RCxJQUFNOEMsYUFBYSx3QkFBd0I5QyxFQUFFK0QsTUFBTSxDQUFDUSxPQUFPOzs7Ozs7c0VBRXhFLDhEQUFDOUY7c0VBQUs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7OztzQ0FNZCw4REFBQ2xCOzRCQUFJQyxXQUFXMUUsZ0dBQW1COzs4Q0FDakMsOERBQUM2RTtvQ0FBT04sTUFBSztvQ0FBU08sU0FBUytDO29DQUFTbkQsV0FBVzFFLGdHQUFtQjs4Q0FBRTs7Ozs7OzhDQUd4RSw4REFBQzZFO29DQUFPTixNQUFLO29DQUFTRyxXQUFXMUUsOEZBQWlCOzhDQUMvQ3VELFdBQVcsb0JBQW9COzs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7OztBQU85QyIsInNvdXJjZXMiOlsid2VicGFjazovL29jZWFuc291bHNwYXJrbGVzLWFkbWluLy4vY29tcG9uZW50cy9hZG1pbi9wb3MvUmVjZWlwdEN1c3RvbWl6ZXIuanM/OWM0MSJdLCJzb3VyY2VzQ29udGVudCI6WyJpbXBvcnQgeyB1c2VTdGF0ZSwgdXNlRWZmZWN0IH0gZnJvbSAncmVhY3QnXG5pbXBvcnQgc3R5bGVzIGZyb20gJ0Avc3R5bGVzL2FkbWluL1JlY2VpcHRDdXN0b21pemVyLm1vZHVsZS5jc3MnXG5cbi8qKlxuICogUmVjZWlwdCBDdXN0b21pemVyIENvbXBvbmVudFxuICogQWxsb3dzIGN1c3RvbWl6YXRpb24gb2YgcmVjZWlwdCB0ZW1wbGF0ZXMgd2l0aCBsaXZlIHByZXZpZXdcbiAqL1xuZXhwb3J0IGRlZmF1bHQgZnVuY3Rpb24gUmVjZWlwdEN1c3RvbWl6ZXIoeyBvblRlbXBsYXRlU2VsZWN0LCBzZWxlY3RlZFRlbXBsYXRlLCBzaG93UHJldmlldyA9IHRydWUgfSkge1xuICBjb25zdCBbdGVtcGxhdGVzLCBzZXRUZW1wbGF0ZXNdID0gdXNlU3RhdGUoW10pXG4gIGNvbnN0IFtsb2FkaW5nLCBzZXRMb2FkaW5nXSA9IHVzZVN0YXRlKHRydWUpXG4gIGNvbnN0IFtlcnJvciwgc2V0RXJyb3JdID0gdXNlU3RhdGUobnVsbClcbiAgY29uc3QgW3ByZXZpZXdEYXRhLCBzZXRQcmV2aWV3RGF0YV0gPSB1c2VTdGF0ZShudWxsKVxuICBjb25zdCBbc2hvd0NyZWF0ZU1vZGFsLCBzZXRTaG93Q3JlYXRlTW9kYWxdID0gdXNlU3RhdGUoZmFsc2UpXG4gIGNvbnN0IFtzaG93RWRpdE1vZGFsLCBzZXRTaG93RWRpdE1vZGFsXSA9IHVzZVN0YXRlKGZhbHNlKVxuICBjb25zdCBbZWRpdGluZ1RlbXBsYXRlLCBzZXRFZGl0aW5nVGVtcGxhdGVdID0gdXNlU3RhdGUobnVsbClcblxuICB1c2VFZmZlY3QoKCkgPT4ge1xuICAgIGxvYWRUZW1wbGF0ZXMoKVxuICAgIGdlbmVyYXRlUHJldmlld0RhdGEoKVxuICB9LCBbXSlcblxuICBjb25zdCBsb2FkVGVtcGxhdGVzID0gYXN5bmMgKCkgPT4ge1xuICAgIHRyeSB7XG4gICAgICBzZXRMb2FkaW5nKHRydWUpXG4gICAgICBjb25zdCByZXNwb25zZSA9IGF3YWl0IGZldGNoKCcvYXBpL2FkbWluL3JlY2VpcHRzJywge1xuICAgICAgICBoZWFkZXJzOiB7XG4gICAgICAgICAgJ0F1dGhvcml6YXRpb24nOiBgQmVhcmVyICR7bG9jYWxTdG9yYWdlLmdldEl0ZW0oJ2FkbWluLXRva2VuJyl9YFxuICAgICAgICB9XG4gICAgICB9KVxuXG4gICAgICBpZiAoIXJlc3BvbnNlLm9rKSB7XG4gICAgICAgIHRocm93IG5ldyBFcnJvcignRmFpbGVkIHRvIGxvYWQgcmVjZWlwdCB0ZW1wbGF0ZXMnKVxuICAgICAgfVxuXG4gICAgICBjb25zdCBkYXRhID0gYXdhaXQgcmVzcG9uc2UuanNvbigpXG4gICAgICBzZXRUZW1wbGF0ZXMoZGF0YS50ZW1wbGF0ZXMgfHwgW10pXG4gICAgICBcbiAgICAgIC8vIFNlbGVjdCBkZWZhdWx0IHRlbXBsYXRlIGlmIG5vbmUgc2VsZWN0ZWRcbiAgICAgIGlmICghc2VsZWN0ZWRUZW1wbGF0ZSAmJiBkYXRhLnRlbXBsYXRlcz8ubGVuZ3RoID4gMCkge1xuICAgICAgICBjb25zdCBkZWZhdWx0VGVtcGxhdGUgPSBkYXRhLnRlbXBsYXRlcy5maW5kKHQgPT4gdC5pc19kZWZhdWx0KSB8fCBkYXRhLnRlbXBsYXRlc1swXVxuICAgICAgICBvblRlbXBsYXRlU2VsZWN0Py4oZGVmYXVsdFRlbXBsYXRlKVxuICAgICAgfVxuICAgIH0gY2F0Y2ggKGVycikge1xuICAgICAgY29uc29sZS5lcnJvcignRXJyb3IgbG9hZGluZyB0ZW1wbGF0ZXM6JywgZXJyKVxuICAgICAgc2V0RXJyb3IoZXJyLm1lc3NhZ2UpXG4gICAgfSBmaW5hbGx5IHtcbiAgICAgIHNldExvYWRpbmcoZmFsc2UpXG4gICAgfVxuICB9XG5cbiAgY29uc3QgZ2VuZXJhdGVQcmV2aWV3RGF0YSA9ICgpID0+IHtcbiAgICBzZXRQcmV2aWV3RGF0YSh7XG4gICAgICByZWNlaXB0X251bWJlcjogYE9TUy0ke0RhdGUubm93KCl9YCxcbiAgICAgIGN1c3RvbWVyX25hbWU6ICdTYXJhaCBKb2huc29uJyxcbiAgICAgIGN1c3RvbWVyX2VtYWlsOiAnc2FyYWhAZXhhbXBsZS5jb20nLFxuICAgICAgY3VzdG9tZXJfcGhvbmU6ICcrNjEgNDAwIDEyMyA0NTYnLFxuICAgICAgc2VydmljZV9uYW1lOiAnRmVzdGl2YWwgRmFjZSBQYWludCcsXG4gICAgICB0aWVyX25hbWU6ICdQcmVtaXVtJyxcbiAgICAgIGFydGlzdF9uYW1lOiAnRW1tYSBXaWxzb24nLFxuICAgICAgc3RhcnRfdGltZTogbmV3IERhdGUoKS50b0lTT1N0cmluZygpLFxuICAgICAgZHVyYXRpb246IDkwLFxuICAgICAgdG90YWxfYW1vdW50OiAxMjAuMDAsXG4gICAgICB0aXBfYW1vdW50OiAxOC4wMCxcbiAgICAgIHBheW1lbnRfbWV0aG9kOiAnQ2FyZCcsXG4gICAgICBub3RlczogJ0N1c3RvbWVyIHJlcXVlc3RlZCBnbGl0dGVyIGFjY2VudHMnXG4gICAgfSlcbiAgfVxuXG4gIGNvbnN0IGhhbmRsZVRlbXBsYXRlU2VsZWN0ID0gKHRlbXBsYXRlKSA9PiB7XG4gICAgb25UZW1wbGF0ZVNlbGVjdD8uKHRlbXBsYXRlKVxuICB9XG5cbiAgY29uc3QgaGFuZGxlQ3JlYXRlVGVtcGxhdGUgPSAoKSA9PiB7XG4gICAgc2V0U2hvd0NyZWF0ZU1vZGFsKHRydWUpXG4gIH1cblxuICBjb25zdCBoYW5kbGVFZGl0VGVtcGxhdGUgPSAodGVtcGxhdGUpID0+IHtcbiAgICBzZXRFZGl0aW5nVGVtcGxhdGUodGVtcGxhdGUpXG4gICAgc2V0U2hvd0VkaXRNb2RhbCh0cnVlKVxuICB9XG5cbiAgY29uc3QgaGFuZGxlRGVsZXRlVGVtcGxhdGUgPSBhc3luYyAodGVtcGxhdGUpID0+IHtcbiAgICBpZiAodGVtcGxhdGUuaXNfZGVmYXVsdCkge1xuICAgICAgYWxlcnQoJ0Nhbm5vdCBkZWxldGUgdGhlIGRlZmF1bHQgdGVtcGxhdGUnKVxuICAgICAgcmV0dXJuXG4gICAgfVxuXG4gICAgaWYgKCFjb25maXJtKGBBcmUgeW91IHN1cmUgeW91IHdhbnQgdG8gZGVsZXRlIFwiJHt0ZW1wbGF0ZS5uYW1lfVwiP2ApKSB7XG4gICAgICByZXR1cm5cbiAgICB9XG5cbiAgICB0cnkge1xuICAgICAgY29uc3QgcmVzcG9uc2UgPSBhd2FpdCBmZXRjaChgL2FwaS9hZG1pbi9yZWNlaXB0cz9pZD0ke3RlbXBsYXRlLmlkfWAsIHtcbiAgICAgICAgbWV0aG9kOiAnREVMRVRFJyxcbiAgICAgICAgaGVhZGVyczoge1xuICAgICAgICAgICdBdXRob3JpemF0aW9uJzogYEJlYXJlciAke2xvY2FsU3RvcmFnZS5nZXRJdGVtKCdhZG1pbi10b2tlbicpfWBcbiAgICAgICAgfVxuICAgICAgfSlcblxuICAgICAgaWYgKCFyZXNwb25zZS5vaykge1xuICAgICAgICB0aHJvdyBuZXcgRXJyb3IoJ0ZhaWxlZCB0byBkZWxldGUgdGVtcGxhdGUnKVxuICAgICAgfVxuXG4gICAgICAvLyBSZWxvYWQgdGVtcGxhdGVzXG4gICAgICBhd2FpdCBsb2FkVGVtcGxhdGVzKClcblxuICAgICAgLy8gSWYgZGVsZXRlZCB0ZW1wbGF0ZSB3YXMgc2VsZWN0ZWQsIHNlbGVjdCBkZWZhdWx0XG4gICAgICBpZiAoc2VsZWN0ZWRUZW1wbGF0ZT8uaWQgPT09IHRlbXBsYXRlLmlkKSB7XG4gICAgICAgIGNvbnN0IGRlZmF1bHRUZW1wbGF0ZSA9IHRlbXBsYXRlcy5maW5kKHQgPT4gdC5pc19kZWZhdWx0KSB8fCB0ZW1wbGF0ZXNbMF1cbiAgICAgICAgb25UZW1wbGF0ZVNlbGVjdD8uKGRlZmF1bHRUZW1wbGF0ZSlcbiAgICAgIH1cbiAgICB9IGNhdGNoIChlcnJvcikge1xuICAgICAgY29uc29sZS5lcnJvcignRXJyb3IgZGVsZXRpbmcgdGVtcGxhdGU6JywgZXJyb3IpXG4gICAgICBhbGVydCgnRmFpbGVkIHRvIGRlbGV0ZSB0ZW1wbGF0ZS4gUGxlYXNlIHRyeSBhZ2Fpbi4nKVxuICAgIH1cbiAgfVxuXG4gIGNvbnN0IGhhbmRsZVNhdmVUZW1wbGF0ZSA9IGFzeW5jICh0ZW1wbGF0ZURhdGEpID0+IHtcbiAgICB0cnkge1xuICAgICAgY29uc3QgdXJsID0gZWRpdGluZ1RlbXBsYXRlXG4gICAgICAgID8gYC9hcGkvYWRtaW4vcmVjZWlwdHM/aWQ9JHtlZGl0aW5nVGVtcGxhdGUuaWR9YFxuICAgICAgICA6ICcvYXBpL2FkbWluL3JlY2VpcHRzJ1xuXG4gICAgICBjb25zdCBtZXRob2QgPSBlZGl0aW5nVGVtcGxhdGUgPyAnUFVUJyA6ICdQT1NUJ1xuXG4gICAgICBjb25zdCByZXNwb25zZSA9IGF3YWl0IGZldGNoKHVybCwge1xuICAgICAgICBtZXRob2QsXG4gICAgICAgIGhlYWRlcnM6IHtcbiAgICAgICAgICAnQ29udGVudC1UeXBlJzogJ2FwcGxpY2F0aW9uL2pzb24nLFxuICAgICAgICAgICdBdXRob3JpemF0aW9uJzogYEJlYXJlciAke2xvY2FsU3RvcmFnZS5nZXRJdGVtKCdhZG1pbi10b2tlbicpfWBcbiAgICAgICAgfSxcbiAgICAgICAgYm9keTogSlNPTi5zdHJpbmdpZnkodGVtcGxhdGVEYXRhKVxuICAgICAgfSlcblxuICAgICAgaWYgKCFyZXNwb25zZS5vaykge1xuICAgICAgICB0aHJvdyBuZXcgRXJyb3IoJ0ZhaWxlZCB0byBzYXZlIHRlbXBsYXRlJylcbiAgICAgIH1cblxuICAgICAgLy8gUmVsb2FkIHRlbXBsYXRlc1xuICAgICAgYXdhaXQgbG9hZFRlbXBsYXRlcygpXG5cbiAgICAgIC8vIENsb3NlIG1vZGFsc1xuICAgICAgc2V0U2hvd0NyZWF0ZU1vZGFsKGZhbHNlKVxuICAgICAgc2V0U2hvd0VkaXRNb2RhbChmYWxzZSlcbiAgICAgIHNldEVkaXRpbmdUZW1wbGF0ZShudWxsKVxuICAgIH0gY2F0Y2ggKGVycm9yKSB7XG4gICAgICBjb25zb2xlLmVycm9yKCdFcnJvciBzYXZpbmcgdGVtcGxhdGU6JywgZXJyb3IpXG4gICAgICBhbGVydCgnRmFpbGVkIHRvIHNhdmUgdGVtcGxhdGUuIFBsZWFzZSB0cnkgYWdhaW4uJylcbiAgICB9XG4gIH1cblxuICBjb25zdCBnZXRUZW1wbGF0ZVR5cGVJY29uID0gKHR5cGUpID0+IHtcbiAgICBzd2l0Y2ggKHR5cGUpIHtcbiAgICAgIGNhc2UgJ2NvbXBhY3QnOiByZXR1cm4gJ/Cfk4QnXG4gICAgICBjYXNlICdkZXRhaWxlZCc6IHJldHVybiAn8J+TiydcbiAgICAgIGRlZmF1bHQ6IHJldHVybiAn8J+nvidcbiAgICB9XG4gIH1cblxuICBjb25zdCBnZXRUZW1wbGF0ZVR5cGVEZXNjcmlwdGlvbiA9ICh0eXBlKSA9PiB7XG4gICAgc3dpdGNoICh0eXBlKSB7XG4gICAgICBjYXNlICdjb21wYWN0JzogcmV0dXJuICdNaW5pbWFsIHJlY2VpcHQgd2l0aCBlc3NlbnRpYWwgaW5mb3JtYXRpb24gb25seSdcbiAgICAgIGNhc2UgJ2RldGFpbGVkJzogcmV0dXJuICdDb21wcmVoZW5zaXZlIHJlY2VpcHQgd2l0aCBhbGwgYXZhaWxhYmxlIGRldGFpbHMnXG4gICAgICBkZWZhdWx0OiByZXR1cm4gJ1N0YW5kYXJkIHJlY2VpcHQgd2l0aCBiYWxhbmNlZCBpbmZvcm1hdGlvbidcbiAgICB9XG4gIH1cblxuICBpZiAobG9hZGluZykge1xuICAgIHJldHVybiAoXG4gICAgICA8ZGl2IGNsYXNzTmFtZT17c3R5bGVzLmxvYWRpbmd9PlxuICAgICAgICA8ZGl2IGNsYXNzTmFtZT17c3R5bGVzLmxvYWRpbmdTcGlubmVyfT48L2Rpdj5cbiAgICAgICAgPHA+TG9hZGluZyByZWNlaXB0IHRlbXBsYXRlcy4uLjwvcD5cbiAgICAgIDwvZGl2PlxuICAgIClcbiAgfVxuXG4gIGlmIChlcnJvcikge1xuICAgIHJldHVybiAoXG4gICAgICA8ZGl2IGNsYXNzTmFtZT17c3R5bGVzLmVycm9yfT5cbiAgICAgICAgPHA+RXJyb3IgbG9hZGluZyB0ZW1wbGF0ZXM6IHtlcnJvcn08L3A+XG4gICAgICAgIDxidXR0b24gb25DbGljaz17bG9hZFRlbXBsYXRlc30gY2xhc3NOYW1lPXtzdHlsZXMucmV0cnlCdG59PlxuICAgICAgICAgIFRyeSBBZ2FpblxuICAgICAgICA8L2J1dHRvbj5cbiAgICAgIDwvZGl2PlxuICAgIClcbiAgfVxuXG4gIHJldHVybiAoXG4gICAgPGRpdiBjbGFzc05hbWU9e3N0eWxlcy5yZWNlaXB0Q3VzdG9taXplcn0+XG4gICAgICA8ZGl2IGNsYXNzTmFtZT17c3R5bGVzLnRlbXBsYXRlc1NlY3Rpb259PlxuICAgICAgICA8ZGl2IGNsYXNzTmFtZT17c3R5bGVzLnNlY3Rpb25IZWFkZXJ9PlxuICAgICAgICAgIDxkaXY+XG4gICAgICAgICAgICA8aDM+UmVjZWlwdCBUZW1wbGF0ZXM8L2gzPlxuICAgICAgICAgICAgPHAgY2xhc3NOYW1lPXtzdHlsZXMuZGVzY3JpcHRpb259PlxuICAgICAgICAgICAgICBDaG9vc2UgYSByZWNlaXB0IHRlbXBsYXRlIGZvciB5b3VyIHRyYW5zYWN0aW9ucy4gWW91IGNhbiBjdXN0b21pemUgdGhlc2UgdGVtcGxhdGVzIGluIHRoZSBhZG1pbiBzZXR0aW5ncy5cbiAgICAgICAgICAgIDwvcD5cbiAgICAgICAgICA8L2Rpdj5cbiAgICAgICAgICA8YnV0dG9uXG4gICAgICAgICAgICBvbkNsaWNrPXtoYW5kbGVDcmVhdGVUZW1wbGF0ZX1cbiAgICAgICAgICAgIGNsYXNzTmFtZT17c3R5bGVzLmNyZWF0ZUJ1dHRvbn1cbiAgICAgICAgICA+XG4gICAgICAgICAgICDinpUgQ3JlYXRlIFRlbXBsYXRlXG4gICAgICAgICAgPC9idXR0b24+XG4gICAgICAgIDwvZGl2PlxuICAgICAgICBcbiAgICAgICAgPGRpdiBjbGFzc05hbWU9e3N0eWxlcy50ZW1wbGF0ZUdyaWR9PlxuICAgICAgICAgIHt0ZW1wbGF0ZXMubWFwKCh0ZW1wbGF0ZSkgPT4gKFxuICAgICAgICAgICAgPGRpdlxuICAgICAgICAgICAgICBrZXk9e3RlbXBsYXRlLmlkfVxuICAgICAgICAgICAgICBjbGFzc05hbWU9e2Ake3N0eWxlcy50ZW1wbGF0ZUNhcmR9ICR7XG4gICAgICAgICAgICAgICAgc2VsZWN0ZWRUZW1wbGF0ZT8uaWQgPT09IHRlbXBsYXRlLmlkID8gc3R5bGVzLnNlbGVjdGVkIDogJydcbiAgICAgICAgICAgICAgfWB9XG4gICAgICAgICAgICAgIG9uQ2xpY2s9eygpID0+IGhhbmRsZVRlbXBsYXRlU2VsZWN0KHRlbXBsYXRlKX1cbiAgICAgICAgICAgID5cbiAgICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9e3N0eWxlcy50ZW1wbGF0ZUhlYWRlcn0+XG4gICAgICAgICAgICAgICAgPHNwYW4gY2xhc3NOYW1lPXtzdHlsZXMudGVtcGxhdGVJY29ufT5cbiAgICAgICAgICAgICAgICAgIHtnZXRUZW1wbGF0ZVR5cGVJY29uKHRlbXBsYXRlLnRlbXBsYXRlX3R5cGUpfVxuICAgICAgICAgICAgICAgIDwvc3Bhbj5cbiAgICAgICAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT17c3R5bGVzLnRlbXBsYXRlSW5mb30+XG4gICAgICAgICAgICAgICAgICA8aDQgY2xhc3NOYW1lPXtzdHlsZXMudGVtcGxhdGVOYW1lfT57dGVtcGxhdGUubmFtZX08L2g0PlxuICAgICAgICAgICAgICAgICAge3RlbXBsYXRlLmlzX2RlZmF1bHQgJiYgKFxuICAgICAgICAgICAgICAgICAgICA8c3BhbiBjbGFzc05hbWU9e3N0eWxlcy5kZWZhdWx0QmFkZ2V9PkRlZmF1bHQ8L3NwYW4+XG4gICAgICAgICAgICAgICAgICApfVxuICAgICAgICAgICAgICAgIDwvZGl2PlxuICAgICAgICAgICAgICA8L2Rpdj5cbiAgICAgICAgICAgICAgXG4gICAgICAgICAgICAgIDxwIGNsYXNzTmFtZT17c3R5bGVzLnRlbXBsYXRlRGVzY3JpcHRpb259PlxuICAgICAgICAgICAgICAgIHt0ZW1wbGF0ZS5kZXNjcmlwdGlvbiB8fCBnZXRUZW1wbGF0ZVR5cGVEZXNjcmlwdGlvbih0ZW1wbGF0ZS50ZW1wbGF0ZV90eXBlKX1cbiAgICAgICAgICAgICAgPC9wPlxuICAgICAgICAgICAgICBcbiAgICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9e3N0eWxlcy50ZW1wbGF0ZUZlYXR1cmVzfT5cbiAgICAgICAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT17c3R5bGVzLmZlYXR1cmVMaXN0fT5cbiAgICAgICAgICAgICAgICAgIHt0ZW1wbGF0ZS5zaG93X2N1c3RvbWVyX2RldGFpbHMgJiYgKFxuICAgICAgICAgICAgICAgICAgICA8c3BhbiBjbGFzc05hbWU9e3N0eWxlcy5mZWF0dXJlfT7wn5GkIEN1c3RvbWVyIERldGFpbHM8L3NwYW4+XG4gICAgICAgICAgICAgICAgICApfVxuICAgICAgICAgICAgICAgICAge3RlbXBsYXRlLnNob3dfc2VydmljZV9kZXRhaWxzICYmIChcbiAgICAgICAgICAgICAgICAgICAgPHNwYW4gY2xhc3NOYW1lPXtzdHlsZXMuZmVhdHVyZX0+8J+OqCBTZXJ2aWNlIEluZm88L3NwYW4+XG4gICAgICAgICAgICAgICAgICApfVxuICAgICAgICAgICAgICAgICAge3RlbXBsYXRlLnNob3dfYXJ0aXN0X2RldGFpbHMgJiYgKFxuICAgICAgICAgICAgICAgICAgICA8c3BhbiBjbGFzc05hbWU9e3N0eWxlcy5mZWF0dXJlfT7inKggQXJ0aXN0IEluZm88L3NwYW4+XG4gICAgICAgICAgICAgICAgICApfVxuICAgICAgICAgICAgICAgICAge3RlbXBsYXRlLnNob3dfcGF5bWVudF9kZXRhaWxzICYmIChcbiAgICAgICAgICAgICAgICAgICAgPHNwYW4gY2xhc3NOYW1lPXtzdHlsZXMuZmVhdHVyZX0+8J+SsyBQYXltZW50IERldGFpbHM8L3NwYW4+XG4gICAgICAgICAgICAgICAgICApfVxuICAgICAgICAgICAgICAgIDwvZGl2PlxuICAgICAgICAgICAgICA8L2Rpdj5cbiAgICAgICAgICAgICAgXG4gICAgICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPXtzdHlsZXMudGVtcGxhdGVNZXRhfT5cbiAgICAgICAgICAgICAgICA8c3BhbiBjbGFzc05hbWU9e3N0eWxlcy50ZW1wbGF0ZVR5cGV9PlxuICAgICAgICAgICAgICAgICAge3RlbXBsYXRlLnRlbXBsYXRlX3R5cGUuY2hhckF0KDApLnRvVXBwZXJDYXNlKCkgKyB0ZW1wbGF0ZS50ZW1wbGF0ZV90eXBlLnNsaWNlKDEpfVxuICAgICAgICAgICAgICAgIDwvc3Bhbj5cbiAgICAgICAgICAgICAgICA8c3BhbiBjbGFzc05hbWU9e3N0eWxlcy5idXNpbmVzc05hbWV9PlxuICAgICAgICAgICAgICAgICAge3RlbXBsYXRlLmJ1c2luZXNzX25hbWV9XG4gICAgICAgICAgICAgICAgPC9zcGFuPlxuICAgICAgICAgICAgICA8L2Rpdj5cblxuICAgICAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT17c3R5bGVzLnRlbXBsYXRlQWN0aW9uc30+XG4gICAgICAgICAgICAgICAgPGJ1dHRvblxuICAgICAgICAgICAgICAgICAgb25DbGljaz17KGUpID0+IHtcbiAgICAgICAgICAgICAgICAgICAgZS5zdG9wUHJvcGFnYXRpb24oKVxuICAgICAgICAgICAgICAgICAgICBoYW5kbGVFZGl0VGVtcGxhdGUodGVtcGxhdGUpXG4gICAgICAgICAgICAgICAgICB9fVxuICAgICAgICAgICAgICAgICAgY2xhc3NOYW1lPXtzdHlsZXMuYWN0aW9uQnV0dG9ufVxuICAgICAgICAgICAgICAgICAgdGl0bGU9XCJFZGl0IFRlbXBsYXRlXCJcbiAgICAgICAgICAgICAgICA+XG4gICAgICAgICAgICAgICAgICDinI/vuI9cbiAgICAgICAgICAgICAgICA8L2J1dHRvbj5cbiAgICAgICAgICAgICAgICB7IXRlbXBsYXRlLmlzX2RlZmF1bHQgJiYgKFxuICAgICAgICAgICAgICAgICAgPGJ1dHRvblxuICAgICAgICAgICAgICAgICAgICBvbkNsaWNrPXsoZSkgPT4ge1xuICAgICAgICAgICAgICAgICAgICAgIGUuc3RvcFByb3BhZ2F0aW9uKClcbiAgICAgICAgICAgICAgICAgICAgICBoYW5kbGVEZWxldGVUZW1wbGF0ZSh0ZW1wbGF0ZSlcbiAgICAgICAgICAgICAgICAgICAgfX1cbiAgICAgICAgICAgICAgICAgICAgY2xhc3NOYW1lPXtgJHtzdHlsZXMuYWN0aW9uQnV0dG9ufSAke3N0eWxlcy5kZWxldGVCdXR0b259YH1cbiAgICAgICAgICAgICAgICAgICAgdGl0bGU9XCJEZWxldGUgVGVtcGxhdGVcIlxuICAgICAgICAgICAgICAgICAgPlxuICAgICAgICAgICAgICAgICAgICDwn5eR77iPXG4gICAgICAgICAgICAgICAgICA8L2J1dHRvbj5cbiAgICAgICAgICAgICAgICApfVxuICAgICAgICAgICAgICA8L2Rpdj5cbiAgICAgICAgICAgIDwvZGl2PlxuICAgICAgICAgICkpfVxuICAgICAgICA8L2Rpdj5cblxuICAgICAgICB7dGVtcGxhdGVzLmxlbmd0aCA9PT0gMCAmJiAoXG4gICAgICAgICAgPGRpdiBjbGFzc05hbWU9e3N0eWxlcy5ub1RlbXBsYXRlc30+XG4gICAgICAgICAgICA8cD5ObyByZWNlaXB0IHRlbXBsYXRlcyBmb3VuZC48L3A+XG4gICAgICAgICAgICA8cD5EZWZhdWx0IHRlbXBsYXRlcyB3aWxsIGJlIGNyZWF0ZWQgYXV0b21hdGljYWxseS48L3A+XG4gICAgICAgICAgPC9kaXY+XG4gICAgICAgICl9XG4gICAgICA8L2Rpdj5cblxuICAgICAge3Nob3dQcmV2aWV3ICYmIHNlbGVjdGVkVGVtcGxhdGUgJiYgcHJldmlld0RhdGEgJiYgKFxuICAgICAgICA8ZGl2IGNsYXNzTmFtZT17c3R5bGVzLnByZXZpZXdTZWN0aW9ufT5cbiAgICAgICAgICA8aDM+UmVjZWlwdCBQcmV2aWV3PC9oMz5cbiAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT17c3R5bGVzLnByZXZpZXdDb250YWluZXJ9PlxuICAgICAgICAgICAgPFJlY2VpcHRQcmV2aWV3IFxuICAgICAgICAgICAgICB0ZW1wbGF0ZT17c2VsZWN0ZWRUZW1wbGF0ZX0gXG4gICAgICAgICAgICAgIGRhdGE9e3ByZXZpZXdEYXRhfSBcbiAgICAgICAgICAgIC8+XG4gICAgICAgICAgPC9kaXY+XG4gICAgICAgIDwvZGl2PlxuICAgICAgKX1cblxuICAgICAgey8qIFRlbXBsYXRlIENyZWF0aW9uL0VkaXRpbmcgTW9kYWwgKi99XG4gICAgICB7KHNob3dDcmVhdGVNb2RhbCB8fCBzaG93RWRpdE1vZGFsKSAmJiAoXG4gICAgICAgIDxUZW1wbGF0ZU1vZGFsXG4gICAgICAgICAgdGVtcGxhdGU9e2VkaXRpbmdUZW1wbGF0ZX1cbiAgICAgICAgICBvblNhdmU9e2hhbmRsZVNhdmVUZW1wbGF0ZX1cbiAgICAgICAgICBvbkNsb3NlPXsoKSA9PiB7XG4gICAgICAgICAgICBzZXRTaG93Q3JlYXRlTW9kYWwoZmFsc2UpXG4gICAgICAgICAgICBzZXRTaG93RWRpdE1vZGFsKGZhbHNlKVxuICAgICAgICAgICAgc2V0RWRpdGluZ1RlbXBsYXRlKG51bGwpXG4gICAgICAgICAgfX1cbiAgICAgICAgLz5cbiAgICAgICl9XG4gICAgPC9kaXY+XG4gIClcbn1cblxuLyoqXG4gKiBSZWNlaXB0IFByZXZpZXcgQ29tcG9uZW50XG4gKiBTaG93cyBhIGxpdmUgcHJldmlldyBvZiBob3cgdGhlIHJlY2VpcHQgd2lsbCBsb29rXG4gKi9cbmZ1bmN0aW9uIFJlY2VpcHRQcmV2aWV3KHsgdGVtcGxhdGUsIGRhdGEgfSkge1xuICBjb25zdCBbcHJldmlld0h0bWwsIHNldFByZXZpZXdIdG1sXSA9IHVzZVN0YXRlKCcnKVxuICBjb25zdCBbbG9hZGluZywgc2V0TG9hZGluZ10gPSB1c2VTdGF0ZSh0cnVlKVxuXG4gIHVzZUVmZmVjdCgoKSA9PiB7XG4gICAgZ2VuZXJhdGVQcmV2aWV3KClcbiAgfSwgW3RlbXBsYXRlLCBkYXRhXSlcblxuICBjb25zdCBnZW5lcmF0ZVByZXZpZXcgPSBhc3luYyAoKSA9PiB7XG4gICAgdHJ5IHtcbiAgICAgIHNldExvYWRpbmcodHJ1ZSlcbiAgICAgIFxuICAgICAgLy8gR2VuZXJhdGUgcHJldmlldyBIVE1MIHVzaW5nIHRoZSByZWNlaXB0IGdlbmVyYXRvclxuICAgICAgY29uc3QgcmVzcG9uc2UgPSBhd2FpdCBmZXRjaCgnL2FwaS9hZG1pbi9yZWNlaXB0cy9wcmV2aWV3Jywge1xuICAgICAgICBtZXRob2Q6ICdQT1NUJyxcbiAgICAgICAgaGVhZGVyczoge1xuICAgICAgICAgICdDb250ZW50LVR5cGUnOiAnYXBwbGljYXRpb24vanNvbicsXG4gICAgICAgICAgJ0F1dGhvcml6YXRpb24nOiBgQmVhcmVyICR7bG9jYWxTdG9yYWdlLmdldEl0ZW0oJ2FkbWluLXRva2VuJyl9YFxuICAgICAgICB9LFxuICAgICAgICBib2R5OiBKU09OLnN0cmluZ2lmeSh7XG4gICAgICAgICAgdGVtcGxhdGVJZDogdGVtcGxhdGUuaWQsXG4gICAgICAgICAgYm9va2luZ0RhdGE6IGRhdGFcbiAgICAgICAgfSlcbiAgICAgIH0pXG5cbiAgICAgIGlmICghcmVzcG9uc2Uub2spIHtcbiAgICAgICAgdGhyb3cgbmV3IEVycm9yKCdGYWlsZWQgdG8gZ2VuZXJhdGUgcHJldmlldycpXG4gICAgICB9XG5cbiAgICAgIGNvbnN0IHJlc3VsdCA9IGF3YWl0IHJlc3BvbnNlLmpzb24oKVxuICAgICAgc2V0UHJldmlld0h0bWwocmVzdWx0Lmh0bWwgfHwgJycpXG4gICAgfSBjYXRjaCAoZXJyb3IpIHtcbiAgICAgIGNvbnNvbGUuZXJyb3IoJ0Vycm9yIGdlbmVyYXRpbmcgcHJldmlldzonLCBlcnJvcilcbiAgICAgIHNldFByZXZpZXdIdG1sKCc8cD5QcmV2aWV3IG5vdCBhdmFpbGFibGU8L3A+JylcbiAgICB9IGZpbmFsbHkge1xuICAgICAgc2V0TG9hZGluZyhmYWxzZSlcbiAgICB9XG4gIH1cblxuICBpZiAobG9hZGluZykge1xuICAgIHJldHVybiAoXG4gICAgICA8ZGl2IGNsYXNzTmFtZT17c3R5bGVzLnByZXZpZXdMb2FkaW5nfT5cbiAgICAgICAgPGRpdiBjbGFzc05hbWU9e3N0eWxlcy5sb2FkaW5nU3Bpbm5lcn0+PC9kaXY+XG4gICAgICAgIDxwPkdlbmVyYXRpbmcgcHJldmlldy4uLjwvcD5cbiAgICAgIDwvZGl2PlxuICAgIClcbiAgfVxuXG4gIHJldHVybiAoXG4gICAgPGRpdiBjbGFzc05hbWU9e3N0eWxlcy5yZWNlaXB0UHJldmlld30+XG4gICAgICA8ZGl2IGNsYXNzTmFtZT17c3R5bGVzLnByZXZpZXdIZWFkZXJ9PlxuICAgICAgICA8c3BhbiBjbGFzc05hbWU9e3N0eWxlcy5wcmV2aWV3TGFiZWx9PlByZXZpZXc8L3NwYW4+XG4gICAgICAgIDxidXR0b25cbiAgICAgICAgICBvbkNsaWNrPXtnZW5lcmF0ZVByZXZpZXd9XG4gICAgICAgICAgY2xhc3NOYW1lPXtzdHlsZXMucmVmcmVzaEJ0bn1cbiAgICAgICAgICB0aXRsZT1cIlJlZnJlc2ggUHJldmlld1wiXG4gICAgICAgID5cbiAgICAgICAgICDwn5SEXG4gICAgICAgIDwvYnV0dG9uPlxuICAgICAgPC9kaXY+XG4gICAgICA8ZGl2XG4gICAgICAgIGNsYXNzTmFtZT17c3R5bGVzLnByZXZpZXdDb250ZW50fVxuICAgICAgICBkYW5nZXJvdXNseVNldElubmVySFRNTD17eyBfX2h0bWw6IHByZXZpZXdIdG1sIH19XG4gICAgICAvPlxuICAgIDwvZGl2PlxuICApXG59XG5cbi8qKlxuICogVGVtcGxhdGUgQ3JlYXRpb24vRWRpdGluZyBNb2RhbFxuICovXG5mdW5jdGlvbiBUZW1wbGF0ZU1vZGFsKHsgdGVtcGxhdGUsIG9uU2F2ZSwgb25DbG9zZSB9KSB7XG4gIGNvbnN0IFtmb3JtRGF0YSwgc2V0Rm9ybURhdGFdID0gdXNlU3RhdGUoe1xuICAgIG5hbWU6IHRlbXBsYXRlPy5uYW1lIHx8ICcnLFxuICAgIGRlc2NyaXB0aW9uOiB0ZW1wbGF0ZT8uZGVzY3JpcHRpb24gfHwgJycsXG4gICAgdGVtcGxhdGVfdHlwZTogdGVtcGxhdGU/LnRlbXBsYXRlX3R5cGUgfHwgJ3N0YW5kYXJkJyxcbiAgICBidXNpbmVzc19uYW1lOiB0ZW1wbGF0ZT8uYnVzaW5lc3NfbmFtZSB8fCAnT2NlYW4gU291bCBTcGFya2xlcycsXG4gICAgYnVzaW5lc3NfYWRkcmVzczogdGVtcGxhdGU/LmJ1c2luZXNzX2FkZHJlc3MgfHwgJycsXG4gICAgYnVzaW5lc3NfcGhvbmU6IHRlbXBsYXRlPy5idXNpbmVzc19waG9uZSB8fCAnJyxcbiAgICBidXNpbmVzc19lbWFpbDogdGVtcGxhdGU/LmJ1c2luZXNzX2VtYWlsIHx8ICcnLFxuICAgIGJ1c2luZXNzX3dlYnNpdGU6IHRlbXBsYXRlPy5idXNpbmVzc193ZWJzaXRlIHx8ICcnLFxuICAgIGJ1c2luZXNzX2FibjogdGVtcGxhdGU/LmJ1c2luZXNzX2FibiB8fCAnJyxcbiAgICBzaG93X2xvZ286IHRlbXBsYXRlPy5zaG93X2xvZ28gPz8gdHJ1ZSxcbiAgICBsb2dvX3Bvc2l0aW9uOiB0ZW1wbGF0ZT8ubG9nb19wb3NpdGlvbiB8fCAnY2VudGVyJyxcbiAgICBoZWFkZXJfY29sb3I6IHRlbXBsYXRlPy5oZWFkZXJfY29sb3IgfHwgJyM2NjdlZWEnLFxuICAgIHRleHRfY29sb3I6IHRlbXBsYXRlPy50ZXh0X2NvbG9yIHx8ICcjMzMzMzMzJyxcbiAgICBmb250X2ZhbWlseTogdGVtcGxhdGU/LmZvbnRfZmFtaWx5IHx8ICdBcmlhbCcsXG4gICAgZm9udF9zaXplOiB0ZW1wbGF0ZT8uZm9udF9zaXplIHx8IDEyLFxuICAgIHNob3dfY3VzdG9tZXJfZGV0YWlsczogdGVtcGxhdGU/LnNob3dfY3VzdG9tZXJfZGV0YWlscyA/PyB0cnVlLFxuICAgIHNob3dfc2VydmljZV9kZXRhaWxzOiB0ZW1wbGF0ZT8uc2hvd19zZXJ2aWNlX2RldGFpbHMgPz8gdHJ1ZSxcbiAgICBzaG93X2FydGlzdF9kZXRhaWxzOiB0ZW1wbGF0ZT8uc2hvd19hcnRpc3RfZGV0YWlscyA/PyB0cnVlLFxuICAgIHNob3dfcGF5bWVudF9kZXRhaWxzOiB0ZW1wbGF0ZT8uc2hvd19wYXltZW50X2RldGFpbHMgPz8gdHJ1ZSxcbiAgICBzaG93X2Jvb2tpbmdfbm90ZXM6IHRlbXBsYXRlPy5zaG93X2Jvb2tpbmdfbm90ZXMgPz8gZmFsc2UsXG4gICAgc2hvd190ZXJtc19jb25kaXRpb25zOiB0ZW1wbGF0ZT8uc2hvd190ZXJtc19jb25kaXRpb25zID8/IHRydWUsXG4gICAgZm9vdGVyX21lc3NhZ2U6IHRlbXBsYXRlPy5mb290ZXJfbWVzc2FnZSB8fCAnVGhhbmsgeW91IGZvciBjaG9vc2luZyBPY2VhbiBTb3VsIFNwYXJrbGVzIScsXG4gICAgc2hvd19zb2NpYWxfbWVkaWE6IHRlbXBsYXRlPy5zaG93X3NvY2lhbF9tZWRpYSA/PyBmYWxzZVxuICB9KVxuXG4gIGNvbnN0IGhhbmRsZVN1Ym1pdCA9IChlKSA9PiB7XG4gICAgZS5wcmV2ZW50RGVmYXVsdCgpXG4gICAgb25TYXZlKGZvcm1EYXRhKVxuICB9XG5cbiAgY29uc3QgaGFuZGxlQ2hhbmdlID0gKGZpZWxkLCB2YWx1ZSkgPT4ge1xuICAgIHNldEZvcm1EYXRhKHByZXYgPT4gKHtcbiAgICAgIC4uLnByZXYsXG4gICAgICBbZmllbGRdOiB2YWx1ZVxuICAgIH0pKVxuICB9XG5cbiAgcmV0dXJuIChcbiAgICA8ZGl2IGNsYXNzTmFtZT17c3R5bGVzLm1vZGFsT3ZlcmxheX0+XG4gICAgICA8ZGl2IGNsYXNzTmFtZT17c3R5bGVzLm1vZGFsfT5cbiAgICAgICAgPGRpdiBjbGFzc05hbWU9e3N0eWxlcy5tb2RhbEhlYWRlcn0+XG4gICAgICAgICAgPGgzPnt0ZW1wbGF0ZSA/ICdFZGl0IFRlbXBsYXRlJyA6ICdDcmVhdGUgVGVtcGxhdGUnfTwvaDM+XG4gICAgICAgICAgPGJ1dHRvbiBvbkNsaWNrPXtvbkNsb3NlfSBjbGFzc05hbWU9e3N0eWxlcy5jbG9zZUJ1dHRvbn0+4pyVPC9idXR0b24+XG4gICAgICAgIDwvZGl2PlxuXG4gICAgICAgIDxmb3JtIG9uU3VibWl0PXtoYW5kbGVTdWJtaXR9IGNsYXNzTmFtZT17c3R5bGVzLm1vZGFsRm9ybX0+XG4gICAgICAgICAgPGRpdiBjbGFzc05hbWU9e3N0eWxlcy5mb3JtR3JpZH0+XG4gICAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT17c3R5bGVzLmZvcm1TZWN0aW9ufT5cbiAgICAgICAgICAgICAgPGg0PkJhc2ljIEluZm9ybWF0aW9uPC9oND5cbiAgICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9e3N0eWxlcy5mb3JtR3JvdXB9PlxuICAgICAgICAgICAgICAgIDxsYWJlbD5UZW1wbGF0ZSBOYW1lPC9sYWJlbD5cbiAgICAgICAgICAgICAgICA8aW5wdXRcbiAgICAgICAgICAgICAgICAgIHR5cGU9XCJ0ZXh0XCJcbiAgICAgICAgICAgICAgICAgIHZhbHVlPXtmb3JtRGF0YS5uYW1lfVxuICAgICAgICAgICAgICAgICAgb25DaGFuZ2U9eyhlKSA9PiBoYW5kbGVDaGFuZ2UoJ25hbWUnLCBlLnRhcmdldC52YWx1ZSl9XG4gICAgICAgICAgICAgICAgICByZXF1aXJlZFxuICAgICAgICAgICAgICAgIC8+XG4gICAgICAgICAgICAgIDwvZGl2PlxuXG4gICAgICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPXtzdHlsZXMuZm9ybUdyb3VwfT5cbiAgICAgICAgICAgICAgICA8bGFiZWw+RGVzY3JpcHRpb248L2xhYmVsPlxuICAgICAgICAgICAgICAgIDx0ZXh0YXJlYVxuICAgICAgICAgICAgICAgICAgdmFsdWU9e2Zvcm1EYXRhLmRlc2NyaXB0aW9ufVxuICAgICAgICAgICAgICAgICAgb25DaGFuZ2U9eyhlKSA9PiBoYW5kbGVDaGFuZ2UoJ2Rlc2NyaXB0aW9uJywgZS50YXJnZXQudmFsdWUpfVxuICAgICAgICAgICAgICAgICAgcm93cz17M31cbiAgICAgICAgICAgICAgICAvPlxuICAgICAgICAgICAgICA8L2Rpdj5cblxuICAgICAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT17c3R5bGVzLmZvcm1Hcm91cH0+XG4gICAgICAgICAgICAgICAgPGxhYmVsPlRlbXBsYXRlIFR5cGU8L2xhYmVsPlxuICAgICAgICAgICAgICAgIDxzZWxlY3RcbiAgICAgICAgICAgICAgICAgIHZhbHVlPXtmb3JtRGF0YS50ZW1wbGF0ZV90eXBlfVxuICAgICAgICAgICAgICAgICAgb25DaGFuZ2U9eyhlKSA9PiBoYW5kbGVDaGFuZ2UoJ3RlbXBsYXRlX3R5cGUnLCBlLnRhcmdldC52YWx1ZSl9XG4gICAgICAgICAgICAgICAgPlxuICAgICAgICAgICAgICAgICAgPG9wdGlvbiB2YWx1ZT1cInN0YW5kYXJkXCI+U3RhbmRhcmQ8L29wdGlvbj5cbiAgICAgICAgICAgICAgICAgIDxvcHRpb24gdmFsdWU9XCJjb21wYWN0XCI+Q29tcGFjdDwvb3B0aW9uPlxuICAgICAgICAgICAgICAgICAgPG9wdGlvbiB2YWx1ZT1cImRldGFpbGVkXCI+RGV0YWlsZWQ8L29wdGlvbj5cbiAgICAgICAgICAgICAgICA8L3NlbGVjdD5cbiAgICAgICAgICAgICAgPC9kaXY+XG4gICAgICAgICAgICA8L2Rpdj5cblxuICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9e3N0eWxlcy5mb3JtU2VjdGlvbn0+XG4gICAgICAgICAgICAgIDxoND5CdXNpbmVzcyBJbmZvcm1hdGlvbjwvaDQ+XG4gICAgICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPXtzdHlsZXMuZm9ybUdyb3VwfT5cbiAgICAgICAgICAgICAgICA8bGFiZWw+QnVzaW5lc3MgTmFtZTwvbGFiZWw+XG4gICAgICAgICAgICAgICAgPGlucHV0XG4gICAgICAgICAgICAgICAgICB0eXBlPVwidGV4dFwiXG4gICAgICAgICAgICAgICAgICB2YWx1ZT17Zm9ybURhdGEuYnVzaW5lc3NfbmFtZX1cbiAgICAgICAgICAgICAgICAgIG9uQ2hhbmdlPXsoZSkgPT4gaGFuZGxlQ2hhbmdlKCdidXNpbmVzc19uYW1lJywgZS50YXJnZXQudmFsdWUpfVxuICAgICAgICAgICAgICAgIC8+XG4gICAgICAgICAgICAgIDwvZGl2PlxuXG4gICAgICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPXtzdHlsZXMuZm9ybUdyb3VwfT5cbiAgICAgICAgICAgICAgICA8bGFiZWw+QWRkcmVzczwvbGFiZWw+XG4gICAgICAgICAgICAgICAgPHRleHRhcmVhXG4gICAgICAgICAgICAgICAgICB2YWx1ZT17Zm9ybURhdGEuYnVzaW5lc3NfYWRkcmVzc31cbiAgICAgICAgICAgICAgICAgIG9uQ2hhbmdlPXsoZSkgPT4gaGFuZGxlQ2hhbmdlKCdidXNpbmVzc19hZGRyZXNzJywgZS50YXJnZXQudmFsdWUpfVxuICAgICAgICAgICAgICAgICAgcm93cz17Mn1cbiAgICAgICAgICAgICAgICAvPlxuICAgICAgICAgICAgICA8L2Rpdj5cblxuICAgICAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT17c3R5bGVzLmZvcm1Hcm91cH0+XG4gICAgICAgICAgICAgICAgPGxhYmVsPlBob25lPC9sYWJlbD5cbiAgICAgICAgICAgICAgICA8aW5wdXRcbiAgICAgICAgICAgICAgICAgIHR5cGU9XCJ0ZXh0XCJcbiAgICAgICAgICAgICAgICAgIHZhbHVlPXtmb3JtRGF0YS5idXNpbmVzc19waG9uZX1cbiAgICAgICAgICAgICAgICAgIG9uQ2hhbmdlPXsoZSkgPT4gaGFuZGxlQ2hhbmdlKCdidXNpbmVzc19waG9uZScsIGUudGFyZ2V0LnZhbHVlKX1cbiAgICAgICAgICAgICAgICAvPlxuICAgICAgICAgICAgICA8L2Rpdj5cblxuICAgICAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT17c3R5bGVzLmZvcm1Hcm91cH0+XG4gICAgICAgICAgICAgICAgPGxhYmVsPkVtYWlsPC9sYWJlbD5cbiAgICAgICAgICAgICAgICA8aW5wdXRcbiAgICAgICAgICAgICAgICAgIHR5cGU9XCJlbWFpbFwiXG4gICAgICAgICAgICAgICAgICB2YWx1ZT17Zm9ybURhdGEuYnVzaW5lc3NfZW1haWx9XG4gICAgICAgICAgICAgICAgICBvbkNoYW5nZT17KGUpID0+IGhhbmRsZUNoYW5nZSgnYnVzaW5lc3NfZW1haWwnLCBlLnRhcmdldC52YWx1ZSl9XG4gICAgICAgICAgICAgICAgLz5cbiAgICAgICAgICAgICAgPC9kaXY+XG4gICAgICAgICAgICA8L2Rpdj5cblxuICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9e3N0eWxlcy5mb3JtU2VjdGlvbn0+XG4gICAgICAgICAgICAgIDxoND5EaXNwbGF5IE9wdGlvbnM8L2g0PlxuICAgICAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT17c3R5bGVzLmNoZWNrYm94R3JvdXB9PlxuICAgICAgICAgICAgICAgIDxsYWJlbCBjbGFzc05hbWU9e3N0eWxlcy5jaGVja2JveExhYmVsfT5cbiAgICAgICAgICAgICAgICAgIDxpbnB1dFxuICAgICAgICAgICAgICAgICAgICB0eXBlPVwiY2hlY2tib3hcIlxuICAgICAgICAgICAgICAgICAgICBjaGVja2VkPXtmb3JtRGF0YS5zaG93X2N1c3RvbWVyX2RldGFpbHN9XG4gICAgICAgICAgICAgICAgICAgIG9uQ2hhbmdlPXsoZSkgPT4gaGFuZGxlQ2hhbmdlKCdzaG93X2N1c3RvbWVyX2RldGFpbHMnLCBlLnRhcmdldC5jaGVja2VkKX1cbiAgICAgICAgICAgICAgICAgIC8+XG4gICAgICAgICAgICAgICAgICA8c3Bhbj5TaG93IEN1c3RvbWVyIERldGFpbHM8L3NwYW4+XG4gICAgICAgICAgICAgICAgPC9sYWJlbD5cblxuICAgICAgICAgICAgICAgIDxsYWJlbCBjbGFzc05hbWU9e3N0eWxlcy5jaGVja2JveExhYmVsfT5cbiAgICAgICAgICAgICAgICAgIDxpbnB1dFxuICAgICAgICAgICAgICAgICAgICB0eXBlPVwiY2hlY2tib3hcIlxuICAgICAgICAgICAgICAgICAgICBjaGVja2VkPXtmb3JtRGF0YS5zaG93X3NlcnZpY2VfZGV0YWlsc31cbiAgICAgICAgICAgICAgICAgICAgb25DaGFuZ2U9eyhlKSA9PiBoYW5kbGVDaGFuZ2UoJ3Nob3dfc2VydmljZV9kZXRhaWxzJywgZS50YXJnZXQuY2hlY2tlZCl9XG4gICAgICAgICAgICAgICAgICAvPlxuICAgICAgICAgICAgICAgICAgPHNwYW4+U2hvdyBTZXJ2aWNlIERldGFpbHM8L3NwYW4+XG4gICAgICAgICAgICAgICAgPC9sYWJlbD5cblxuICAgICAgICAgICAgICAgIDxsYWJlbCBjbGFzc05hbWU9e3N0eWxlcy5jaGVja2JveExhYmVsfT5cbiAgICAgICAgICAgICAgICAgIDxpbnB1dFxuICAgICAgICAgICAgICAgICAgICB0eXBlPVwiY2hlY2tib3hcIlxuICAgICAgICAgICAgICAgICAgICBjaGVja2VkPXtmb3JtRGF0YS5zaG93X2FydGlzdF9kZXRhaWxzfVxuICAgICAgICAgICAgICAgICAgICBvbkNoYW5nZT17KGUpID0+IGhhbmRsZUNoYW5nZSgnc2hvd19hcnRpc3RfZGV0YWlscycsIGUudGFyZ2V0LmNoZWNrZWQpfVxuICAgICAgICAgICAgICAgICAgLz5cbiAgICAgICAgICAgICAgICAgIDxzcGFuPlNob3cgQXJ0aXN0IERldGFpbHM8L3NwYW4+XG4gICAgICAgICAgICAgICAgPC9sYWJlbD5cblxuICAgICAgICAgICAgICAgIDxsYWJlbCBjbGFzc05hbWU9e3N0eWxlcy5jaGVja2JveExhYmVsfT5cbiAgICAgICAgICAgICAgICAgIDxpbnB1dFxuICAgICAgICAgICAgICAgICAgICB0eXBlPVwiY2hlY2tib3hcIlxuICAgICAgICAgICAgICAgICAgICBjaGVja2VkPXtmb3JtRGF0YS5zaG93X3BheW1lbnRfZGV0YWlsc31cbiAgICAgICAgICAgICAgICAgICAgb25DaGFuZ2U9eyhlKSA9PiBoYW5kbGVDaGFuZ2UoJ3Nob3dfcGF5bWVudF9kZXRhaWxzJywgZS50YXJnZXQuY2hlY2tlZCl9XG4gICAgICAgICAgICAgICAgICAvPlxuICAgICAgICAgICAgICAgICAgPHNwYW4+U2hvdyBQYXltZW50IERldGFpbHM8L3NwYW4+XG4gICAgICAgICAgICAgICAgPC9sYWJlbD5cbiAgICAgICAgICAgICAgPC9kaXY+XG4gICAgICAgICAgICA8L2Rpdj5cbiAgICAgICAgICA8L2Rpdj5cblxuICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPXtzdHlsZXMubW9kYWxBY3Rpb25zfT5cbiAgICAgICAgICAgIDxidXR0b24gdHlwZT1cImJ1dHRvblwiIG9uQ2xpY2s9e29uQ2xvc2V9IGNsYXNzTmFtZT17c3R5bGVzLmNhbmNlbEJ1dHRvbn0+XG4gICAgICAgICAgICAgIENhbmNlbFxuICAgICAgICAgICAgPC9idXR0b24+XG4gICAgICAgICAgICA8YnV0dG9uIHR5cGU9XCJzdWJtaXRcIiBjbGFzc05hbWU9e3N0eWxlcy5zYXZlQnV0dG9ufT5cbiAgICAgICAgICAgICAge3RlbXBsYXRlID8gJ1VwZGF0ZSBUZW1wbGF0ZScgOiAnQ3JlYXRlIFRlbXBsYXRlJ31cbiAgICAgICAgICAgIDwvYnV0dG9uPlxuICAgICAgICAgIDwvZGl2PlxuICAgICAgICA8L2Zvcm0+XG4gICAgICA8L2Rpdj5cbiAgICA8L2Rpdj5cbiAgKVxufVxuIl0sIm5hbWVzIjpbInVzZVN0YXRlIiwidXNlRWZmZWN0Iiwic3R5bGVzIiwiUmVjZWlwdEN1c3RvbWl6ZXIiLCJvblRlbXBsYXRlU2VsZWN0Iiwic2VsZWN0ZWRUZW1wbGF0ZSIsInNob3dQcmV2aWV3IiwidGVtcGxhdGVzIiwic2V0VGVtcGxhdGVzIiwibG9hZGluZyIsInNldExvYWRpbmciLCJlcnJvciIsInNldEVycm9yIiwicHJldmlld0RhdGEiLCJzZXRQcmV2aWV3RGF0YSIsInNob3dDcmVhdGVNb2RhbCIsInNldFNob3dDcmVhdGVNb2RhbCIsInNob3dFZGl0TW9kYWwiLCJzZXRTaG93RWRpdE1vZGFsIiwiZWRpdGluZ1RlbXBsYXRlIiwic2V0RWRpdGluZ1RlbXBsYXRlIiwibG9hZFRlbXBsYXRlcyIsImdlbmVyYXRlUHJldmlld0RhdGEiLCJyZXNwb25zZSIsImZldGNoIiwiaGVhZGVycyIsImxvY2FsU3RvcmFnZSIsImdldEl0ZW0iLCJvayIsIkVycm9yIiwiZGF0YSIsImpzb24iLCJsZW5ndGgiLCJkZWZhdWx0VGVtcGxhdGUiLCJmaW5kIiwidCIsImlzX2RlZmF1bHQiLCJlcnIiLCJjb25zb2xlIiwibWVzc2FnZSIsInJlY2VpcHRfbnVtYmVyIiwiRGF0ZSIsIm5vdyIsImN1c3RvbWVyX25hbWUiLCJjdXN0b21lcl9lbWFpbCIsImN1c3RvbWVyX3Bob25lIiwic2VydmljZV9uYW1lIiwidGllcl9uYW1lIiwiYXJ0aXN0X25hbWUiLCJzdGFydF90aW1lIiwidG9JU09TdHJpbmciLCJkdXJhdGlvbiIsInRvdGFsX2Ftb3VudCIsInRpcF9hbW91bnQiLCJwYXltZW50X21ldGhvZCIsIm5vdGVzIiwiaGFuZGxlVGVtcGxhdGVTZWxlY3QiLCJ0ZW1wbGF0ZSIsImhhbmRsZUNyZWF0ZVRlbXBsYXRlIiwiaGFuZGxlRWRpdFRlbXBsYXRlIiwiaGFuZGxlRGVsZXRlVGVtcGxhdGUiLCJhbGVydCIsImNvbmZpcm0iLCJuYW1lIiwiaWQiLCJtZXRob2QiLCJoYW5kbGVTYXZlVGVtcGxhdGUiLCJ0ZW1wbGF0ZURhdGEiLCJ1cmwiLCJib2R5IiwiSlNPTiIsInN0cmluZ2lmeSIsImdldFRlbXBsYXRlVHlwZUljb24iLCJ0eXBlIiwiZ2V0VGVtcGxhdGVUeXBlRGVzY3JpcHRpb24iLCJkaXYiLCJjbGFzc05hbWUiLCJsb2FkaW5nU3Bpbm5lciIsInAiLCJidXR0b24iLCJvbkNsaWNrIiwicmV0cnlCdG4iLCJyZWNlaXB0Q3VzdG9taXplciIsInRlbXBsYXRlc1NlY3Rpb24iLCJzZWN0aW9uSGVhZGVyIiwiaDMiLCJkZXNjcmlwdGlvbiIsImNyZWF0ZUJ1dHRvbiIsInRlbXBsYXRlR3JpZCIsIm1hcCIsInRlbXBsYXRlQ2FyZCIsInNlbGVjdGVkIiwidGVtcGxhdGVIZWFkZXIiLCJzcGFuIiwidGVtcGxhdGVJY29uIiwidGVtcGxhdGVfdHlwZSIsInRlbXBsYXRlSW5mbyIsImg0IiwidGVtcGxhdGVOYW1lIiwiZGVmYXVsdEJhZGdlIiwidGVtcGxhdGVEZXNjcmlwdGlvbiIsInRlbXBsYXRlRmVhdHVyZXMiLCJmZWF0dXJlTGlzdCIsInNob3dfY3VzdG9tZXJfZGV0YWlscyIsImZlYXR1cmUiLCJzaG93X3NlcnZpY2VfZGV0YWlscyIsInNob3dfYXJ0aXN0X2RldGFpbHMiLCJzaG93X3BheW1lbnRfZGV0YWlscyIsInRlbXBsYXRlTWV0YSIsInRlbXBsYXRlVHlwZSIsImNoYXJBdCIsInRvVXBwZXJDYXNlIiwic2xpY2UiLCJidXNpbmVzc05hbWUiLCJidXNpbmVzc19uYW1lIiwidGVtcGxhdGVBY3Rpb25zIiwiZSIsInN0b3BQcm9wYWdhdGlvbiIsImFjdGlvbkJ1dHRvbiIsInRpdGxlIiwiZGVsZXRlQnV0dG9uIiwibm9UZW1wbGF0ZXMiLCJwcmV2aWV3U2VjdGlvbiIsInByZXZpZXdDb250YWluZXIiLCJSZWNlaXB0UHJldmlldyIsIlRlbXBsYXRlTW9kYWwiLCJvblNhdmUiLCJvbkNsb3NlIiwicHJldmlld0h0bWwiLCJzZXRQcmV2aWV3SHRtbCIsImdlbmVyYXRlUHJldmlldyIsInRlbXBsYXRlSWQiLCJib29raW5nRGF0YSIsInJlc3VsdCIsImh0bWwiLCJwcmV2aWV3TG9hZGluZyIsInJlY2VpcHRQcmV2aWV3IiwicHJldmlld0hlYWRlciIsInByZXZpZXdMYWJlbCIsInJlZnJlc2hCdG4iLCJwcmV2aWV3Q29udGVudCIsImRhbmdlcm91c2x5U2V0SW5uZXJIVE1MIiwiX19odG1sIiwiZm9ybURhdGEiLCJzZXRGb3JtRGF0YSIsImJ1c2luZXNzX2FkZHJlc3MiLCJidXNpbmVzc19waG9uZSIsImJ1c2luZXNzX2VtYWlsIiwiYnVzaW5lc3Nfd2Vic2l0ZSIsImJ1c2luZXNzX2FibiIsInNob3dfbG9nbyIsImxvZ29fcG9zaXRpb24iLCJoZWFkZXJfY29sb3IiLCJ0ZXh0X2NvbG9yIiwiZm9udF9mYW1pbHkiLCJmb250X3NpemUiLCJzaG93X2Jvb2tpbmdfbm90ZXMiLCJzaG93X3Rlcm1zX2NvbmRpdGlvbnMiLCJmb290ZXJfbWVzc2FnZSIsInNob3dfc29jaWFsX21lZGlhIiwiaGFuZGxlU3VibWl0IiwicHJldmVudERlZmF1bHQiLCJoYW5kbGVDaGFuZ2UiLCJmaWVsZCIsInZhbHVlIiwicHJldiIsIm1vZGFsT3ZlcmxheSIsIm1vZGFsIiwibW9kYWxIZWFkZXIiLCJjbG9zZUJ1dHRvbiIsImZvcm0iLCJvblN1Ym1pdCIsIm1vZGFsRm9ybSIsImZvcm1HcmlkIiwiZm9ybVNlY3Rpb24iLCJmb3JtR3JvdXAiLCJsYWJlbCIsImlucHV0Iiwib25DaGFuZ2UiLCJ0YXJnZXQiLCJyZXF1aXJlZCIsInRleHRhcmVhIiwicm93cyIsInNlbGVjdCIsIm9wdGlvbiIsImNoZWNrYm94R3JvdXAiLCJjaGVja2JveExhYmVsIiwiY2hlY2tlZCIsIm1vZGFsQWN0aW9ucyIsImNhbmNlbEJ1dHRvbiIsInNhdmVCdXR0b24iXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///./components/admin/pos/ReceiptCustomizer.js\n");

/***/ }),

/***/ "./hooks/useAuth.ts":
/*!**************************!*\
  !*** ./hooks/useAuth.ts ***!
  \**************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   useAuth: () => (/* binding */ useAuth)\n/* harmony export */ });\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react */ \"react\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_router__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next/router */ \"./node_modules/next/router.js\");\n/* harmony import */ var next_router__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(next_router__WEBPACK_IMPORTED_MODULE_1__);\n\n\nfunction useAuth() {\n    const router = (0,next_router__WEBPACK_IMPORTED_MODULE_1__.useRouter)();\n    const [authState, setAuthState] = (0,react__WEBPACK_IMPORTED_MODULE_0__.useState)({\n        user: null,\n        loading: true,\n        error: null\n    });\n    (0,react__WEBPACK_IMPORTED_MODULE_0__.useEffect)(()=>{\n        checkAuth();\n    }, []);\n    const checkAuth = async ()=>{\n        try {\n            const token = localStorage.getItem(\"admin-token\");\n            if (!token) {\n                setAuthState({\n                    user: null,\n                    loading: false,\n                    error: null\n                });\n                return;\n            }\n            const response = await fetch(\"/api/auth/verify\", {\n                headers: {\n                    \"Authorization\": `Bearer ${token}`\n                }\n            });\n            if (!response.ok) {\n                // Token is invalid\n                localStorage.removeItem(\"admin-token\");\n                setAuthState({\n                    user: null,\n                    loading: false,\n                    error: \"Session expired\"\n                });\n                return;\n            }\n            const data = await response.json();\n            setAuthState({\n                user: data.user,\n                loading: false,\n                error: null\n            });\n        } catch (error) {\n            console.error(\"Auth check error:\", error);\n            localStorage.removeItem(\"admin-token\");\n            setAuthState({\n                user: null,\n                loading: false,\n                error: \"Authentication failed\"\n            });\n        }\n    };\n    const login = async (email, password)=>{\n        try {\n            setAuthState((prev)=>({\n                    ...prev,\n                    loading: true,\n                    error: null\n                }));\n            const response = await fetch(\"/api/auth/login\", {\n                method: \"POST\",\n                headers: {\n                    \"Content-Type\": \"application/json\"\n                },\n                body: JSON.stringify({\n                    email,\n                    password\n                })\n            });\n            const data = await response.json();\n            if (!response.ok) {\n                throw new Error(data.error || \"Login failed\");\n            }\n            if (data.requiresMFA) {\n                return {\n                    requiresMFA: true,\n                    user: data.user\n                };\n            }\n            localStorage.setItem(\"admin-token\", data.token);\n            setAuthState({\n                user: data.user,\n                loading: false,\n                error: null\n            });\n            return {\n                success: true,\n                user: data.user\n            };\n        } catch (error) {\n            const errorMessage = error instanceof Error ? error.message : \"Login failed\";\n            setAuthState((prev)=>({\n                    ...prev,\n                    loading: false,\n                    error: errorMessage\n                }));\n            throw error;\n        }\n    };\n    const verifyMFA = async (userId, mfaCode)=>{\n        try {\n            setAuthState((prev)=>({\n                    ...prev,\n                    loading: true,\n                    error: null\n                }));\n            const response = await fetch(\"/api/auth/mfa-verify\", {\n                method: \"POST\",\n                headers: {\n                    \"Content-Type\": \"application/json\"\n                },\n                body: JSON.stringify({\n                    userId,\n                    mfaCode\n                })\n            });\n            const data = await response.json();\n            if (!response.ok) {\n                throw new Error(data.error || \"MFA verification failed\");\n            }\n            localStorage.setItem(\"admin-token\", data.token);\n            setAuthState({\n                user: data.user,\n                loading: false,\n                error: null\n            });\n            return {\n                success: true,\n                user: data.user\n            };\n        } catch (error) {\n            const errorMessage = error instanceof Error ? error.message : \"MFA verification failed\";\n            setAuthState((prev)=>({\n                    ...prev,\n                    loading: false,\n                    error: errorMessage\n                }));\n            throw error;\n        }\n    };\n    const logout = async ()=>{\n        try {\n            const token = localStorage.getItem(\"admin-token\");\n            if (token) {\n                await fetch(\"/api/auth/logout\", {\n                    method: \"POST\",\n                    headers: {\n                        \"Authorization\": `Bearer ${token}`\n                    }\n                });\n            }\n        } catch (error) {\n            console.error(\"Logout error:\", error);\n        } finally{\n            localStorage.removeItem(\"admin-token\");\n            setAuthState({\n                user: null,\n                loading: false,\n                error: null\n            });\n            router.push(\"/admin/login\");\n        }\n    };\n    const updateUser = (updatedUser)=>{\n        setAuthState((prev)=>({\n                ...prev,\n                user: prev.user ? {\n                    ...prev.user,\n                    ...updatedUser\n                } : null\n            }));\n    };\n    const hasPermission = (permission)=>{\n        if (!authState.user) return false;\n        // DEV role has all permissions\n        if (authState.user.role === \"DEV\") return true;\n        // Check specific permissions\n        return authState.user.permissions.includes(permission);\n    };\n    const hasRole = (roles)=>{\n        if (!authState.user) return false;\n        const roleArray = Array.isArray(roles) ? roles : [\n            roles\n        ];\n        return roleArray.includes(authState.user.role);\n    };\n    const isAdmin = ()=>{\n        return hasRole([\n            \"DEV\",\n            \"Admin\"\n        ]);\n    };\n    const isStaff = ()=>{\n        return hasRole([\n            \"DEV\",\n            \"Admin\",\n            \"Artist\",\n            \"Braider\"\n        ]);\n    };\n    return {\n        user: authState.user,\n        loading: authState.loading,\n        error: authState.error,\n        login,\n        verifyMFA,\n        logout,\n        updateUser,\n        hasPermission,\n        hasRole,\n        isAdmin,\n        isStaff,\n        checkAuth\n    };\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiLi9ob29rcy91c2VBdXRoLnRzIiwibWFwcGluZ3MiOiI7Ozs7Ozs7O0FBQTRDO0FBQ0o7QUFvQmpDLFNBQVNHO0lBQ2QsTUFBTUMsU0FBU0Ysc0RBQVNBO0lBQ3hCLE1BQU0sQ0FBQ0csV0FBV0MsYUFBYSxHQUFHTiwrQ0FBUUEsQ0FBWTtRQUNwRE8sTUFBTTtRQUNOQyxTQUFTO1FBQ1RDLE9BQU87SUFDVDtJQUVBUixnREFBU0EsQ0FBQztRQUNSUztJQUNGLEdBQUcsRUFBRTtJQUVMLE1BQU1BLFlBQVk7UUFDaEIsSUFBSTtZQUNGLE1BQU1DLFFBQVFDLGFBQWFDLE9BQU8sQ0FBQztZQUVuQyxJQUFJLENBQUNGLE9BQU87Z0JBQ1ZMLGFBQWE7b0JBQUVDLE1BQU07b0JBQU1DLFNBQVM7b0JBQU9DLE9BQU87Z0JBQUs7Z0JBQ3ZEO1lBQ0Y7WUFFQSxNQUFNSyxXQUFXLE1BQU1DLE1BQU0sb0JBQW9CO2dCQUMvQ0MsU0FBUztvQkFDUCxpQkFBaUIsQ0FBQyxPQUFPLEVBQUVMLE1BQU0sQ0FBQztnQkFDcEM7WUFDRjtZQUVBLElBQUksQ0FBQ0csU0FBU0csRUFBRSxFQUFFO2dCQUNoQixtQkFBbUI7Z0JBQ25CTCxhQUFhTSxVQUFVLENBQUM7Z0JBQ3hCWixhQUFhO29CQUFFQyxNQUFNO29CQUFNQyxTQUFTO29CQUFPQyxPQUFPO2dCQUFrQjtnQkFDcEU7WUFDRjtZQUVBLE1BQU1VLE9BQU8sTUFBTUwsU0FBU00sSUFBSTtZQUNoQ2QsYUFBYTtnQkFDWEMsTUFBTVksS0FBS1osSUFBSTtnQkFDZkMsU0FBUztnQkFDVEMsT0FBTztZQUNUO1FBRUYsRUFBRSxPQUFPQSxPQUFPO1lBQ2RZLFFBQVFaLEtBQUssQ0FBQyxxQkFBcUJBO1lBQ25DRyxhQUFhTSxVQUFVLENBQUM7WUFDeEJaLGFBQWE7Z0JBQ1hDLE1BQU07Z0JBQ05DLFNBQVM7Z0JBQ1RDLE9BQU87WUFDVDtRQUNGO0lBQ0Y7SUFFQSxNQUFNYSxRQUFRLE9BQU9DLE9BQWVDO1FBQ2xDLElBQUk7WUFDRmxCLGFBQWFtQixDQUFBQSxPQUFTO29CQUFFLEdBQUdBLElBQUk7b0JBQUVqQixTQUFTO29CQUFNQyxPQUFPO2dCQUFLO1lBRTVELE1BQU1LLFdBQVcsTUFBTUMsTUFBTSxtQkFBbUI7Z0JBQzlDVyxRQUFRO2dCQUNSVixTQUFTO29CQUNQLGdCQUFnQjtnQkFDbEI7Z0JBQ0FXLE1BQU1DLEtBQUtDLFNBQVMsQ0FBQztvQkFBRU47b0JBQU9DO2dCQUFTO1lBQ3pDO1lBRUEsTUFBTUwsT0FBTyxNQUFNTCxTQUFTTSxJQUFJO1lBRWhDLElBQUksQ0FBQ04sU0FBU0csRUFBRSxFQUFFO2dCQUNoQixNQUFNLElBQUlhLE1BQU1YLEtBQUtWLEtBQUssSUFBSTtZQUNoQztZQUVBLElBQUlVLEtBQUtZLFdBQVcsRUFBRTtnQkFDcEIsT0FBTztvQkFBRUEsYUFBYTtvQkFBTXhCLE1BQU1ZLEtBQUtaLElBQUk7Z0JBQUM7WUFDOUM7WUFFQUssYUFBYW9CLE9BQU8sQ0FBQyxlQUFlYixLQUFLUixLQUFLO1lBQzlDTCxhQUFhO2dCQUNYQyxNQUFNWSxLQUFLWixJQUFJO2dCQUNmQyxTQUFTO2dCQUNUQyxPQUFPO1lBQ1Q7WUFFQSxPQUFPO2dCQUFFd0IsU0FBUztnQkFBTTFCLE1BQU1ZLEtBQUtaLElBQUk7WUFBQztRQUUxQyxFQUFFLE9BQU9FLE9BQU87WUFDZCxNQUFNeUIsZUFBZXpCLGlCQUFpQnFCLFFBQVFyQixNQUFNMEIsT0FBTyxHQUFHO1lBQzlEN0IsYUFBYW1CLENBQUFBLE9BQVM7b0JBQ3BCLEdBQUdBLElBQUk7b0JBQ1BqQixTQUFTO29CQUNUQyxPQUFPeUI7Z0JBQ1Q7WUFDQSxNQUFNekI7UUFDUjtJQUNGO0lBRUEsTUFBTTJCLFlBQVksT0FBT0MsUUFBZ0JDO1FBQ3ZDLElBQUk7WUFDRmhDLGFBQWFtQixDQUFBQSxPQUFTO29CQUFFLEdBQUdBLElBQUk7b0JBQUVqQixTQUFTO29CQUFNQyxPQUFPO2dCQUFLO1lBRTVELE1BQU1LLFdBQVcsTUFBTUMsTUFBTSx3QkFBd0I7Z0JBQ25EVyxRQUFRO2dCQUNSVixTQUFTO29CQUNQLGdCQUFnQjtnQkFDbEI7Z0JBQ0FXLE1BQU1DLEtBQUtDLFNBQVMsQ0FBQztvQkFBRVE7b0JBQVFDO2dCQUFRO1lBQ3pDO1lBRUEsTUFBTW5CLE9BQU8sTUFBTUwsU0FBU00sSUFBSTtZQUVoQyxJQUFJLENBQUNOLFNBQVNHLEVBQUUsRUFBRTtnQkFDaEIsTUFBTSxJQUFJYSxNQUFNWCxLQUFLVixLQUFLLElBQUk7WUFDaEM7WUFFQUcsYUFBYW9CLE9BQU8sQ0FBQyxlQUFlYixLQUFLUixLQUFLO1lBQzlDTCxhQUFhO2dCQUNYQyxNQUFNWSxLQUFLWixJQUFJO2dCQUNmQyxTQUFTO2dCQUNUQyxPQUFPO1lBQ1Q7WUFFQSxPQUFPO2dCQUFFd0IsU0FBUztnQkFBTTFCLE1BQU1ZLEtBQUtaLElBQUk7WUFBQztRQUUxQyxFQUFFLE9BQU9FLE9BQU87WUFDZCxNQUFNeUIsZUFBZXpCLGlCQUFpQnFCLFFBQVFyQixNQUFNMEIsT0FBTyxHQUFHO1lBQzlEN0IsYUFBYW1CLENBQUFBLE9BQVM7b0JBQ3BCLEdBQUdBLElBQUk7b0JBQ1BqQixTQUFTO29CQUNUQyxPQUFPeUI7Z0JBQ1Q7WUFDQSxNQUFNekI7UUFDUjtJQUNGO0lBRUEsTUFBTThCLFNBQVM7UUFDYixJQUFJO1lBQ0YsTUFBTTVCLFFBQVFDLGFBQWFDLE9BQU8sQ0FBQztZQUVuQyxJQUFJRixPQUFPO2dCQUNULE1BQU1JLE1BQU0sb0JBQW9CO29CQUM5QlcsUUFBUTtvQkFDUlYsU0FBUzt3QkFDUCxpQkFBaUIsQ0FBQyxPQUFPLEVBQUVMLE1BQU0sQ0FBQztvQkFDcEM7Z0JBQ0Y7WUFDRjtRQUNGLEVBQUUsT0FBT0YsT0FBTztZQUNkWSxRQUFRWixLQUFLLENBQUMsaUJBQWlCQTtRQUNqQyxTQUFVO1lBQ1JHLGFBQWFNLFVBQVUsQ0FBQztZQUN4QlosYUFBYTtnQkFBRUMsTUFBTTtnQkFBTUMsU0FBUztnQkFBT0MsT0FBTztZQUFLO1lBQ3ZETCxPQUFPb0MsSUFBSSxDQUFDO1FBQ2Q7SUFDRjtJQUVBLE1BQU1DLGFBQWEsQ0FBQ0M7UUFDbEJwQyxhQUFhbUIsQ0FBQUEsT0FBUztnQkFDcEIsR0FBR0EsSUFBSTtnQkFDUGxCLE1BQU1rQixLQUFLbEIsSUFBSSxHQUFHO29CQUFFLEdBQUdrQixLQUFLbEIsSUFBSTtvQkFBRSxHQUFHbUMsV0FBVztnQkFBQyxJQUFJO1lBQ3ZEO0lBQ0Y7SUFFQSxNQUFNQyxnQkFBZ0IsQ0FBQ0M7UUFDckIsSUFBSSxDQUFDdkMsVUFBVUUsSUFBSSxFQUFFLE9BQU87UUFFNUIsK0JBQStCO1FBQy9CLElBQUlGLFVBQVVFLElBQUksQ0FBQ3NDLElBQUksS0FBSyxPQUFPLE9BQU87UUFFMUMsNkJBQTZCO1FBQzdCLE9BQU94QyxVQUFVRSxJQUFJLENBQUN1QyxXQUFXLENBQUNDLFFBQVEsQ0FBQ0g7SUFDN0M7SUFFQSxNQUFNSSxVQUFVLENBQUNDO1FBQ2YsSUFBSSxDQUFDNUMsVUFBVUUsSUFBSSxFQUFFLE9BQU87UUFFNUIsTUFBTTJDLFlBQVlDLE1BQU1DLE9BQU8sQ0FBQ0gsU0FBU0EsUUFBUTtZQUFDQTtTQUFNO1FBQ3hELE9BQU9DLFVBQVVILFFBQVEsQ0FBQzFDLFVBQVVFLElBQUksQ0FBQ3NDLElBQUk7SUFDL0M7SUFFQSxNQUFNUSxVQUFVO1FBQ2QsT0FBT0wsUUFBUTtZQUFDO1lBQU87U0FBUTtJQUNqQztJQUVBLE1BQU1NLFVBQVU7UUFDZCxPQUFPTixRQUFRO1lBQUM7WUFBTztZQUFTO1lBQVU7U0FBVTtJQUN0RDtJQUVBLE9BQU87UUFDTHpDLE1BQU1GLFVBQVVFLElBQUk7UUFDcEJDLFNBQVNILFVBQVVHLE9BQU87UUFDMUJDLE9BQU9KLFVBQVVJLEtBQUs7UUFDdEJhO1FBQ0FjO1FBQ0FHO1FBQ0FFO1FBQ0FFO1FBQ0FLO1FBQ0FLO1FBQ0FDO1FBQ0E1QztJQUNGO0FBQ0YiLCJzb3VyY2VzIjpbIndlYnBhY2s6Ly9vY2VhbnNvdWxzcGFya2xlcy1hZG1pbi8uL2hvb2tzL3VzZUF1dGgudHM/OWM3NiJdLCJzb3VyY2VzQ29udGVudCI6WyJpbXBvcnQgeyB1c2VTdGF0ZSwgdXNlRWZmZWN0IH0gZnJvbSAncmVhY3QnO1xyXG5pbXBvcnQgeyB1c2VSb3V0ZXIgfSBmcm9tICduZXh0L3JvdXRlcic7XHJcblxyXG5pbnRlcmZhY2UgVXNlciB7XHJcbiAgaWQ6IHN0cmluZztcclxuICBlbWFpbDogc3RyaW5nO1xyXG4gIHJvbGU6ICdERVYnIHwgJ0FkbWluJyB8ICdBcnRpc3QnIHwgJ0JyYWlkZXInO1xyXG4gIGZpcnN0TmFtZTogc3RyaW5nO1xyXG4gIGxhc3ROYW1lOiBzdHJpbmc7XHJcbiAgaXNBY3RpdmU6IGJvb2xlYW47XHJcbiAgbWZhRW5hYmxlZDogYm9vbGVhbjtcclxuICBsYXN0QWN0aXZpdHk6IG51bWJlcjtcclxuICBwZXJtaXNzaW9uczogc3RyaW5nW107XHJcbn1cclxuXHJcbmludGVyZmFjZSBBdXRoU3RhdGUge1xyXG4gIHVzZXI6IFVzZXIgfCBudWxsO1xyXG4gIGxvYWRpbmc6IGJvb2xlYW47XHJcbiAgZXJyb3I6IHN0cmluZyB8IG51bGw7XHJcbn1cclxuXHJcbmV4cG9ydCBmdW5jdGlvbiB1c2VBdXRoKCkge1xyXG4gIGNvbnN0IHJvdXRlciA9IHVzZVJvdXRlcigpO1xyXG4gIGNvbnN0IFthdXRoU3RhdGUsIHNldEF1dGhTdGF0ZV0gPSB1c2VTdGF0ZTxBdXRoU3RhdGU+KHtcclxuICAgIHVzZXI6IG51bGwsXHJcbiAgICBsb2FkaW5nOiB0cnVlLFxyXG4gICAgZXJyb3I6IG51bGxcclxuICB9KTtcclxuXHJcbiAgdXNlRWZmZWN0KCgpID0+IHtcclxuICAgIGNoZWNrQXV0aCgpO1xyXG4gIH0sIFtdKTtcclxuXHJcbiAgY29uc3QgY2hlY2tBdXRoID0gYXN5bmMgKCkgPT4ge1xyXG4gICAgdHJ5IHtcclxuICAgICAgY29uc3QgdG9rZW4gPSBsb2NhbFN0b3JhZ2UuZ2V0SXRlbSgnYWRtaW4tdG9rZW4nKTtcclxuICAgICAgXHJcbiAgICAgIGlmICghdG9rZW4pIHtcclxuICAgICAgICBzZXRBdXRoU3RhdGUoeyB1c2VyOiBudWxsLCBsb2FkaW5nOiBmYWxzZSwgZXJyb3I6IG51bGwgfSk7XHJcbiAgICAgICAgcmV0dXJuO1xyXG4gICAgICB9XHJcblxyXG4gICAgICBjb25zdCByZXNwb25zZSA9IGF3YWl0IGZldGNoKCcvYXBpL2F1dGgvdmVyaWZ5Jywge1xyXG4gICAgICAgIGhlYWRlcnM6IHtcclxuICAgICAgICAgICdBdXRob3JpemF0aW9uJzogYEJlYXJlciAke3Rva2VufWBcclxuICAgICAgICB9XHJcbiAgICAgIH0pO1xyXG5cclxuICAgICAgaWYgKCFyZXNwb25zZS5vaykge1xyXG4gICAgICAgIC8vIFRva2VuIGlzIGludmFsaWRcclxuICAgICAgICBsb2NhbFN0b3JhZ2UucmVtb3ZlSXRlbSgnYWRtaW4tdG9rZW4nKTtcclxuICAgICAgICBzZXRBdXRoU3RhdGUoeyB1c2VyOiBudWxsLCBsb2FkaW5nOiBmYWxzZSwgZXJyb3I6ICdTZXNzaW9uIGV4cGlyZWQnIH0pO1xyXG4gICAgICAgIHJldHVybjtcclxuICAgICAgfVxyXG5cclxuICAgICAgY29uc3QgZGF0YSA9IGF3YWl0IHJlc3BvbnNlLmpzb24oKTtcclxuICAgICAgc2V0QXV0aFN0YXRlKHsgXHJcbiAgICAgICAgdXNlcjogZGF0YS51c2VyLCBcclxuICAgICAgICBsb2FkaW5nOiBmYWxzZSwgXHJcbiAgICAgICAgZXJyb3I6IG51bGwgXHJcbiAgICAgIH0pO1xyXG5cclxuICAgIH0gY2F0Y2ggKGVycm9yKSB7XHJcbiAgICAgIGNvbnNvbGUuZXJyb3IoJ0F1dGggY2hlY2sgZXJyb3I6JywgZXJyb3IpO1xyXG4gICAgICBsb2NhbFN0b3JhZ2UucmVtb3ZlSXRlbSgnYWRtaW4tdG9rZW4nKTtcclxuICAgICAgc2V0QXV0aFN0YXRlKHsgXHJcbiAgICAgICAgdXNlcjogbnVsbCwgXHJcbiAgICAgICAgbG9hZGluZzogZmFsc2UsIFxyXG4gICAgICAgIGVycm9yOiAnQXV0aGVudGljYXRpb24gZmFpbGVkJyBcclxuICAgICAgfSk7XHJcbiAgICB9XHJcbiAgfTtcclxuXHJcbiAgY29uc3QgbG9naW4gPSBhc3luYyAoZW1haWw6IHN0cmluZywgcGFzc3dvcmQ6IHN0cmluZykgPT4ge1xyXG4gICAgdHJ5IHtcclxuICAgICAgc2V0QXV0aFN0YXRlKHByZXYgPT4gKHsgLi4ucHJldiwgbG9hZGluZzogdHJ1ZSwgZXJyb3I6IG51bGwgfSkpO1xyXG5cclxuICAgICAgY29uc3QgcmVzcG9uc2UgPSBhd2FpdCBmZXRjaCgnL2FwaS9hdXRoL2xvZ2luJywge1xyXG4gICAgICAgIG1ldGhvZDogJ1BPU1QnLFxyXG4gICAgICAgIGhlYWRlcnM6IHtcclxuICAgICAgICAgICdDb250ZW50LVR5cGUnOiAnYXBwbGljYXRpb24vanNvbidcclxuICAgICAgICB9LFxyXG4gICAgICAgIGJvZHk6IEpTT04uc3RyaW5naWZ5KHsgZW1haWwsIHBhc3N3b3JkIH0pXHJcbiAgICAgIH0pO1xyXG5cclxuICAgICAgY29uc3QgZGF0YSA9IGF3YWl0IHJlc3BvbnNlLmpzb24oKTtcclxuXHJcbiAgICAgIGlmICghcmVzcG9uc2Uub2spIHtcclxuICAgICAgICB0aHJvdyBuZXcgRXJyb3IoZGF0YS5lcnJvciB8fCAnTG9naW4gZmFpbGVkJyk7XHJcbiAgICAgIH1cclxuXHJcbiAgICAgIGlmIChkYXRhLnJlcXVpcmVzTUZBKSB7XHJcbiAgICAgICAgcmV0dXJuIHsgcmVxdWlyZXNNRkE6IHRydWUsIHVzZXI6IGRhdGEudXNlciB9O1xyXG4gICAgICB9XHJcblxyXG4gICAgICBsb2NhbFN0b3JhZ2Uuc2V0SXRlbSgnYWRtaW4tdG9rZW4nLCBkYXRhLnRva2VuKTtcclxuICAgICAgc2V0QXV0aFN0YXRlKHsgXHJcbiAgICAgICAgdXNlcjogZGF0YS51c2VyLCBcclxuICAgICAgICBsb2FkaW5nOiBmYWxzZSwgXHJcbiAgICAgICAgZXJyb3I6IG51bGwgXHJcbiAgICAgIH0pO1xyXG5cclxuICAgICAgcmV0dXJuIHsgc3VjY2VzczogdHJ1ZSwgdXNlcjogZGF0YS51c2VyIH07XHJcblxyXG4gICAgfSBjYXRjaCAoZXJyb3IpIHtcclxuICAgICAgY29uc3QgZXJyb3JNZXNzYWdlID0gZXJyb3IgaW5zdGFuY2VvZiBFcnJvciA/IGVycm9yLm1lc3NhZ2UgOiAnTG9naW4gZmFpbGVkJztcclxuICAgICAgc2V0QXV0aFN0YXRlKHByZXYgPT4gKHsgXHJcbiAgICAgICAgLi4ucHJldiwgXHJcbiAgICAgICAgbG9hZGluZzogZmFsc2UsIFxyXG4gICAgICAgIGVycm9yOiBlcnJvck1lc3NhZ2UgXHJcbiAgICAgIH0pKTtcclxuICAgICAgdGhyb3cgZXJyb3I7XHJcbiAgICB9XHJcbiAgfTtcclxuXHJcbiAgY29uc3QgdmVyaWZ5TUZBID0gYXN5bmMgKHVzZXJJZDogc3RyaW5nLCBtZmFDb2RlOiBzdHJpbmcpID0+IHtcclxuICAgIHRyeSB7XHJcbiAgICAgIHNldEF1dGhTdGF0ZShwcmV2ID0+ICh7IC4uLnByZXYsIGxvYWRpbmc6IHRydWUsIGVycm9yOiBudWxsIH0pKTtcclxuXHJcbiAgICAgIGNvbnN0IHJlc3BvbnNlID0gYXdhaXQgZmV0Y2goJy9hcGkvYXV0aC9tZmEtdmVyaWZ5Jywge1xyXG4gICAgICAgIG1ldGhvZDogJ1BPU1QnLFxyXG4gICAgICAgIGhlYWRlcnM6IHtcclxuICAgICAgICAgICdDb250ZW50LVR5cGUnOiAnYXBwbGljYXRpb24vanNvbidcclxuICAgICAgICB9LFxyXG4gICAgICAgIGJvZHk6IEpTT04uc3RyaW5naWZ5KHsgdXNlcklkLCBtZmFDb2RlIH0pXHJcbiAgICAgIH0pO1xyXG5cclxuICAgICAgY29uc3QgZGF0YSA9IGF3YWl0IHJlc3BvbnNlLmpzb24oKTtcclxuXHJcbiAgICAgIGlmICghcmVzcG9uc2Uub2spIHtcclxuICAgICAgICB0aHJvdyBuZXcgRXJyb3IoZGF0YS5lcnJvciB8fCAnTUZBIHZlcmlmaWNhdGlvbiBmYWlsZWQnKTtcclxuICAgICAgfVxyXG5cclxuICAgICAgbG9jYWxTdG9yYWdlLnNldEl0ZW0oJ2FkbWluLXRva2VuJywgZGF0YS50b2tlbik7XHJcbiAgICAgIHNldEF1dGhTdGF0ZSh7IFxyXG4gICAgICAgIHVzZXI6IGRhdGEudXNlciwgXHJcbiAgICAgICAgbG9hZGluZzogZmFsc2UsIFxyXG4gICAgICAgIGVycm9yOiBudWxsIFxyXG4gICAgICB9KTtcclxuXHJcbiAgICAgIHJldHVybiB7IHN1Y2Nlc3M6IHRydWUsIHVzZXI6IGRhdGEudXNlciB9O1xyXG5cclxuICAgIH0gY2F0Y2ggKGVycm9yKSB7XHJcbiAgICAgIGNvbnN0IGVycm9yTWVzc2FnZSA9IGVycm9yIGluc3RhbmNlb2YgRXJyb3IgPyBlcnJvci5tZXNzYWdlIDogJ01GQSB2ZXJpZmljYXRpb24gZmFpbGVkJztcclxuICAgICAgc2V0QXV0aFN0YXRlKHByZXYgPT4gKHsgXHJcbiAgICAgICAgLi4ucHJldiwgXHJcbiAgICAgICAgbG9hZGluZzogZmFsc2UsIFxyXG4gICAgICAgIGVycm9yOiBlcnJvck1lc3NhZ2UgXHJcbiAgICAgIH0pKTtcclxuICAgICAgdGhyb3cgZXJyb3I7XHJcbiAgICB9XHJcbiAgfTtcclxuXHJcbiAgY29uc3QgbG9nb3V0ID0gYXN5bmMgKCkgPT4ge1xyXG4gICAgdHJ5IHtcclxuICAgICAgY29uc3QgdG9rZW4gPSBsb2NhbFN0b3JhZ2UuZ2V0SXRlbSgnYWRtaW4tdG9rZW4nKTtcclxuICAgICAgXHJcbiAgICAgIGlmICh0b2tlbikge1xyXG4gICAgICAgIGF3YWl0IGZldGNoKCcvYXBpL2F1dGgvbG9nb3V0Jywge1xyXG4gICAgICAgICAgbWV0aG9kOiAnUE9TVCcsXHJcbiAgICAgICAgICBoZWFkZXJzOiB7XHJcbiAgICAgICAgICAgICdBdXRob3JpemF0aW9uJzogYEJlYXJlciAke3Rva2VufWBcclxuICAgICAgICAgIH1cclxuICAgICAgICB9KTtcclxuICAgICAgfVxyXG4gICAgfSBjYXRjaCAoZXJyb3IpIHtcclxuICAgICAgY29uc29sZS5lcnJvcignTG9nb3V0IGVycm9yOicsIGVycm9yKTtcclxuICAgIH0gZmluYWxseSB7XHJcbiAgICAgIGxvY2FsU3RvcmFnZS5yZW1vdmVJdGVtKCdhZG1pbi10b2tlbicpO1xyXG4gICAgICBzZXRBdXRoU3RhdGUoeyB1c2VyOiBudWxsLCBsb2FkaW5nOiBmYWxzZSwgZXJyb3I6IG51bGwgfSk7XHJcbiAgICAgIHJvdXRlci5wdXNoKCcvYWRtaW4vbG9naW4nKTtcclxuICAgIH1cclxuICB9O1xyXG5cclxuICBjb25zdCB1cGRhdGVVc2VyID0gKHVwZGF0ZWRVc2VyOiBQYXJ0aWFsPFVzZXI+KSA9PiB7XHJcbiAgICBzZXRBdXRoU3RhdGUocHJldiA9PiAoe1xyXG4gICAgICAuLi5wcmV2LFxyXG4gICAgICB1c2VyOiBwcmV2LnVzZXIgPyB7IC4uLnByZXYudXNlciwgLi4udXBkYXRlZFVzZXIgfSA6IG51bGxcclxuICAgIH0pKTtcclxuICB9O1xyXG5cclxuICBjb25zdCBoYXNQZXJtaXNzaW9uID0gKHBlcm1pc3Npb246IHN0cmluZyk6IGJvb2xlYW4gPT4ge1xyXG4gICAgaWYgKCFhdXRoU3RhdGUudXNlcikgcmV0dXJuIGZhbHNlO1xyXG4gICAgXHJcbiAgICAvLyBERVYgcm9sZSBoYXMgYWxsIHBlcm1pc3Npb25zXHJcbiAgICBpZiAoYXV0aFN0YXRlLnVzZXIucm9sZSA9PT0gJ0RFVicpIHJldHVybiB0cnVlO1xyXG4gICAgXHJcbiAgICAvLyBDaGVjayBzcGVjaWZpYyBwZXJtaXNzaW9uc1xyXG4gICAgcmV0dXJuIGF1dGhTdGF0ZS51c2VyLnBlcm1pc3Npb25zLmluY2x1ZGVzKHBlcm1pc3Npb24pO1xyXG4gIH07XHJcblxyXG4gIGNvbnN0IGhhc1JvbGUgPSAocm9sZXM6IHN0cmluZyB8IHN0cmluZ1tdKTogYm9vbGVhbiA9PiB7XHJcbiAgICBpZiAoIWF1dGhTdGF0ZS51c2VyKSByZXR1cm4gZmFsc2U7XHJcbiAgICBcclxuICAgIGNvbnN0IHJvbGVBcnJheSA9IEFycmF5LmlzQXJyYXkocm9sZXMpID8gcm9sZXMgOiBbcm9sZXNdO1xyXG4gICAgcmV0dXJuIHJvbGVBcnJheS5pbmNsdWRlcyhhdXRoU3RhdGUudXNlci5yb2xlKTtcclxuICB9O1xyXG5cclxuICBjb25zdCBpc0FkbWluID0gKCk6IGJvb2xlYW4gPT4ge1xyXG4gICAgcmV0dXJuIGhhc1JvbGUoWydERVYnLCAnQWRtaW4nXSk7XHJcbiAgfTtcclxuXHJcbiAgY29uc3QgaXNTdGFmZiA9ICgpOiBib29sZWFuID0+IHtcclxuICAgIHJldHVybiBoYXNSb2xlKFsnREVWJywgJ0FkbWluJywgJ0FydGlzdCcsICdCcmFpZGVyJ10pO1xyXG4gIH07XHJcblxyXG4gIHJldHVybiB7XHJcbiAgICB1c2VyOiBhdXRoU3RhdGUudXNlcixcclxuICAgIGxvYWRpbmc6IGF1dGhTdGF0ZS5sb2FkaW5nLFxyXG4gICAgZXJyb3I6IGF1dGhTdGF0ZS5lcnJvcixcclxuICAgIGxvZ2luLFxyXG4gICAgdmVyaWZ5TUZBLFxyXG4gICAgbG9nb3V0LFxyXG4gICAgdXBkYXRlVXNlcixcclxuICAgIGhhc1Blcm1pc3Npb24sXHJcbiAgICBoYXNSb2xlLFxyXG4gICAgaXNBZG1pbixcclxuICAgIGlzU3RhZmYsXHJcbiAgICBjaGVja0F1dGhcclxuICB9O1xyXG59XHJcbiJdLCJuYW1lcyI6WyJ1c2VTdGF0ZSIsInVzZUVmZmVjdCIsInVzZVJvdXRlciIsInVzZUF1dGgiLCJyb3V0ZXIiLCJhdXRoU3RhdGUiLCJzZXRBdXRoU3RhdGUiLCJ1c2VyIiwibG9hZGluZyIsImVycm9yIiwiY2hlY2tBdXRoIiwidG9rZW4iLCJsb2NhbFN0b3JhZ2UiLCJnZXRJdGVtIiwicmVzcG9uc2UiLCJmZXRjaCIsImhlYWRlcnMiLCJvayIsInJlbW92ZUl0ZW0iLCJkYXRhIiwianNvbiIsImNvbnNvbGUiLCJsb2dpbiIsImVtYWlsIiwicGFzc3dvcmQiLCJwcmV2IiwibWV0aG9kIiwiYm9keSIsIkpTT04iLCJzdHJpbmdpZnkiLCJFcnJvciIsInJlcXVpcmVzTUZBIiwic2V0SXRlbSIsInN1Y2Nlc3MiLCJlcnJvck1lc3NhZ2UiLCJtZXNzYWdlIiwidmVyaWZ5TUZBIiwidXNlcklkIiwibWZhQ29kZSIsImxvZ291dCIsInB1c2giLCJ1cGRhdGVVc2VyIiwidXBkYXRlZFVzZXIiLCJoYXNQZXJtaXNzaW9uIiwicGVybWlzc2lvbiIsInJvbGUiLCJwZXJtaXNzaW9ucyIsImluY2x1ZGVzIiwiaGFzUm9sZSIsInJvbGVzIiwicm9sZUFycmF5IiwiQXJyYXkiLCJpc0FycmF5IiwiaXNBZG1pbiIsImlzU3RhZmYiXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///./hooks/useAuth.ts\n");

/***/ }),

/***/ "./pages/_app.tsx":
/*!************************!*\
  !*** ./pages/_app.tsx ***!
  \************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.a(module, async (__webpack_handle_async_dependencies__, __webpack_async_result__) => { try {\n__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ AdminApp)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"react/jsx-dev-runtime\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_head__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next/head */ \"next/head\");\n/* harmony import */ var next_head__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(next_head__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! react */ \"react\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var react_toastify__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! react-toastify */ \"react-toastify\");\n/* harmony import */ var react_toastify_dist_ReactToastify_css__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! react-toastify/dist/ReactToastify.css */ \"./node_modules/react-toastify/dist/ReactToastify.css\");\n/* harmony import */ var react_toastify_dist_ReactToastify_css__WEBPACK_IMPORTED_MODULE_4___default = /*#__PURE__*/__webpack_require__.n(react_toastify_dist_ReactToastify_css__WEBPACK_IMPORTED_MODULE_4__);\n/* harmony import */ var _styles_globals_css__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! ../styles/globals.css */ \"./styles/globals.css\");\n/* harmony import */ var _styles_globals_css__WEBPACK_IMPORTED_MODULE_5___default = /*#__PURE__*/__webpack_require__.n(_styles_globals_css__WEBPACK_IMPORTED_MODULE_5__);\nvar __webpack_async_dependencies__ = __webpack_handle_async_dependencies__([react_toastify__WEBPACK_IMPORTED_MODULE_3__]);\nreact_toastify__WEBPACK_IMPORTED_MODULE_3__ = (__webpack_async_dependencies__.then ? (await __webpack_async_dependencies__)() : __webpack_async_dependencies__)[0];\n\n\n\n\n\n\nfunction AdminApp({ Component, pageProps }) {\n    (0,react__WEBPACK_IMPORTED_MODULE_2__.useEffect)(()=>{\n        // Security: Disable right-click context menu in production\n        if (false) {}\n    }, []);\n    (0,react__WEBPACK_IMPORTED_MODULE_2__.useEffect)(()=>{\n        // Security: Clear console in production\n        if (false) {}\n    }, []);\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_head__WEBPACK_IMPORTED_MODULE_1___default()), {\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"meta\", {\n                        charSet: \"utf-8\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\connectplusapps website\\\\admin.oceansoulsparkles.com.au\\\\oceansoulsparkles-admin\\\\pages\\\\_app.tsx\",\n                        lineNumber: 53,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"meta\", {\n                        name: \"viewport\",\n                        content: \"width=device-width, initial-scale=1\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\connectplusapps website\\\\admin.oceansoulsparkles.com.au\\\\oceansoulsparkles-admin\\\\pages\\\\_app.tsx\",\n                        lineNumber: 54,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"meta\", {\n                        httpEquiv: \"X-Content-Type-Options\",\n                        content: \"nosniff\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\connectplusapps website\\\\admin.oceansoulsparkles.com.au\\\\oceansoulsparkles-admin\\\\pages\\\\_app.tsx\",\n                        lineNumber: 56,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"meta\", {\n                        httpEquiv: \"X-XSS-Protection\",\n                        content: \"1; mode=block\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\connectplusapps website\\\\admin.oceansoulsparkles.com.au\\\\oceansoulsparkles-admin\\\\pages\\\\_app.tsx\",\n                        lineNumber: 57,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"meta\", {\n                        name: \"referrer\",\n                        content: \"strict-origin-when-cross-origin\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\connectplusapps website\\\\admin.oceansoulsparkles.com.au\\\\oceansoulsparkles-admin\\\\pages\\\\_app.tsx\",\n                        lineNumber: 58,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"meta\", {\n                        name: \"robots\",\n                        content: \"noindex, nofollow, noarchive, nosnippet\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\connectplusapps website\\\\admin.oceansoulsparkles.com.au\\\\oceansoulsparkles-admin\\\\pages\\\\_app.tsx\",\n                        lineNumber: 61,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"meta\", {\n                        name: \"googlebot\",\n                        content: \"noindex, nofollow\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\connectplusapps website\\\\admin.oceansoulsparkles.com.au\\\\oceansoulsparkles-admin\\\\pages\\\\_app.tsx\",\n                        lineNumber: 62,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"meta\", {\n                        name: \"description\",\n                        content: \"Ocean Soul Sparkles Admin Portal - Secure staff access only\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\connectplusapps website\\\\admin.oceansoulsparkles.com.au\\\\oceansoulsparkles-admin\\\\pages\\\\_app.tsx\",\n                        lineNumber: 63,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"link\", {\n                        rel: \"icon\",\n                        href: \"/admin/favicon.ico\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\connectplusapps website\\\\admin.oceansoulsparkles.com.au\\\\oceansoulsparkles-admin\\\\pages\\\\_app.tsx\",\n                        lineNumber: 66,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"link\", {\n                        rel: \"apple-touch-icon\",\n                        sizes: \"180x180\",\n                        href: \"/admin/apple-touch-icon.png\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\connectplusapps website\\\\admin.oceansoulsparkles.com.au\\\\oceansoulsparkles-admin\\\\pages\\\\_app.tsx\",\n                        lineNumber: 67,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"link\", {\n                        rel: \"icon\",\n                        type: \"image/png\",\n                        sizes: \"32x32\",\n                        href: \"/admin/favicon-32x32.png\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\connectplusapps website\\\\admin.oceansoulsparkles.com.au\\\\oceansoulsparkles-admin\\\\pages\\\\_app.tsx\",\n                        lineNumber: 68,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"link\", {\n                        rel: \"icon\",\n                        type: \"image/png\",\n                        sizes: \"16x16\",\n                        href: \"/admin/favicon-16x16.png\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\connectplusapps website\\\\admin.oceansoulsparkles.com.au\\\\oceansoulsparkles-admin\\\\pages\\\\_app.tsx\",\n                        lineNumber: 69,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"meta\", {\n                        name: \"theme-color\",\n                        content: \"#3788d8\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\connectplusapps website\\\\admin.oceansoulsparkles.com.au\\\\oceansoulsparkles-admin\\\\pages\\\\_app.tsx\",\n                        lineNumber: 72,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"meta\", {\n                        name: \"msapplication-TileColor\",\n                        content: \"#3788d8\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\connectplusapps website\\\\admin.oceansoulsparkles.com.au\\\\oceansoulsparkles-admin\\\\pages\\\\_app.tsx\",\n                        lineNumber: 73,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"link\", {\n                        rel: \"preconnect\",\n                        href: \"https://fonts.googleapis.com\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\connectplusapps website\\\\admin.oceansoulsparkles.com.au\\\\oceansoulsparkles-admin\\\\pages\\\\_app.tsx\",\n                        lineNumber: 76,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"link\", {\n                        rel: \"preconnect\",\n                        href: \"https://fonts.gstatic.com\",\n                        crossOrigin: \"anonymous\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\connectplusapps website\\\\admin.oceansoulsparkles.com.au\\\\oceansoulsparkles-admin\\\\pages\\\\_app.tsx\",\n                        lineNumber: 77,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"link\", {\n                        rel: \"preconnect\",\n                        href: \"https://ndlgbcsbidyhxbpqzgqp.supabase.co\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\connectplusapps website\\\\admin.oceansoulsparkles.com.au\\\\oceansoulsparkles-admin\\\\pages\\\\_app.tsx\",\n                        lineNumber: 78,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"link\", {\n                        rel: \"dns-prefetch\",\n                        href: \"https://js.squareup.com\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\connectplusapps website\\\\admin.oceansoulsparkles.com.au\\\\oceansoulsparkles-admin\\\\pages\\\\_app.tsx\",\n                        lineNumber: 80,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"link\", {\n                        rel: \"dns-prefetch\",\n                        href: \"https://api.onesignal.com\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\connectplusapps website\\\\admin.oceansoulsparkles.com.au\\\\oceansoulsparkles-admin\\\\pages\\\\_app.tsx\",\n                        lineNumber: 81,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"title\", {\n                        children: \"Ocean Soul Sparkles Admin Portal\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\connectplusapps website\\\\admin.oceansoulsparkles.com.au\\\\oceansoulsparkles-admin\\\\pages\\\\_app.tsx\",\n                        lineNumber: 84,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\connectplusapps website\\\\admin.oceansoulsparkles.com.au\\\\oceansoulsparkles-admin\\\\pages\\\\_app.tsx\",\n                lineNumber: 51,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(Component, {\n                ...pageProps\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\connectplusapps website\\\\admin.oceansoulsparkles.com.au\\\\oceansoulsparkles-admin\\\\pages\\\\_app.tsx\",\n                lineNumber: 88,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_toastify__WEBPACK_IMPORTED_MODULE_3__.ToastContainer, {\n                position: \"top-right\",\n                autoClose: 5000,\n                hideProgressBar: false,\n                newestOnTop: false,\n                closeOnClick: true,\n                rtl: false,\n                pauseOnFocusLoss: true,\n                draggable: true,\n                pauseOnHover: true,\n                theme: \"light\",\n                toastStyle: {\n                    fontFamily: \"inherit\",\n                    fontSize: \"14px\"\n                }\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\connectplusapps website\\\\admin.oceansoulsparkles.com.au\\\\oceansoulsparkles-admin\\\\pages\\\\_app.tsx\",\n                lineNumber: 91,\n                columnNumber: 7\n            }, this),\n             false && /*#__PURE__*/ 0,\n             true && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                style: {\n                    position: \"fixed\",\n                    top: \"0\",\n                    left: \"0\",\n                    right: \"0\",\n                    background: \"#ff6b6b\",\n                    color: \"white\",\n                    padding: \"4px\",\n                    textAlign: \"center\",\n                    fontSize: \"12px\",\n                    fontWeight: \"bold\",\n                    zIndex: 10000\n                },\n                children: \"\\uD83D\\uDEA7 DEVELOPMENT MODE - ADMIN PORTAL \\uD83D\\uDEA7\"\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\connectplusapps website\\\\admin.oceansoulsparkles.com.au\\\\oceansoulsparkles-admin\\\\pages\\\\_app.tsx\",\n                lineNumber: 132,\n                columnNumber: 9\n            }, this)\n        ]\n    }, void 0, true);\n}\n\n__webpack_async_result__();\n} catch(e) { __webpack_async_result__(e); } });//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///./pages/_app.tsx\n");

/***/ }),

/***/ "./pages/admin/receipts.js":
/*!*********************************!*\
  !*** ./pages/admin/receipts.js ***!
  \*********************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.a(module, async (__webpack_handle_async_dependencies__, __webpack_async_result__) => { try {\n__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ ReceiptsPage)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"react/jsx-dev-runtime\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"react\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var next_head__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/head */ \"next/head\");\n/* harmony import */ var next_head__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(next_head__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var _components_admin_AdminLayout__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/components/admin/AdminLayout */ \"./components/admin/AdminLayout.tsx\");\n/* harmony import */ var _components_admin_pos_ReceiptCustomizer__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/components/admin/pos/ReceiptCustomizer */ \"./components/admin/pos/ReceiptCustomizer.js\");\n/* harmony import */ var _hooks_useAuth__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @/hooks/useAuth */ \"./hooks/useAuth.ts\");\n/* harmony import */ var _styles_admin_Receipts_module_css__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! @/styles/admin/Receipts.module.css */ \"./styles/admin/Receipts.module.css\");\n/* harmony import */ var _styles_admin_Receipts_module_css__WEBPACK_IMPORTED_MODULE_6___default = /*#__PURE__*/__webpack_require__.n(_styles_admin_Receipts_module_css__WEBPACK_IMPORTED_MODULE_6__);\nvar __webpack_async_dependencies__ = __webpack_handle_async_dependencies__([_components_admin_AdminLayout__WEBPACK_IMPORTED_MODULE_3__]);\n_components_admin_AdminLayout__WEBPACK_IMPORTED_MODULE_3__ = (__webpack_async_dependencies__.then ? (await __webpack_async_dependencies__)() : __webpack_async_dependencies__)[0];\n\n\n\n\n\n\n\n/**\n * Receipt Management Page\n * Allows admins to customize receipt templates and manage receipt settings\n */ function ReceiptsPage() {\n    const { user, loading: authLoading } = (0,_hooks_useAuth__WEBPACK_IMPORTED_MODULE_5__.useAuth)();\n    const [selectedTemplate, setSelectedTemplate] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [activeTab, setActiveTab] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"templates\") // 'templates', 'settings'\n    ;\n    const [loading, setLoading] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [message, setMessage] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const handleTemplateSelect = (template)=>{\n        setSelectedTemplate(template);\n    };\n    const showMessage = (text, type = \"success\")=>{\n        setMessage({\n            text,\n            type\n        });\n        setTimeout(()=>setMessage(null), 5000);\n    };\n    if (authLoading) {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_admin_AdminLayout__WEBPACK_IMPORTED_MODULE_3__[\"default\"], {\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: (_styles_admin_Receipts_module_css__WEBPACK_IMPORTED_MODULE_6___default().loading),\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: (_styles_admin_Receipts_module_css__WEBPACK_IMPORTED_MODULE_6___default().loadingSpinner)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\connectplusapps website\\\\admin.oceansoulsparkles.com.au\\\\oceansoulsparkles-admin\\\\pages\\\\admin\\\\receipts.js\",\n                        lineNumber: 32,\n                        columnNumber: 11\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                        children: \"Loading...\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\connectplusapps website\\\\admin.oceansoulsparkles.com.au\\\\oceansoulsparkles-admin\\\\pages\\\\admin\\\\receipts.js\",\n                        lineNumber: 33,\n                        columnNumber: 11\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\connectplusapps website\\\\admin.oceansoulsparkles.com.au\\\\oceansoulsparkles-admin\\\\pages\\\\admin\\\\receipts.js\",\n                lineNumber: 31,\n                columnNumber: 9\n            }, this)\n        }, void 0, false, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\connectplusapps website\\\\admin.oceansoulsparkles.com.au\\\\oceansoulsparkles-admin\\\\pages\\\\admin\\\\receipts.js\",\n            lineNumber: 30,\n            columnNumber: 7\n        }, this);\n    }\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_admin_AdminLayout__WEBPACK_IMPORTED_MODULE_3__[\"default\"], {\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_head__WEBPACK_IMPORTED_MODULE_2___default()), {\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"title\", {\n                    children: \"Receipt Management - Ocean Soul Sparkles Admin\"\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\connectplusapps website\\\\admin.oceansoulsparkles.com.au\\\\oceansoulsparkles-admin\\\\pages\\\\admin\\\\receipts.js\",\n                    lineNumber: 42,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\connectplusapps website\\\\admin.oceansoulsparkles.com.au\\\\oceansoulsparkles-admin\\\\pages\\\\admin\\\\receipts.js\",\n                lineNumber: 41,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: (_styles_admin_Receipts_module_css__WEBPACK_IMPORTED_MODULE_6___default().receiptsPage),\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: (_styles_admin_Receipts_module_css__WEBPACK_IMPORTED_MODULE_6___default().header),\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: (_styles_admin_Receipts_module_css__WEBPACK_IMPORTED_MODULE_6___default().headerContent),\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                                    children: \"Receipt Management\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\connectplusapps website\\\\admin.oceansoulsparkles.com.au\\\\oceansoulsparkles-admin\\\\pages\\\\admin\\\\receipts.js\",\n                                    lineNumber: 48,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                    children: \"Customize receipt templates and manage receipt settings for your POS system.\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\connectplusapps website\\\\admin.oceansoulsparkles.com.au\\\\oceansoulsparkles-admin\\\\pages\\\\admin\\\\receipts.js\",\n                                    lineNumber: 49,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\connectplusapps website\\\\admin.oceansoulsparkles.com.au\\\\oceansoulsparkles-admin\\\\pages\\\\admin\\\\receipts.js\",\n                            lineNumber: 47,\n                            columnNumber: 11\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\connectplusapps website\\\\admin.oceansoulsparkles.com.au\\\\oceansoulsparkles-admin\\\\pages\\\\admin\\\\receipts.js\",\n                        lineNumber: 46,\n                        columnNumber: 9\n                    }, this),\n                    message && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: `${(_styles_admin_Receipts_module_css__WEBPACK_IMPORTED_MODULE_6___default().message)} ${(_styles_admin_Receipts_module_css__WEBPACK_IMPORTED_MODULE_6___default())[message.type]}`,\n                        children: message.text\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\connectplusapps website\\\\admin.oceansoulsparkles.com.au\\\\oceansoulsparkles-admin\\\\pages\\\\admin\\\\receipts.js\",\n                        lineNumber: 54,\n                        columnNumber: 11\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: (_styles_admin_Receipts_module_css__WEBPACK_IMPORTED_MODULE_6___default().tabNavigation),\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                className: `${(_styles_admin_Receipts_module_css__WEBPACK_IMPORTED_MODULE_6___default().tabButton)} ${activeTab === \"templates\" ? (_styles_admin_Receipts_module_css__WEBPACK_IMPORTED_MODULE_6___default().active) : \"\"}`,\n                                onClick: ()=>setActiveTab(\"templates\"),\n                                children: \"\\uD83D\\uDCC4 Templates\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\connectplusapps website\\\\admin.oceansoulsparkles.com.au\\\\oceansoulsparkles-admin\\\\pages\\\\admin\\\\receipts.js\",\n                                lineNumber: 60,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                className: `${(_styles_admin_Receipts_module_css__WEBPACK_IMPORTED_MODULE_6___default().tabButton)} ${activeTab === \"settings\" ? (_styles_admin_Receipts_module_css__WEBPACK_IMPORTED_MODULE_6___default().active) : \"\"}`,\n                                onClick: ()=>setActiveTab(\"settings\"),\n                                children: \"⚙️ Settings\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\connectplusapps website\\\\admin.oceansoulsparkles.com.au\\\\oceansoulsparkles-admin\\\\pages\\\\admin\\\\receipts.js\",\n                                lineNumber: 66,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\connectplusapps website\\\\admin.oceansoulsparkles.com.au\\\\oceansoulsparkles-admin\\\\pages\\\\admin\\\\receipts.js\",\n                        lineNumber: 59,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: (_styles_admin_Receipts_module_css__WEBPACK_IMPORTED_MODULE_6___default().tabContent),\n                        children: [\n                            activeTab === \"templates\" && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: (_styles_admin_Receipts_module_css__WEBPACK_IMPORTED_MODULE_6___default().templatesTab),\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: (_styles_admin_Receipts_module_css__WEBPACK_IMPORTED_MODULE_6___default().tabHeader),\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                                                children: \"Receipt Templates\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\connectplusapps website\\\\admin.oceansoulsparkles.com.au\\\\oceansoulsparkles-admin\\\\pages\\\\admin\\\\receipts.js\",\n                                                lineNumber: 78,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                children: \"Choose and customize receipt templates for different transaction types.\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\connectplusapps website\\\\admin.oceansoulsparkles.com.au\\\\oceansoulsparkles-admin\\\\pages\\\\admin\\\\receipts.js\",\n                                                lineNumber: 79,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\connectplusapps website\\\\admin.oceansoulsparkles.com.au\\\\oceansoulsparkles-admin\\\\pages\\\\admin\\\\receipts.js\",\n                                        lineNumber: 77,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_admin_pos_ReceiptCustomizer__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n                                        onTemplateSelect: handleTemplateSelect,\n                                        selectedTemplate: selectedTemplate,\n                                        showPreview: true\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\connectplusapps website\\\\admin.oceansoulsparkles.com.au\\\\oceansoulsparkles-admin\\\\pages\\\\admin\\\\receipts.js\",\n                                        lineNumber: 82,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\connectplusapps website\\\\admin.oceansoulsparkles.com.au\\\\oceansoulsparkles-admin\\\\pages\\\\admin\\\\receipts.js\",\n                                lineNumber: 76,\n                                columnNumber: 13\n                            }, this),\n                            activeTab === \"settings\" && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: (_styles_admin_Receipts_module_css__WEBPACK_IMPORTED_MODULE_6___default().settingsTab),\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: (_styles_admin_Receipts_module_css__WEBPACK_IMPORTED_MODULE_6___default().tabHeader),\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                                                children: \"Receipt Settings\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\connectplusapps website\\\\admin.oceansoulsparkles.com.au\\\\oceansoulsparkles-admin\\\\pages\\\\admin\\\\receipts.js\",\n                                                lineNumber: 93,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                children: \"Configure global receipt settings and business information.\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\connectplusapps website\\\\admin.oceansoulsparkles.com.au\\\\oceansoulsparkles-admin\\\\pages\\\\admin\\\\receipts.js\",\n                                                lineNumber: 94,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\connectplusapps website\\\\admin.oceansoulsparkles.com.au\\\\oceansoulsparkles-admin\\\\pages\\\\admin\\\\receipts.js\",\n                                        lineNumber: 92,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(ReceiptSettings, {\n                                        onMessage: showMessage\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\connectplusapps website\\\\admin.oceansoulsparkles.com.au\\\\oceansoulsparkles-admin\\\\pages\\\\admin\\\\receipts.js\",\n                                        lineNumber: 97,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\connectplusapps website\\\\admin.oceansoulsparkles.com.au\\\\oceansoulsparkles-admin\\\\pages\\\\admin\\\\receipts.js\",\n                                lineNumber: 91,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\connectplusapps website\\\\admin.oceansoulsparkles.com.au\\\\oceansoulsparkles-admin\\\\pages\\\\admin\\\\receipts.js\",\n                        lineNumber: 74,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\connectplusapps website\\\\admin.oceansoulsparkles.com.au\\\\oceansoulsparkles-admin\\\\pages\\\\admin\\\\receipts.js\",\n                lineNumber: 45,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\connectplusapps website\\\\admin.oceansoulsparkles.com.au\\\\oceansoulsparkles-admin\\\\pages\\\\admin\\\\receipts.js\",\n        lineNumber: 40,\n        columnNumber: 5\n    }, this);\n}\n/**\n * Receipt Settings Component\n * Manages global receipt settings\n */ function ReceiptSettings({ onMessage }) {\n    const [settings, setSettings] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)({\n        autoGenerateReceipts: true,\n        emailReceiptsToCustomers: true,\n        printReceiptsAutomatically: false,\n        receiptNumberPrefix: \"OSS\",\n        includeQRCode: false,\n        defaultTemplate: \"\",\n        businessInfo: {\n            name: \"Ocean Soul Sparkles\",\n            address: \"\",\n            phone: \"\",\n            email: \"\",\n            website: \"\",\n            abn: \"\"\n        }\n    });\n    const [loading, setLoading] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [templates, setTemplates] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        loadSettings();\n        loadTemplates();\n    }, []);\n    const loadSettings = async ()=>{\n        try {\n            // Load settings from system_settings table\n            const response = await fetch(\"/api/admin/settings\", {\n                headers: {\n                    \"Authorization\": `Bearer ${localStorage.getItem(\"admin-token\")}`\n                }\n            });\n            if (response.ok) {\n                const data = await response.json();\n                // Extract receipt-related settings\n                const receiptSettings = {\n                    autoGenerateReceipts: data.receipt?.autoGenerateReceipts !== \"false\",\n                    emailReceiptsToCustomers: data.receipt?.emailReceiptsToCustomers !== \"false\",\n                    printReceiptsAutomatically: data.receipt?.printReceiptsAutomatically === \"true\",\n                    receiptNumberPrefix: data.receipt?.receiptNumberPrefix || \"OSS\",\n                    includeQRCode: data.receipt?.includeQRCode === \"true\",\n                    defaultTemplate: data.receipt?.defaultTemplate || \"\",\n                    businessInfo: {\n                        name: data.general?.businessName || \"Ocean Soul Sparkles\",\n                        address: data.general?.businessAddress || \"\",\n                        phone: data.general?.businessPhone || \"\",\n                        email: data.general?.businessEmail || \"\",\n                        website: data.general?.businessWebsite || \"\",\n                        abn: data.general?.businessABN || \"\"\n                    }\n                };\n                setSettings(receiptSettings);\n            }\n        } catch (error) {\n            console.error(\"Error loading receipt settings:\", error);\n        }\n    };\n    const loadTemplates = async ()=>{\n        try {\n            const response = await fetch(\"/api/admin/receipts\", {\n                headers: {\n                    \"Authorization\": `Bearer ${localStorage.getItem(\"admin-token\")}`\n                }\n            });\n            if (response.ok) {\n                const data = await response.json();\n                setTemplates(data.templates || []);\n            }\n        } catch (error) {\n            console.error(\"Error loading templates:\", error);\n        }\n    };\n    const handleSettingChange = (key, value)=>{\n        if (key.includes(\".\")) {\n            const [parent, child] = key.split(\".\");\n            setSettings((prev)=>({\n                    ...prev,\n                    [parent]: {\n                        ...prev[parent],\n                        [child]: value\n                    }\n                }));\n        } else {\n            setSettings((prev)=>({\n                    ...prev,\n                    [key]: value\n                }));\n        }\n    };\n    const saveSettings = async ()=>{\n        try {\n            setLoading(true);\n            // Save settings to system_settings table\n            const settingsToSave = {\n                \"receipt.autoGenerateReceipts\": settings.autoGenerateReceipts.toString(),\n                \"receipt.emailReceiptsToCustomers\": settings.emailReceiptsToCustomers.toString(),\n                \"receipt.printReceiptsAutomatically\": settings.printReceiptsAutomatically.toString(),\n                \"receipt.receiptNumberPrefix\": settings.receiptNumberPrefix,\n                \"receipt.includeQRCode\": settings.includeQRCode.toString(),\n                \"receipt.defaultTemplate\": settings.defaultTemplate,\n                \"general.businessName\": settings.businessInfo.name,\n                \"general.businessAddress\": settings.businessInfo.address,\n                \"general.businessPhone\": settings.businessInfo.phone,\n                \"general.businessEmail\": settings.businessInfo.email,\n                \"general.businessWebsite\": settings.businessInfo.website,\n                \"general.businessABN\": settings.businessInfo.abn\n            };\n            const response = await fetch(\"/api/admin/settings\", {\n                method: \"PUT\",\n                headers: {\n                    \"Content-Type\": \"application/json\",\n                    \"Authorization\": `Bearer ${localStorage.getItem(\"admin-token\")}`\n                },\n                body: JSON.stringify({\n                    settings: settingsToSave\n                })\n            });\n            if (!response.ok) {\n                throw new Error(\"Failed to save settings\");\n            }\n            onMessage?.(\"Receipt settings saved successfully!\", \"success\");\n        } catch (error) {\n            console.error(\"Error saving settings:\", error);\n            onMessage?.(\"Failed to save settings. Please try again.\", \"error\");\n        } finally{\n            setLoading(false);\n        }\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: (_styles_admin_Receipts_module_css__WEBPACK_IMPORTED_MODULE_6___default().receiptSettings),\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: (_styles_admin_Receipts_module_css__WEBPACK_IMPORTED_MODULE_6___default().settingsGrid),\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: (_styles_admin_Receipts_module_css__WEBPACK_IMPORTED_MODULE_6___default().settingsSection),\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                children: \"Receipt Generation\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\connectplusapps website\\\\admin.oceansoulsparkles.com.au\\\\oceansoulsparkles-admin\\\\pages\\\\admin\\\\receipts.js\",\n                                lineNumber: 251,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: (_styles_admin_Receipts_module_css__WEBPACK_IMPORTED_MODULE_6___default().settingGroup),\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                    className: (_styles_admin_Receipts_module_css__WEBPACK_IMPORTED_MODULE_6___default().checkboxLabel),\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                            type: \"checkbox\",\n                                            checked: settings.autoGenerateReceipts,\n                                            onChange: (e)=>handleSettingChange(\"autoGenerateReceipts\", e.target.checked)\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\connectplusapps website\\\\admin.oceansoulsparkles.com.au\\\\oceansoulsparkles-admin\\\\pages\\\\admin\\\\receipts.js\",\n                                            lineNumber: 254,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                            children: \"Automatically generate receipts for all transactions\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\connectplusapps website\\\\admin.oceansoulsparkles.com.au\\\\oceansoulsparkles-admin\\\\pages\\\\admin\\\\receipts.js\",\n                                            lineNumber: 259,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\connectplusapps website\\\\admin.oceansoulsparkles.com.au\\\\oceansoulsparkles-admin\\\\pages\\\\admin\\\\receipts.js\",\n                                    lineNumber: 253,\n                                    columnNumber: 13\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\connectplusapps website\\\\admin.oceansoulsparkles.com.au\\\\oceansoulsparkles-admin\\\\pages\\\\admin\\\\receipts.js\",\n                                lineNumber: 252,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: (_styles_admin_Receipts_module_css__WEBPACK_IMPORTED_MODULE_6___default().settingGroup),\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                    className: (_styles_admin_Receipts_module_css__WEBPACK_IMPORTED_MODULE_6___default().checkboxLabel),\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                            type: \"checkbox\",\n                                            checked: settings.emailReceiptsToCustomers,\n                                            onChange: (e)=>handleSettingChange(\"emailReceiptsToCustomers\", e.target.checked)\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\connectplusapps website\\\\admin.oceansoulsparkles.com.au\\\\oceansoulsparkles-admin\\\\pages\\\\admin\\\\receipts.js\",\n                                            lineNumber: 265,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                            children: \"Email receipts to customers automatically\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\connectplusapps website\\\\admin.oceansoulsparkles.com.au\\\\oceansoulsparkles-admin\\\\pages\\\\admin\\\\receipts.js\",\n                                            lineNumber: 270,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\connectplusapps website\\\\admin.oceansoulsparkles.com.au\\\\oceansoulsparkles-admin\\\\pages\\\\admin\\\\receipts.js\",\n                                    lineNumber: 264,\n                                    columnNumber: 13\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\connectplusapps website\\\\admin.oceansoulsparkles.com.au\\\\oceansoulsparkles-admin\\\\pages\\\\admin\\\\receipts.js\",\n                                lineNumber: 263,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: (_styles_admin_Receipts_module_css__WEBPACK_IMPORTED_MODULE_6___default().settingGroup),\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                    className: (_styles_admin_Receipts_module_css__WEBPACK_IMPORTED_MODULE_6___default().checkboxLabel),\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                            type: \"checkbox\",\n                                            checked: settings.printReceiptsAutomatically,\n                                            onChange: (e)=>handleSettingChange(\"printReceiptsAutomatically\", e.target.checked)\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\connectplusapps website\\\\admin.oceansoulsparkles.com.au\\\\oceansoulsparkles-admin\\\\pages\\\\admin\\\\receipts.js\",\n                                            lineNumber: 276,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                            children: \"Print receipts automatically\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\connectplusapps website\\\\admin.oceansoulsparkles.com.au\\\\oceansoulsparkles-admin\\\\pages\\\\admin\\\\receipts.js\",\n                                            lineNumber: 281,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\connectplusapps website\\\\admin.oceansoulsparkles.com.au\\\\oceansoulsparkles-admin\\\\pages\\\\admin\\\\receipts.js\",\n                                    lineNumber: 275,\n                                    columnNumber: 13\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\connectplusapps website\\\\admin.oceansoulsparkles.com.au\\\\oceansoulsparkles-admin\\\\pages\\\\admin\\\\receipts.js\",\n                                lineNumber: 274,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\connectplusapps website\\\\admin.oceansoulsparkles.com.au\\\\oceansoulsparkles-admin\\\\pages\\\\admin\\\\receipts.js\",\n                        lineNumber: 250,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: (_styles_admin_Receipts_module_css__WEBPACK_IMPORTED_MODULE_6___default().settingsSection),\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                children: \"Receipt Configuration\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\connectplusapps website\\\\admin.oceansoulsparkles.com.au\\\\oceansoulsparkles-admin\\\\pages\\\\admin\\\\receipts.js\",\n                                lineNumber: 287,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: (_styles_admin_Receipts_module_css__WEBPACK_IMPORTED_MODULE_6___default().settingGroup),\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                        children: \"Receipt Number Prefix\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\connectplusapps website\\\\admin.oceansoulsparkles.com.au\\\\oceansoulsparkles-admin\\\\pages\\\\admin\\\\receipts.js\",\n                                        lineNumber: 289,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                        type: \"text\",\n                                        value: settings.receiptNumberPrefix,\n                                        onChange: (e)=>handleSettingChange(\"receiptNumberPrefix\", e.target.value),\n                                        placeholder: \"OSS\",\n                                        maxLength: 10\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\connectplusapps website\\\\admin.oceansoulsparkles.com.au\\\\oceansoulsparkles-admin\\\\pages\\\\admin\\\\receipts.js\",\n                                        lineNumber: 290,\n                                        columnNumber: 13\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\connectplusapps website\\\\admin.oceansoulsparkles.com.au\\\\oceansoulsparkles-admin\\\\pages\\\\admin\\\\receipts.js\",\n                                lineNumber: 288,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: (_styles_admin_Receipts_module_css__WEBPACK_IMPORTED_MODULE_6___default().settingGroup),\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                        children: \"Default Template\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\connectplusapps website\\\\admin.oceansoulsparkles.com.au\\\\oceansoulsparkles-admin\\\\pages\\\\admin\\\\receipts.js\",\n                                        lineNumber: 300,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"select\", {\n                                        value: settings.defaultTemplate,\n                                        onChange: (e)=>handleSettingChange(\"defaultTemplate\", e.target.value),\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                value: \"\",\n                                                children: \"Select default template\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\connectplusapps website\\\\admin.oceansoulsparkles.com.au\\\\oceansoulsparkles-admin\\\\pages\\\\admin\\\\receipts.js\",\n                                                lineNumber: 305,\n                                                columnNumber: 15\n                                            }, this),\n                                            templates.map((template)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                    value: template.id,\n                                                    children: template.name\n                                                }, template.id, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\connectplusapps website\\\\admin.oceansoulsparkles.com.au\\\\oceansoulsparkles-admin\\\\pages\\\\admin\\\\receipts.js\",\n                                                    lineNumber: 307,\n                                                    columnNumber: 17\n                                                }, this))\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\connectplusapps website\\\\admin.oceansoulsparkles.com.au\\\\oceansoulsparkles-admin\\\\pages\\\\admin\\\\receipts.js\",\n                                        lineNumber: 301,\n                                        columnNumber: 13\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\connectplusapps website\\\\admin.oceansoulsparkles.com.au\\\\oceansoulsparkles-admin\\\\pages\\\\admin\\\\receipts.js\",\n                                lineNumber: 299,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: (_styles_admin_Receipts_module_css__WEBPACK_IMPORTED_MODULE_6___default().settingGroup),\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                    className: (_styles_admin_Receipts_module_css__WEBPACK_IMPORTED_MODULE_6___default().checkboxLabel),\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                            type: \"checkbox\",\n                                            checked: settings.includeQRCode,\n                                            onChange: (e)=>handleSettingChange(\"includeQRCode\", e.target.checked)\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\connectplusapps website\\\\admin.oceansoulsparkles.com.au\\\\oceansoulsparkles-admin\\\\pages\\\\admin\\\\receipts.js\",\n                                            lineNumber: 316,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                            children: \"Include QR code on receipts\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\connectplusapps website\\\\admin.oceansoulsparkles.com.au\\\\oceansoulsparkles-admin\\\\pages\\\\admin\\\\receipts.js\",\n                                            lineNumber: 321,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\connectplusapps website\\\\admin.oceansoulsparkles.com.au\\\\oceansoulsparkles-admin\\\\pages\\\\admin\\\\receipts.js\",\n                                    lineNumber: 315,\n                                    columnNumber: 13\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\connectplusapps website\\\\admin.oceansoulsparkles.com.au\\\\oceansoulsparkles-admin\\\\pages\\\\admin\\\\receipts.js\",\n                                lineNumber: 314,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\connectplusapps website\\\\admin.oceansoulsparkles.com.au\\\\oceansoulsparkles-admin\\\\pages\\\\admin\\\\receipts.js\",\n                        lineNumber: 286,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: (_styles_admin_Receipts_module_css__WEBPACK_IMPORTED_MODULE_6___default().settingsSection),\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                children: \"Business Information\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\connectplusapps website\\\\admin.oceansoulsparkles.com.au\\\\oceansoulsparkles-admin\\\\pages\\\\admin\\\\receipts.js\",\n                                lineNumber: 327,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: (_styles_admin_Receipts_module_css__WEBPACK_IMPORTED_MODULE_6___default().settingGroup),\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                        children: \"Business Name\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\connectplusapps website\\\\admin.oceansoulsparkles.com.au\\\\oceansoulsparkles-admin\\\\pages\\\\admin\\\\receipts.js\",\n                                        lineNumber: 329,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                        type: \"text\",\n                                        value: settings.businessInfo.name,\n                                        onChange: (e)=>handleSettingChange(\"businessInfo.name\", e.target.value),\n                                        placeholder: \"Ocean Soul Sparkles\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\connectplusapps website\\\\admin.oceansoulsparkles.com.au\\\\oceansoulsparkles-admin\\\\pages\\\\admin\\\\receipts.js\",\n                                        lineNumber: 330,\n                                        columnNumber: 13\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\connectplusapps website\\\\admin.oceansoulsparkles.com.au\\\\oceansoulsparkles-admin\\\\pages\\\\admin\\\\receipts.js\",\n                                lineNumber: 328,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: (_styles_admin_Receipts_module_css__WEBPACK_IMPORTED_MODULE_6___default().settingGroup),\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                        children: \"Address\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\connectplusapps website\\\\admin.oceansoulsparkles.com.au\\\\oceansoulsparkles-admin\\\\pages\\\\admin\\\\receipts.js\",\n                                        lineNumber: 339,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"textarea\", {\n                                        value: settings.businessInfo.address,\n                                        onChange: (e)=>handleSettingChange(\"businessInfo.address\", e.target.value),\n                                        placeholder: \"Business address\",\n                                        rows: 3\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\connectplusapps website\\\\admin.oceansoulsparkles.com.au\\\\oceansoulsparkles-admin\\\\pages\\\\admin\\\\receipts.js\",\n                                        lineNumber: 340,\n                                        columnNumber: 13\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\connectplusapps website\\\\admin.oceansoulsparkles.com.au\\\\oceansoulsparkles-admin\\\\pages\\\\admin\\\\receipts.js\",\n                                lineNumber: 338,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: (_styles_admin_Receipts_module_css__WEBPACK_IMPORTED_MODULE_6___default().settingGroup),\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                        children: \"Phone\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\connectplusapps website\\\\admin.oceansoulsparkles.com.au\\\\oceansoulsparkles-admin\\\\pages\\\\admin\\\\receipts.js\",\n                                        lineNumber: 349,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                        type: \"text\",\n                                        value: settings.businessInfo.phone,\n                                        onChange: (e)=>handleSettingChange(\"businessInfo.phone\", e.target.value),\n                                        placeholder: \"+61 XXX XXX XXX\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\connectplusapps website\\\\admin.oceansoulsparkles.com.au\\\\oceansoulsparkles-admin\\\\pages\\\\admin\\\\receipts.js\",\n                                        lineNumber: 350,\n                                        columnNumber: 13\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\connectplusapps website\\\\admin.oceansoulsparkles.com.au\\\\oceansoulsparkles-admin\\\\pages\\\\admin\\\\receipts.js\",\n                                lineNumber: 348,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: (_styles_admin_Receipts_module_css__WEBPACK_IMPORTED_MODULE_6___default().settingGroup),\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                        children: \"Email\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\connectplusapps website\\\\admin.oceansoulsparkles.com.au\\\\oceansoulsparkles-admin\\\\pages\\\\admin\\\\receipts.js\",\n                                        lineNumber: 359,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                        type: \"email\",\n                                        value: settings.businessInfo.email,\n                                        onChange: (e)=>handleSettingChange(\"businessInfo.email\", e.target.value),\n                                        placeholder: \"<EMAIL>\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\connectplusapps website\\\\admin.oceansoulsparkles.com.au\\\\oceansoulsparkles-admin\\\\pages\\\\admin\\\\receipts.js\",\n                                        lineNumber: 360,\n                                        columnNumber: 13\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\connectplusapps website\\\\admin.oceansoulsparkles.com.au\\\\oceansoulsparkles-admin\\\\pages\\\\admin\\\\receipts.js\",\n                                lineNumber: 358,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: (_styles_admin_Receipts_module_css__WEBPACK_IMPORTED_MODULE_6___default().settingGroup),\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                        children: \"Website\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\connectplusapps website\\\\admin.oceansoulsparkles.com.au\\\\oceansoulsparkles-admin\\\\pages\\\\admin\\\\receipts.js\",\n                                        lineNumber: 369,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                        type: \"text\",\n                                        value: settings.businessInfo.website,\n                                        onChange: (e)=>handleSettingChange(\"businessInfo.website\", e.target.value),\n                                        placeholder: \"oceansoulsparkles.com.au\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\connectplusapps website\\\\admin.oceansoulsparkles.com.au\\\\oceansoulsparkles-admin\\\\pages\\\\admin\\\\receipts.js\",\n                                        lineNumber: 370,\n                                        columnNumber: 13\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\connectplusapps website\\\\admin.oceansoulsparkles.com.au\\\\oceansoulsparkles-admin\\\\pages\\\\admin\\\\receipts.js\",\n                                lineNumber: 368,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: (_styles_admin_Receipts_module_css__WEBPACK_IMPORTED_MODULE_6___default().settingGroup),\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                        children: \"ABN\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\connectplusapps website\\\\admin.oceansoulsparkles.com.au\\\\oceansoulsparkles-admin\\\\pages\\\\admin\\\\receipts.js\",\n                                        lineNumber: 379,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                        type: \"text\",\n                                        value: settings.businessInfo.abn,\n                                        onChange: (e)=>handleSettingChange(\"businessInfo.abn\", e.target.value),\n                                        placeholder: \"**************\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\connectplusapps website\\\\admin.oceansoulsparkles.com.au\\\\oceansoulsparkles-admin\\\\pages\\\\admin\\\\receipts.js\",\n                                        lineNumber: 380,\n                                        columnNumber: 13\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\connectplusapps website\\\\admin.oceansoulsparkles.com.au\\\\oceansoulsparkles-admin\\\\pages\\\\admin\\\\receipts.js\",\n                                lineNumber: 378,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\connectplusapps website\\\\admin.oceansoulsparkles.com.au\\\\oceansoulsparkles-admin\\\\pages\\\\admin\\\\receipts.js\",\n                        lineNumber: 326,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\connectplusapps website\\\\admin.oceansoulsparkles.com.au\\\\oceansoulsparkles-admin\\\\pages\\\\admin\\\\receipts.js\",\n                lineNumber: 249,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: (_styles_admin_Receipts_module_css__WEBPACK_IMPORTED_MODULE_6___default().settingsActions),\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                    onClick: saveSettings,\n                    disabled: loading,\n                    className: (_styles_admin_Receipts_module_css__WEBPACK_IMPORTED_MODULE_6___default().saveButton),\n                    children: loading ? \"Saving...\" : \"Save Settings\"\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\connectplusapps website\\\\admin.oceansoulsparkles.com.au\\\\oceansoulsparkles-admin\\\\pages\\\\admin\\\\receipts.js\",\n                    lineNumber: 391,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\connectplusapps website\\\\admin.oceansoulsparkles.com.au\\\\oceansoulsparkles-admin\\\\pages\\\\admin\\\\receipts.js\",\n                lineNumber: 390,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\connectplusapps website\\\\admin.oceansoulsparkles.com.au\\\\oceansoulsparkles-admin\\\\pages\\\\admin\\\\receipts.js\",\n        lineNumber: 248,\n        columnNumber: 5\n    }, this);\n}\n\n__webpack_async_result__();\n} catch(e) { __webpack_async_result__(e); } });//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///./pages/admin/receipts.js\n");

/***/ }),

/***/ "./styles/globals.css":
/*!****************************!*\
  !*** ./styles/globals.css ***!
  \****************************/
/***/ (() => {



/***/ }),

/***/ "next/dist/compiled/next-server/pages.runtime.dev.js":
/*!**********************************************************************!*\
  !*** external "next/dist/compiled/next-server/pages.runtime.dev.js" ***!
  \**********************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/compiled/next-server/pages.runtime.dev.js");

/***/ }),

/***/ "next/head":
/*!****************************!*\
  !*** external "next/head" ***!
  \****************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/head");

/***/ }),

/***/ "react":
/*!************************!*\
  !*** external "react" ***!
  \************************/
/***/ ((module) => {

"use strict";
module.exports = require("react");

/***/ }),

/***/ "react-dom":
/*!****************************!*\
  !*** external "react-dom" ***!
  \****************************/
/***/ ((module) => {

"use strict";
module.exports = require("react-dom");

/***/ }),

/***/ "react/jsx-dev-runtime":
/*!****************************************!*\
  !*** external "react/jsx-dev-runtime" ***!
  \****************************************/
/***/ ((module) => {

"use strict";
module.exports = require("react/jsx-dev-runtime");

/***/ }),

/***/ "react/jsx-runtime":
/*!************************************!*\
  !*** external "react/jsx-runtime" ***!
  \************************************/
/***/ ((module) => {

"use strict";
module.exports = require("react/jsx-runtime");

/***/ }),

/***/ "fs":
/*!*********************!*\
  !*** external "fs" ***!
  \*********************/
/***/ ((module) => {

"use strict";
module.exports = require("fs");

/***/ }),

/***/ "path":
/*!***********************!*\
  !*** external "path" ***!
  \***********************/
/***/ ((module) => {

"use strict";
module.exports = require("path");

/***/ }),

/***/ "stream":
/*!*************************!*\
  !*** external "stream" ***!
  \*************************/
/***/ ((module) => {

"use strict";
module.exports = require("stream");

/***/ }),

/***/ "zlib":
/*!***********************!*\
  !*** external "zlib" ***!
  \***********************/
/***/ ((module) => {

"use strict";
module.exports = require("zlib");

/***/ }),

/***/ "react-toastify":
/*!*********************************!*\
  !*** external "react-toastify" ***!
  \*********************************/
/***/ ((module) => {

"use strict";
module.exports = import("react-toastify");;

/***/ })

};
;

// load runtime
var __webpack_require__ = require("../../webpack-runtime.js");
__webpack_require__.C(exports);
var __webpack_exec__ = (moduleId) => (__webpack_require__(__webpack_require__.s = moduleId))
var __webpack_exports__ = __webpack_require__.X(0, ["vendor-chunks/next","vendor-chunks/@swc","vendor-chunks/react-toastify"], () => (__webpack_exec__("./node_modules/next/dist/build/webpack/loaders/next-route-loader/index.js?kind=PAGES&page=%2Fadmin%2Freceipts&preferredRegion=&absolutePagePath=.%2Fpages%5Cadmin%5Creceipts.js&absoluteAppPath=private-next-pages%2F_app&absoluteDocumentPath=private-next-pages%2F_document&middlewareConfigBase64=e30%3D!")));
module.exports = __webpack_exports__;

})();