"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
(() => {
var exports = {};
exports.id = "pages/api/admin/artists";
exports.ids = ["pages/api/admin/artists"];
exports.modules = {

/***/ "@supabase/supabase-js":
/*!****************************************!*\
  !*** external "@supabase/supabase-js" ***!
  \****************************************/
/***/ ((module) => {

module.exports = require("@supabase/supabase-js");

/***/ }),

/***/ "next/dist/compiled/next-server/pages-api.runtime.dev.js":
/*!**************************************************************************!*\
  !*** external "next/dist/compiled/next-server/pages-api.runtime.dev.js" ***!
  \**************************************************************************/
/***/ ((module) => {

module.exports = require("next/dist/compiled/next-server/pages-api.runtime.dev.js");

/***/ }),

/***/ "(api)/./node_modules/next/dist/build/webpack/loaders/next-route-loader/index.js?kind=PAGES_API&page=%2Fapi%2Fadmin%2Fartists&preferredRegion=&absolutePagePath=.%2Fpages%5Capi%5Cadmin%5Cartists.ts&middlewareConfigBase64=e30%3D!":
/*!************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-route-loader/index.js?kind=PAGES_API&page=%2Fapi%2Fadmin%2Fartists&preferredRegion=&absolutePagePath=.%2Fpages%5Capi%5Cadmin%5Cartists.ts&middlewareConfigBase64=e30%3D! ***!
  \************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   config: () => (/* binding */ config),\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__),\n/* harmony export */   routeModule: () => (/* binding */ routeModule)\n/* harmony export */ });\n/* harmony import */ var next_dist_server_future_route_modules_pages_api_module_compiled__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/server/future/route-modules/pages-api/module.compiled */ \"(api)/./node_modules/next/dist/server/future/route-modules/pages-api/module.compiled.js\");\n/* harmony import */ var next_dist_server_future_route_modules_pages_api_module_compiled__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_future_route_modules_pages_api_module_compiled__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_dist_server_future_route_kind__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next/dist/server/future/route-kind */ \"(api)/./node_modules/next/dist/server/future/route-kind.js\");\n/* harmony import */ var next_dist_build_templates_helpers__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/dist/build/templates/helpers */ \"(api)/./node_modules/next/dist/build/templates/helpers.js\");\n/* harmony import */ var _pages_api_admin_artists_ts__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./pages\\api\\admin\\artists.ts */ \"(api)/./pages/api/admin/artists.ts\");\n\n\n\n// Import the userland code.\n\n// Re-export the handler (should be the default export).\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = ((0,next_dist_build_templates_helpers__WEBPACK_IMPORTED_MODULE_2__.hoist)(_pages_api_admin_artists_ts__WEBPACK_IMPORTED_MODULE_3__, \"default\"));\n// Re-export config.\nconst config = (0,next_dist_build_templates_helpers__WEBPACK_IMPORTED_MODULE_2__.hoist)(_pages_api_admin_artists_ts__WEBPACK_IMPORTED_MODULE_3__, \"config\");\n// Create and export the route module that will be consumed.\nconst routeModule = new next_dist_server_future_route_modules_pages_api_module_compiled__WEBPACK_IMPORTED_MODULE_0__.PagesAPIRouteModule({\n    definition: {\n        kind: next_dist_server_future_route_kind__WEBPACK_IMPORTED_MODULE_1__.RouteKind.PAGES_API,\n        page: \"/api/admin/artists\",\n        pathname: \"/api/admin/artists\",\n        // The following aren't used in production.\n        bundlePath: \"\",\n        filename: \"\"\n    },\n    userland: _pages_api_admin_artists_ts__WEBPACK_IMPORTED_MODULE_3__\n});\n\n//# sourceMappingURL=pages-api.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(api)/./node_modules/next/dist/build/webpack/loaders/next-route-loader/index.js?kind=PAGES_API&page=%2Fapi%2Fadmin%2Fartists&preferredRegion=&absolutePagePath=.%2Fpages%5Capi%5Cadmin%5Cartists.ts&middlewareConfigBase64=e30%3D!\n");

/***/ }),

/***/ "(api)/./pages/api/admin/artists.ts":
/*!************************************!*\
  !*** ./pages/api/admin/artists.ts ***!
  \************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ handler)\n/* harmony export */ });\n/* harmony import */ var _supabase_supabase_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! @supabase/supabase-js */ \"@supabase/supabase-js\");\n/* harmony import */ var _supabase_supabase_js__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(_supabase_supabase_js__WEBPACK_IMPORTED_MODULE_0__);\n\nconst supabase = (0,_supabase_supabase_js__WEBPACK_IMPORTED_MODULE_0__.createClient)(\"https://ndlgbcsbidyhxbpqzgqp.supabase.co\", process.env.SUPABASE_SERVICE_ROLE_KEY);\nasync function handler(req, res) {\n    // Verify admin authentication\n    const authHeader = req.headers.authorization;\n    if (!authHeader || !authHeader.startsWith(\"Bearer \")) {\n        return res.status(401).json({\n            error: \"Unauthorized\"\n        });\n    }\n    try {\n        if (req.method === \"GET\") {\n            // Try to get artists from users table with artist role, or create mock data structure\n            const { data: artists, error } = await supabase.from(\"users\").select(\"*\").eq(\"role\", \"artist\");\n            if (error && error.message.includes('relation \"users\" does not exist')) {\n                // If no users table, return structured mock data for now\n                const mockArtists = [\n                    {\n                        id: 1,\n                        name: \"Sarah Johnson\",\n                        email: \"<EMAIL>\",\n                        phone: \"0412 345 678\",\n                        specializations: [\n                            \"Box Braids\",\n                            \"Cornrows\",\n                            \"Twists\"\n                        ],\n                        status: \"active\",\n                        availability: {\n                            monday: {\n                                start: \"09:00\",\n                                end: \"17:00\",\n                                available: true\n                            },\n                            tuesday: {\n                                start: \"09:00\",\n                                end: \"17:00\",\n                                available: true\n                            },\n                            wednesday: {\n                                start: \"09:00\",\n                                end: \"17:00\",\n                                available: true\n                            },\n                            thursday: {\n                                start: \"09:00\",\n                                end: \"17:00\",\n                                available: true\n                            },\n                            friday: {\n                                start: \"09:00\",\n                                end: \"17:00\",\n                                available: true\n                            },\n                            saturday: {\n                                start: \"10:00\",\n                                end: \"16:00\",\n                                available: true\n                            },\n                            sunday: {\n                                start: \"10:00\",\n                                end: \"16:00\",\n                                available: false\n                            }\n                        },\n                        rating: 4.8,\n                        total_bookings: 127,\n                        total_revenue: 15240,\n                        created_at: \"2024-01-15T00:00:00Z\"\n                    },\n                    {\n                        id: 2,\n                        name: \"Maya Patel\",\n                        email: \"<EMAIL>\",\n                        phone: \"0423 456 789\",\n                        specializations: [\n                            \"Locs\",\n                            \"Faux Locs\",\n                            \"Goddess Braids\"\n                        ],\n                        status: \"active\",\n                        availability: {\n                            monday: {\n                                start: \"10:00\",\n                                end: \"18:00\",\n                                available: true\n                            },\n                            tuesday: {\n                                start: \"10:00\",\n                                end: \"18:00\",\n                                available: true\n                            },\n                            wednesday: {\n                                start: \"10:00\",\n                                end: \"18:00\",\n                                available: false\n                            },\n                            thursday: {\n                                start: \"10:00\",\n                                end: \"18:00\",\n                                available: true\n                            },\n                            friday: {\n                                start: \"10:00\",\n                                end: \"18:00\",\n                                available: true\n                            },\n                            saturday: {\n                                start: \"09:00\",\n                                end: \"17:00\",\n                                available: true\n                            },\n                            sunday: {\n                                start: \"11:00\",\n                                end: \"15:00\",\n                                available: true\n                            }\n                        },\n                        rating: 4.9,\n                        total_bookings: 98,\n                        total_revenue: 12800,\n                        created_at: \"2024-02-01T00:00:00Z\"\n                    },\n                    {\n                        id: 3,\n                        name: \"Keisha Williams\",\n                        email: \"<EMAIL>\",\n                        phone: \"0434 567 890\",\n                        specializations: [\n                            \"Senegalese Twists\",\n                            \"Marley Twists\",\n                            \"Passion Twists\"\n                        ],\n                        status: \"active\",\n                        availability: {\n                            monday: {\n                                start: \"08:00\",\n                                end: \"16:00\",\n                                available: true\n                            },\n                            tuesday: {\n                                start: \"08:00\",\n                                end: \"16:00\",\n                                available: true\n                            },\n                            wednesday: {\n                                start: \"08:00\",\n                                end: \"16:00\",\n                                available: true\n                            },\n                            thursday: {\n                                start: \"08:00\",\n                                end: \"16:00\",\n                                available: true\n                            },\n                            friday: {\n                                start: \"08:00\",\n                                end: \"16:00\",\n                                available: true\n                            },\n                            saturday: {\n                                start: \"09:00\",\n                                end: \"15:00\",\n                                available: true\n                            },\n                            sunday: {\n                                start: \"09:00\",\n                                end: \"15:00\",\n                                available: false\n                            }\n                        },\n                        rating: 4.7,\n                        total_bookings: 145,\n                        total_revenue: 18600,\n                        created_at: \"2024-01-20T00:00:00Z\"\n                    }\n                ];\n                return res.status(200).json({\n                    artists: mockArtists,\n                    source: \"mock\"\n                });\n            }\n            if (error) {\n                throw error;\n            }\n            return res.status(200).json({\n                artists: artists || [],\n                source: \"database\"\n            });\n        }\n        if (req.method === \"POST\") {\n            const { name, email, phone, specializations, availability } = req.body;\n            const { data, error } = await supabase.from(\"users\").insert([\n                {\n                    name,\n                    email,\n                    phone,\n                    role: \"artist\",\n                    specializations,\n                    availability,\n                    status: \"active\",\n                    created_at: new Date().toISOString()\n                }\n            ]).select();\n            if (error) {\n                throw error;\n            }\n            return res.status(201).json({\n                artist: data[0]\n            });\n        }\n        return res.status(405).json({\n            error: \"Method not allowed\"\n        });\n    } catch (error) {\n        console.error(\"Artists API error:\", error);\n        return res.status(500).json({\n            error: \"Internal server error\"\n        });\n    }\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(api)/./pages/api/admin/artists.ts\n");

/***/ })

};
;

// load runtime
var __webpack_require__ = require("../../../webpack-api-runtime.js");
__webpack_require__.C(exports);
var __webpack_exec__ = (moduleId) => (__webpack_require__(__webpack_require__.s = moduleId))
var __webpack_exports__ = __webpack_require__.X(0, ["vendor-chunks/next"], () => (__webpack_exec__("(api)/./node_modules/next/dist/build/webpack/loaders/next-route-loader/index.js?kind=PAGES_API&page=%2Fapi%2Fadmin%2Fartists&preferredRegion=&absolutePagePath=.%2Fpages%5Capi%5Cadmin%5Cartists.ts&middlewareConfigBase64=e30%3D!")));
module.exports = __webpack_exports__;

})();