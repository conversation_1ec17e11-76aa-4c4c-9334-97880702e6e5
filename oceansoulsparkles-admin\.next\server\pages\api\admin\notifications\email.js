"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
(() => {
var exports = {};
exports.id = "pages/api/admin/notifications/email";
exports.ids = ["pages/api/admin/notifications/email"];
exports.modules = {

/***/ "@supabase/supabase-js":
/*!****************************************!*\
  !*** external "@supabase/supabase-js" ***!
  \****************************************/
/***/ ((module) => {

module.exports = require("@supabase/supabase-js");

/***/ }),

/***/ "bcryptjs":
/*!***************************!*\
  !*** external "bcryptjs" ***!
  \***************************/
/***/ ((module) => {

module.exports = require("bcryptjs");

/***/ }),

/***/ "jsonwebtoken":
/*!*******************************!*\
  !*** external "jsonwebtoken" ***!
  \*******************************/
/***/ ((module) => {

module.exports = require("jsonwebtoken");

/***/ }),

/***/ "next/dist/compiled/next-server/pages-api.runtime.dev.js":
/*!**************************************************************************!*\
  !*** external "next/dist/compiled/next-server/pages-api.runtime.dev.js" ***!
  \**************************************************************************/
/***/ ((module) => {

module.exports = require("next/dist/compiled/next-server/pages-api.runtime.dev.js");

/***/ }),

/***/ "nodemailer":
/*!*****************************!*\
  !*** external "nodemailer" ***!
  \*****************************/
/***/ ((module) => {

module.exports = require("nodemailer");

/***/ }),

/***/ "speakeasy":
/*!****************************!*\
  !*** external "speakeasy" ***!
  \****************************/
/***/ ((module) => {

module.exports = require("speakeasy");

/***/ }),

/***/ "(api)/./node_modules/next/dist/build/webpack/loaders/next-route-loader/index.js?kind=PAGES_API&page=%2Fapi%2Fadmin%2Fnotifications%2Femail&preferredRegion=&absolutePagePath=.%2Fpages%5Capi%5Cadmin%5Cnotifications%5Cemail.js&middlewareConfigBase64=e30%3D!":
/*!****************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-route-loader/index.js?kind=PAGES_API&page=%2Fapi%2Fadmin%2Fnotifications%2Femail&preferredRegion=&absolutePagePath=.%2Fpages%5Capi%5Cadmin%5Cnotifications%5Cemail.js&middlewareConfigBase64=e30%3D! ***!
  \****************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   config: () => (/* binding */ config),\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__),\n/* harmony export */   routeModule: () => (/* binding */ routeModule)\n/* harmony export */ });\n/* harmony import */ var next_dist_server_future_route_modules_pages_api_module_compiled__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/server/future/route-modules/pages-api/module.compiled */ \"(api)/./node_modules/next/dist/server/future/route-modules/pages-api/module.compiled.js\");\n/* harmony import */ var next_dist_server_future_route_modules_pages_api_module_compiled__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_future_route_modules_pages_api_module_compiled__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_dist_server_future_route_kind__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next/dist/server/future/route-kind */ \"(api)/./node_modules/next/dist/server/future/route-kind.js\");\n/* harmony import */ var next_dist_build_templates_helpers__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/dist/build/templates/helpers */ \"(api)/./node_modules/next/dist/build/templates/helpers.js\");\n/* harmony import */ var _pages_api_admin_notifications_email_js__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./pages\\api\\admin\\notifications\\email.js */ \"(api)/./pages/api/admin/notifications/email.js\");\n\n\n\n// Import the userland code.\n\n// Re-export the handler (should be the default export).\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = ((0,next_dist_build_templates_helpers__WEBPACK_IMPORTED_MODULE_2__.hoist)(_pages_api_admin_notifications_email_js__WEBPACK_IMPORTED_MODULE_3__, \"default\"));\n// Re-export config.\nconst config = (0,next_dist_build_templates_helpers__WEBPACK_IMPORTED_MODULE_2__.hoist)(_pages_api_admin_notifications_email_js__WEBPACK_IMPORTED_MODULE_3__, \"config\");\n// Create and export the route module that will be consumed.\nconst routeModule = new next_dist_server_future_route_modules_pages_api_module_compiled__WEBPACK_IMPORTED_MODULE_0__.PagesAPIRouteModule({\n    definition: {\n        kind: next_dist_server_future_route_kind__WEBPACK_IMPORTED_MODULE_1__.RouteKind.PAGES_API,\n        page: \"/api/admin/notifications/email\",\n        pathname: \"/api/admin/notifications/email\",\n        // The following aren't used in production.\n        bundlePath: \"\",\n        filename: \"\"\n    },\n    userland: _pages_api_admin_notifications_email_js__WEBPACK_IMPORTED_MODULE_3__\n});\n\n//# sourceMappingURL=pages-api.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(api)/./node_modules/next/dist/build/webpack/loaders/next-route-loader/index.js?kind=PAGES_API&page=%2Fapi%2Fadmin%2Fnotifications%2Femail&preferredRegion=&absolutePagePath=.%2Fpages%5Capi%5Cadmin%5Cnotifications%5Cemail.js&middlewareConfigBase64=e30%3D!\n");

/***/ }),

/***/ "(api)/./lib/auth/admin-auth.ts":
/*!********************************!*\
  !*** ./lib/auth/admin-auth.ts ***!
  \********************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   adminLogin: () => (/* binding */ adminLogin),\n/* harmony export */   adminLogout: () => (/* binding */ adminLogout),\n/* harmony export */   authenticateAdminRequest: () => (/* binding */ authenticateAdminRequest),\n/* harmony export */   enableMFA: () => (/* binding */ enableMFA),\n/* harmony export */   generateMFASecret: () => (/* binding */ generateMFASecret),\n/* harmony export */   verifyAdminToken: () => (/* binding */ verifyAdminToken),\n/* harmony export */   verifyMFAAndLogin: () => (/* binding */ verifyMFAAndLogin)\n/* harmony export */ });\n/* harmony import */ var jsonwebtoken__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! jsonwebtoken */ \"jsonwebtoken\");\n/* harmony import */ var jsonwebtoken__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(jsonwebtoken__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var bcryptjs__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! bcryptjs */ \"bcryptjs\");\n/* harmony import */ var bcryptjs__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(bcryptjs__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _supabase_supabase_js__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @supabase/supabase-js */ \"@supabase/supabase-js\");\n/* harmony import */ var _supabase_supabase_js__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(_supabase_supabase_js__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var _security_audit_logging__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ../security/audit-logging */ \"(api)/./lib/security/audit-logging.ts\");\n\n\n\n\n// Handle missing environment variables gracefully\nconst supabaseUrl = \"https://ndlgbcsbidyhxbpqzgqp.supabase.co\" || 0;\nconst supabaseKey = process.env.SUPABASE_SERVICE_ROLE_KEY || \"eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.placeholder\";\nconst supabase = (0,_supabase_supabase_js__WEBPACK_IMPORTED_MODULE_2__.createClient)(supabaseUrl, supabaseKey);\n/**\r\n * Verify admin authentication token\r\n */ async function verifyAdminToken(token) {\n    try {\n        // Handle missing JWT secret gracefully\n        const jwtSecret = process.env.JWT_SECRET || \"placeholder-secret\";\n        const decoded = jsonwebtoken__WEBPACK_IMPORTED_MODULE_0___default().verify(token, jwtSecret);\n        // Get user from database with latest info\n        const { data: user, error } = await supabase.from(\"admin_users\").select(`\r\n        id,\r\n        email,\r\n        role,\r\n        first_name,\r\n        last_name,\r\n        is_active,\r\n        mfa_enabled,\r\n        last_activity,\r\n        permissions\r\n      `).eq(\"id\", decoded.userId).eq(\"is_active\", true).single();\n        if (error || !user) {\n            return {\n                valid: false,\n                error: \"User not found or inactive\"\n            };\n        }\n        // Check if user is still active\n        if (!user.is_active) {\n            return {\n                valid: false,\n                error: \"User account is deactivated\"\n            };\n        }\n        // Update last activity\n        await supabase.from(\"admin_users\").update({\n            last_activity: Date.now()\n        }).eq(\"id\", user.id);\n        return {\n            valid: true,\n            user: {\n                id: user.id,\n                email: user.email,\n                role: user.role,\n                firstName: user.first_name,\n                lastName: user.last_name,\n                isActive: user.is_active,\n                mfaEnabled: user.mfa_enabled,\n                lastActivity: Date.now(),\n                permissions: user.permissions || []\n            }\n        };\n    } catch (error) {\n        return {\n            valid: false,\n            error: \"Invalid token\"\n        };\n    }\n}\n/**\r\n * Admin login with email and password\r\n */ async function adminLogin(email, password, ip) {\n    try {\n        // Check for rate limiting\n        const { data: attempts } = await supabase.from(\"login_attempts\").select(\"*\").eq(\"email\", email).gte(\"created_at\", new Date(Date.now() - 15 * 60 * 1000).toISOString()).order(\"created_at\", {\n            ascending: false\n        });\n        if (attempts && attempts.length >= 5) {\n            await (0,_security_audit_logging__WEBPACK_IMPORTED_MODULE_3__.auditLog)({\n                action: \"LOGIN_BLOCKED\",\n                email,\n                ip,\n                reason: \"Too many failed attempts\"\n            });\n            return {\n                success: false,\n                error: \"Account temporarily locked due to too many failed attempts\"\n            };\n        }\n        // Get user from database\n        const { data: user, error } = await supabase.from(\"admin_users\").select(`\r\n        id,\r\n        email,\r\n        password_hash,\r\n        role,\r\n        first_name,\r\n        last_name,\r\n        is_active,\r\n        mfa_enabled,\r\n        mfa_secret,\r\n        permissions\r\n      `).eq(\"email\", email.toLowerCase()).single();\n        if (error || !user) {\n            await recordFailedAttempt(email, ip, error ? `Database error: ${error.message}` : \"User not found\");\n            return {\n                success: false,\n                error: \"Invalid credentials\"\n            };\n        }\n        // Check if user is active\n        if (!user.is_active) {\n            await (0,_security_audit_logging__WEBPACK_IMPORTED_MODULE_3__.auditLog)({\n                action: \"LOGIN_DENIED\",\n                userId: user.id,\n                email,\n                ip,\n                reason: \"Account deactivated\"\n            });\n            return {\n                success: false,\n                error: \"Account is deactivated\"\n            };\n        }\n        // Verify password\n        const passwordValid = await bcryptjs__WEBPACK_IMPORTED_MODULE_1___default().compare(password, user.password_hash);\n        if (!passwordValid) {\n            await recordFailedAttempt(email, ip, \"Invalid password\");\n            return {\n                success: false,\n                error: \"Invalid credentials\"\n            };\n        }\n        // Clear failed attempts on successful password verification\n        await supabase.from(\"login_attempts\").delete().eq(\"email\", email);\n        // Check if MFA is required\n        if (user.mfa_enabled && user.mfa_secret) {\n            // Return success but indicate MFA is required\n            return {\n                success: true,\n                requiresMFA: true,\n                user: {\n                    id: user.id,\n                    email: user.email,\n                    role: user.role,\n                    firstName: user.first_name,\n                    lastName: user.last_name,\n                    isActive: user.is_active,\n                    mfaEnabled: user.mfa_enabled,\n                    lastActivity: Date.now(),\n                    permissions: user.permissions || []\n                }\n            };\n        }\n        // Generate JWT token\n        const jwtSecret = process.env.JWT_SECRET || \"placeholder-secret\";\n        const token = jsonwebtoken__WEBPACK_IMPORTED_MODULE_0___default().sign({\n            userId: user.id,\n            email: user.email,\n            role: user.role\n        }, jwtSecret, {\n            expiresIn: \"8h\"\n        });\n        // Update last login\n        await supabase.from(\"admin_users\").update({\n            last_login_at: new Date().toISOString(),\n            last_activity: Date.now()\n        }).eq(\"id\", user.id);\n        await (0,_security_audit_logging__WEBPACK_IMPORTED_MODULE_3__.auditLog)({\n            action: \"LOGIN_SUCCESS\",\n            userId: user.id,\n            userRole: user.role,\n            email,\n            ip\n        });\n        return {\n            success: true,\n            token,\n            user: {\n                id: user.id,\n                email: user.email,\n                role: user.role,\n                firstName: user.first_name,\n                lastName: user.last_name,\n                isActive: user.is_active,\n                mfaEnabled: user.mfa_enabled,\n                lastActivity: Date.now(),\n                permissions: user.permissions || []\n            }\n        };\n    } catch (error) {\n        console.error(\"Admin login error:\", error);\n        return {\n            success: false,\n            error: \"Login failed\"\n        };\n    }\n}\n/**\r\n * Verify MFA token and complete login\r\n * Note: This function requires Node.js runtime due to speakeasy dependency\r\n */ async function verifyMFAAndLogin(userId, mfaToken, ip) {\n    // Import speakeasy dynamically to avoid Edge Runtime issues\n    const speakeasy = await Promise.resolve(/*! import() */).then(__webpack_require__.t.bind(__webpack_require__, /*! speakeasy */ \"speakeasy\", 23));\n    try {\n        const { data: user, error } = await supabase.from(\"admin_users\").select(`\r\n        id,\r\n        email,\r\n        role,\r\n        first_name,\r\n        last_name,\r\n        is_active,\r\n        mfa_enabled,\r\n        mfa_secret,\r\n        permissions\r\n      `).eq(\"id\", userId).single();\n        if (error || !user || !user.mfa_secret) {\n            return {\n                success: false,\n                error: \"Invalid MFA setup\"\n            };\n        }\n        // Verify MFA token\n        const verified = speakeasy.totp.verify({\n            secret: user.mfa_secret,\n            encoding: \"base32\",\n            token: mfaToken,\n            window: 2\n        });\n        if (!verified) {\n            await (0,_security_audit_logging__WEBPACK_IMPORTED_MODULE_3__.auditLog)({\n                action: \"MFA_FAILED\",\n                userId: user.id,\n                email: user.email,\n                ip,\n                reason: \"Invalid MFA token\"\n            });\n            return {\n                success: false,\n                error: \"Invalid MFA token\"\n            };\n        }\n        // Generate JWT token\n        const jwtSecret = process.env.JWT_SECRET || \"placeholder-secret\";\n        const token = jsonwebtoken__WEBPACK_IMPORTED_MODULE_0___default().sign({\n            userId: user.id,\n            email: user.email,\n            role: user.role\n        }, jwtSecret, {\n            expiresIn: \"8h\"\n        });\n        // Update last login\n        await supabase.from(\"admin_users\").update({\n            last_login_at: new Date().toISOString(),\n            last_activity: Date.now()\n        }).eq(\"id\", user.id);\n        await (0,_security_audit_logging__WEBPACK_IMPORTED_MODULE_3__.auditLog)({\n            action: \"MFA_LOGIN_SUCCESS\",\n            userId: user.id,\n            userRole: user.role,\n            email: user.email,\n            ip\n        });\n        return {\n            success: true,\n            token,\n            user: {\n                id: user.id,\n                email: user.email,\n                role: user.role,\n                firstName: user.first_name,\n                lastName: user.last_name,\n                isActive: user.is_active,\n                mfaEnabled: user.mfa_enabled,\n                lastActivity: Date.now(),\n                permissions: user.permissions || []\n            }\n        };\n    } catch (error) {\n        console.error(\"MFA verification error:\", error);\n        return {\n            success: false,\n            error: \"MFA verification failed\"\n        };\n    }\n}\n/**\r\n * Generate MFA secret for user\r\n * Note: This function requires Node.js runtime due to speakeasy dependency\r\n */ async function generateMFASecret(userId) {\n    // Import speakeasy dynamically to avoid Edge Runtime issues\n    const speakeasy = await Promise.resolve(/*! import() */).then(__webpack_require__.t.bind(__webpack_require__, /*! speakeasy */ \"speakeasy\", 23));\n    try {\n        const { data: user } = await supabase.from(\"admin_users\").select(\"email, first_name, last_name\").eq(\"id\", userId).single();\n        if (!user) return null;\n        const secret = speakeasy.generateSecret({\n            name: `${user.first_name} ${user.last_name}`,\n            issuer: \"Ocean Soul Sparkles Admin\",\n            length: 32\n        });\n        return {\n            secret: secret.base32,\n            qrCode: secret.otpauth_url\n        };\n    } catch (error) {\n        console.error(\"MFA secret generation error:\", error);\n        return null;\n    }\n}\n/**\r\n * Enable MFA for user\r\n * Note: This function requires Node.js runtime due to speakeasy dependency\r\n */ async function enableMFA(userId, secret, token) {\n    // Import speakeasy dynamically to avoid Edge Runtime issues\n    const speakeasy = await Promise.resolve(/*! import() */).then(__webpack_require__.t.bind(__webpack_require__, /*! speakeasy */ \"speakeasy\", 23));\n    try {\n        // Verify the token first\n        const verified = speakeasy.totp.verify({\n            secret,\n            encoding: \"base32\",\n            token,\n            window: 2\n        });\n        if (!verified) return false;\n        // Save MFA secret to database\n        const { error } = await supabase.from(\"admin_users\").update({\n            mfa_secret: secret,\n            mfa_enabled: true\n        }).eq(\"id\", userId);\n        if (error) return false;\n        await (0,_security_audit_logging__WEBPACK_IMPORTED_MODULE_3__.auditLog)({\n            action: \"MFA_ENABLED\",\n            userId,\n            reason: \"User enabled MFA\"\n        });\n        return true;\n    } catch (error) {\n        console.error(\"MFA enable error:\", error);\n        return false;\n    }\n}\n/**\r\n * Record failed login attempt\r\n */ async function recordFailedAttempt(email, ip, reason) {\n    await supabase.from(\"login_attempts\").insert({\n        email,\n        ip_address: ip,\n        success: false,\n        reason,\n        created_at: new Date().toISOString()\n    });\n    await (0,_security_audit_logging__WEBPACK_IMPORTED_MODULE_3__.auditLog)({\n        action: \"LOGIN_FAILED\",\n        email,\n        ip,\n        reason\n    });\n}\n/**\r\n * Admin logout\r\n */ async function adminLogout(userId, ip) {\n    try {\n        await (0,_security_audit_logging__WEBPACK_IMPORTED_MODULE_3__.auditLog)({\n            action: \"LOGOUT\",\n            userId,\n            ip\n        });\n    } catch (error) {\n        console.error(\"Logout audit error:\", error);\n    }\n}\n/**\r\n * Authenticate admin request (alias for verifyAdminToken for backward compatibility)\r\n */ async function authenticateAdminRequest(token) {\n    return verifyAdminToken(token);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKGFwaSkvLi9saWIvYXV0aC9hZG1pbi1hdXRoLnRzIiwibWFwcGluZ3MiOiI7Ozs7Ozs7Ozs7Ozs7Ozs7O0FBQStCO0FBQ0Q7QUFDdUI7QUFDQTtBQUVyRCxrREFBa0Q7QUFDbEQsTUFBTUksY0FBY0MsMENBQW9DLElBQUk7QUFDNUQsTUFBTUcsY0FBY0gsUUFBUUMsR0FBRyxDQUFDRyx5QkFBeUIsSUFBSTtBQUU3RCxNQUFNQyxXQUFXUixtRUFBWUEsQ0FBQ0UsYUFBYUk7QUE2QjNDOztDQUVDLEdBQ00sZUFBZUcsaUJBQWlCQyxLQUFhO0lBQ2xELElBQUk7UUFDRix1Q0FBdUM7UUFDdkMsTUFBTUMsWUFBWVIsUUFBUUMsR0FBRyxDQUFDUSxVQUFVLElBQUk7UUFDNUMsTUFBTUMsVUFBVWYsMERBQVUsQ0FBQ1ksT0FBT0M7UUFFbEMsMENBQTBDO1FBQzFDLE1BQU0sRUFBRUksTUFBTUMsSUFBSSxFQUFFQyxLQUFLLEVBQUUsR0FBRyxNQUFNVCxTQUNqQ1UsSUFBSSxDQUFDLGVBQ0xDLE1BQU0sQ0FBQyxDQUFDOzs7Ozs7Ozs7O01BVVQsQ0FBQyxFQUNBQyxFQUFFLENBQUMsTUFBTVAsUUFBUVEsTUFBTSxFQUN2QkQsRUFBRSxDQUFDLGFBQWEsTUFDaEJFLE1BQU07UUFFVCxJQUFJTCxTQUFTLENBQUNELE1BQU07WUFDbEIsT0FBTztnQkFBRU8sT0FBTztnQkFBT04sT0FBTztZQUE2QjtRQUM3RDtRQUVBLGdDQUFnQztRQUNoQyxJQUFJLENBQUNELEtBQUtRLFNBQVMsRUFBRTtZQUNuQixPQUFPO2dCQUFFRCxPQUFPO2dCQUFPTixPQUFPO1lBQThCO1FBQzlEO1FBRUEsdUJBQXVCO1FBQ3ZCLE1BQU1ULFNBQ0hVLElBQUksQ0FBQyxlQUNMTyxNQUFNLENBQUM7WUFBRUMsZUFBZUMsS0FBS0MsR0FBRztRQUFHLEdBQ25DUixFQUFFLENBQUMsTUFBTUosS0FBS2EsRUFBRTtRQUVuQixPQUFPO1lBQ0xOLE9BQU87WUFDUFAsTUFBTTtnQkFDSmEsSUFBSWIsS0FBS2EsRUFBRTtnQkFDWEMsT0FBT2QsS0FBS2MsS0FBSztnQkFDakJDLE1BQU1mLEtBQUtlLElBQUk7Z0JBQ2ZDLFdBQVdoQixLQUFLaUIsVUFBVTtnQkFDMUJDLFVBQVVsQixLQUFLbUIsU0FBUztnQkFDeEJDLFVBQVVwQixLQUFLUSxTQUFTO2dCQUN4QmEsWUFBWXJCLEtBQUtzQixXQUFXO2dCQUM1QkMsY0FBY1osS0FBS0MsR0FBRztnQkFDdEJZLGFBQWF4QixLQUFLd0IsV0FBVyxJQUFJLEVBQUU7WUFDckM7UUFDRjtJQUNGLEVBQUUsT0FBT3ZCLE9BQU87UUFDZCxPQUFPO1lBQUVNLE9BQU87WUFBT04sT0FBTztRQUFnQjtJQUNoRDtBQUNGO0FBRUE7O0NBRUMsR0FDTSxlQUFld0IsV0FDcEJYLEtBQWEsRUFDYlksUUFBZ0IsRUFDaEJDLEVBQVc7SUFFWCxJQUFJO1FBQ0YsMEJBQTBCO1FBQzFCLE1BQU0sRUFBRTVCLE1BQU02QixRQUFRLEVBQUUsR0FBRyxNQUFNcEMsU0FDOUJVLElBQUksQ0FBQyxrQkFDTEMsTUFBTSxDQUFDLEtBQ1BDLEVBQUUsQ0FBQyxTQUFTVSxPQUNaZSxHQUFHLENBQUMsY0FBYyxJQUFJbEIsS0FBS0EsS0FBS0MsR0FBRyxLQUFLLEtBQUssS0FBSyxNQUFNa0IsV0FBVyxJQUNuRUMsS0FBSyxDQUFDLGNBQWM7WUFBRUMsV0FBVztRQUFNO1FBRTFDLElBQUlKLFlBQVlBLFNBQVNLLE1BQU0sSUFBSSxHQUFHO1lBQ3BDLE1BQU1oRCxpRUFBUUEsQ0FBQztnQkFDYmlELFFBQVE7Z0JBQ1JwQjtnQkFDQWE7Z0JBQ0FRLFFBQVE7WUFDVjtZQUNBLE9BQU87Z0JBQUVDLFNBQVM7Z0JBQU9uQyxPQUFPO1lBQTZEO1FBQy9GO1FBRUEseUJBQXlCO1FBQ3pCLE1BQU0sRUFBRUYsTUFBTUMsSUFBSSxFQUFFQyxLQUFLLEVBQUUsR0FBRyxNQUFNVCxTQUNqQ1UsSUFBSSxDQUFDLGVBQ0xDLE1BQU0sQ0FBQyxDQUFDOzs7Ozs7Ozs7OztNQVdULENBQUMsRUFDQUMsRUFBRSxDQUFDLFNBQVNVLE1BQU11QixXQUFXLElBQzdCL0IsTUFBTTtRQUlULElBQUlMLFNBQVMsQ0FBQ0QsTUFBTTtZQUNsQixNQUFNc0Msb0JBQW9CeEIsT0FBT2EsSUFBSTFCLFFBQVEsQ0FBQyxnQkFBZ0IsRUFBRUEsTUFBTXNDLE9BQU8sQ0FBQyxDQUFDLEdBQUc7WUFDbEYsT0FBTztnQkFBRUgsU0FBUztnQkFBT25DLE9BQU87WUFBc0I7UUFDeEQ7UUFFQSwwQkFBMEI7UUFDMUIsSUFBSSxDQUFDRCxLQUFLUSxTQUFTLEVBQUU7WUFDbkIsTUFBTXZCLGlFQUFRQSxDQUFDO2dCQUNiaUQsUUFBUTtnQkFDUjdCLFFBQVFMLEtBQUthLEVBQUU7Z0JBQ2ZDO2dCQUNBYTtnQkFDQVEsUUFBUTtZQUNWO1lBQ0EsT0FBTztnQkFBRUMsU0FBUztnQkFBT25DLE9BQU87WUFBeUI7UUFDM0Q7UUFFQSxrQkFBa0I7UUFDbEIsTUFBTXVDLGdCQUFnQixNQUFNekQsdURBQWMsQ0FBQzJDLFVBQVUxQixLQUFLMEMsYUFBYTtRQUN2RSxJQUFJLENBQUNGLGVBQWU7WUFDbEIsTUFBTUYsb0JBQW9CeEIsT0FBT2EsSUFBSTtZQUNyQyxPQUFPO2dCQUFFUyxTQUFTO2dCQUFPbkMsT0FBTztZQUFzQjtRQUN4RDtRQUVBLDREQUE0RDtRQUM1RCxNQUFNVCxTQUNIVSxJQUFJLENBQUMsa0JBQ0x5QyxNQUFNLEdBQ052QyxFQUFFLENBQUMsU0FBU1U7UUFFZiwyQkFBMkI7UUFDM0IsSUFBSWQsS0FBS3NCLFdBQVcsSUFBSXRCLEtBQUs0QyxVQUFVLEVBQUU7WUFDdkMsOENBQThDO1lBQzlDLE9BQU87Z0JBQ0xSLFNBQVM7Z0JBQ1RTLGFBQWE7Z0JBQ2I3QyxNQUFNO29CQUNKYSxJQUFJYixLQUFLYSxFQUFFO29CQUNYQyxPQUFPZCxLQUFLYyxLQUFLO29CQUNqQkMsTUFBTWYsS0FBS2UsSUFBSTtvQkFDZkMsV0FBV2hCLEtBQUtpQixVQUFVO29CQUMxQkMsVUFBVWxCLEtBQUttQixTQUFTO29CQUN4QkMsVUFBVXBCLEtBQUtRLFNBQVM7b0JBQ3hCYSxZQUFZckIsS0FBS3NCLFdBQVc7b0JBQzVCQyxjQUFjWixLQUFLQyxHQUFHO29CQUN0QlksYUFBYXhCLEtBQUt3QixXQUFXLElBQUksRUFBRTtnQkFDckM7WUFDRjtRQUNGO1FBRUEscUJBQXFCO1FBQ3JCLE1BQU03QixZQUFZUixRQUFRQyxHQUFHLENBQUNRLFVBQVUsSUFBSTtRQUM1QyxNQUFNRixRQUFRWix3REFBUSxDQUNwQjtZQUNFdUIsUUFBUUwsS0FBS2EsRUFBRTtZQUNmQyxPQUFPZCxLQUFLYyxLQUFLO1lBQ2pCQyxNQUFNZixLQUFLZSxJQUFJO1FBQ2pCLEdBQ0FwQixXQUNBO1lBQUVvRCxXQUFXO1FBQUs7UUFHcEIsb0JBQW9CO1FBQ3BCLE1BQU12RCxTQUNIVSxJQUFJLENBQUMsZUFDTE8sTUFBTSxDQUFDO1lBQ051QyxlQUFlLElBQUlyQyxPQUFPbUIsV0FBVztZQUNyQ3BCLGVBQWVDLEtBQUtDLEdBQUc7UUFDekIsR0FDQ1IsRUFBRSxDQUFDLE1BQU1KLEtBQUthLEVBQUU7UUFFbkIsTUFBTTVCLGlFQUFRQSxDQUFDO1lBQ2JpRCxRQUFRO1lBQ1I3QixRQUFRTCxLQUFLYSxFQUFFO1lBQ2ZvQyxVQUFVakQsS0FBS2UsSUFBSTtZQUNuQkQ7WUFDQWE7UUFDRjtRQUVBLE9BQU87WUFDTFMsU0FBUztZQUNUMUM7WUFDQU0sTUFBTTtnQkFDSmEsSUFBSWIsS0FBS2EsRUFBRTtnQkFDWEMsT0FBT2QsS0FBS2MsS0FBSztnQkFDakJDLE1BQU1mLEtBQUtlLElBQUk7Z0JBQ2ZDLFdBQVdoQixLQUFLaUIsVUFBVTtnQkFDMUJDLFVBQVVsQixLQUFLbUIsU0FBUztnQkFDeEJDLFVBQVVwQixLQUFLUSxTQUFTO2dCQUN4QmEsWUFBWXJCLEtBQUtzQixXQUFXO2dCQUM1QkMsY0FBY1osS0FBS0MsR0FBRztnQkFDdEJZLGFBQWF4QixLQUFLd0IsV0FBVyxJQUFJLEVBQUU7WUFDckM7UUFDRjtJQUNGLEVBQUUsT0FBT3ZCLE9BQU87UUFDZGlELFFBQVFqRCxLQUFLLENBQUMsc0JBQXNCQTtRQUNwQyxPQUFPO1lBQUVtQyxTQUFTO1lBQU9uQyxPQUFPO1FBQWU7SUFDakQ7QUFDRjtBQUVBOzs7Q0FHQyxHQUNNLGVBQWVrRCxrQkFDcEI5QyxNQUFjLEVBQ2QrQyxRQUFnQixFQUNoQnpCLEVBQVc7SUFFWCw0REFBNEQ7SUFDNUQsTUFBTTBCLFlBQVksTUFBTSx3SEFBTztJQUUvQixJQUFJO1FBQ0YsTUFBTSxFQUFFdEQsTUFBTUMsSUFBSSxFQUFFQyxLQUFLLEVBQUUsR0FBRyxNQUFNVCxTQUNqQ1UsSUFBSSxDQUFDLGVBQ0xDLE1BQU0sQ0FBQyxDQUFDOzs7Ozs7Ozs7O01BVVQsQ0FBQyxFQUNBQyxFQUFFLENBQUMsTUFBTUMsUUFDVEMsTUFBTTtRQUVULElBQUlMLFNBQVMsQ0FBQ0QsUUFBUSxDQUFDQSxLQUFLNEMsVUFBVSxFQUFFO1lBQ3RDLE9BQU87Z0JBQUVSLFNBQVM7Z0JBQU9uQyxPQUFPO1lBQW9CO1FBQ3REO1FBRUEsbUJBQW1CO1FBQ25CLE1BQU1xRCxXQUFXRCxVQUFVRSxJQUFJLENBQUN6RCxNQUFNLENBQUM7WUFDckMwRCxRQUFReEQsS0FBSzRDLFVBQVU7WUFDdkJhLFVBQVU7WUFDVi9ELE9BQU8wRDtZQUNQTSxRQUFRO1FBQ1Y7UUFFQSxJQUFJLENBQUNKLFVBQVU7WUFDYixNQUFNckUsaUVBQVFBLENBQUM7Z0JBQ2JpRCxRQUFRO2dCQUNSN0IsUUFBUUwsS0FBS2EsRUFBRTtnQkFDZkMsT0FBT2QsS0FBS2MsS0FBSztnQkFDakJhO2dCQUNBUSxRQUFRO1lBQ1Y7WUFDQSxPQUFPO2dCQUFFQyxTQUFTO2dCQUFPbkMsT0FBTztZQUFvQjtRQUN0RDtRQUVBLHFCQUFxQjtRQUNyQixNQUFNTixZQUFZUixRQUFRQyxHQUFHLENBQUNRLFVBQVUsSUFBSTtRQUM1QyxNQUFNRixRQUFRWix3REFBUSxDQUNwQjtZQUNFdUIsUUFBUUwsS0FBS2EsRUFBRTtZQUNmQyxPQUFPZCxLQUFLYyxLQUFLO1lBQ2pCQyxNQUFNZixLQUFLZSxJQUFJO1FBQ2pCLEdBQ0FwQixXQUNBO1lBQUVvRCxXQUFXO1FBQUs7UUFHcEIsb0JBQW9CO1FBQ3BCLE1BQU12RCxTQUNIVSxJQUFJLENBQUMsZUFDTE8sTUFBTSxDQUFDO1lBQ051QyxlQUFlLElBQUlyQyxPQUFPbUIsV0FBVztZQUNyQ3BCLGVBQWVDLEtBQUtDLEdBQUc7UUFDekIsR0FDQ1IsRUFBRSxDQUFDLE1BQU1KLEtBQUthLEVBQUU7UUFFbkIsTUFBTTVCLGlFQUFRQSxDQUFDO1lBQ2JpRCxRQUFRO1lBQ1I3QixRQUFRTCxLQUFLYSxFQUFFO1lBQ2ZvQyxVQUFVakQsS0FBS2UsSUFBSTtZQUNuQkQsT0FBT2QsS0FBS2MsS0FBSztZQUNqQmE7UUFDRjtRQUVBLE9BQU87WUFDTFMsU0FBUztZQUNUMUM7WUFDQU0sTUFBTTtnQkFDSmEsSUFBSWIsS0FBS2EsRUFBRTtnQkFDWEMsT0FBT2QsS0FBS2MsS0FBSztnQkFDakJDLE1BQU1mLEtBQUtlLElBQUk7Z0JBQ2ZDLFdBQVdoQixLQUFLaUIsVUFBVTtnQkFDMUJDLFVBQVVsQixLQUFLbUIsU0FBUztnQkFDeEJDLFVBQVVwQixLQUFLUSxTQUFTO2dCQUN4QmEsWUFBWXJCLEtBQUtzQixXQUFXO2dCQUM1QkMsY0FBY1osS0FBS0MsR0FBRztnQkFDdEJZLGFBQWF4QixLQUFLd0IsV0FBVyxJQUFJLEVBQUU7WUFDckM7UUFDRjtJQUNGLEVBQUUsT0FBT3ZCLE9BQU87UUFDZGlELFFBQVFqRCxLQUFLLENBQUMsMkJBQTJCQTtRQUN6QyxPQUFPO1lBQUVtQyxTQUFTO1lBQU9uQyxPQUFPO1FBQTBCO0lBQzVEO0FBQ0Y7QUFFQTs7O0NBR0MsR0FDTSxlQUFlMEQsa0JBQWtCdEQsTUFBYztJQUNwRCw0REFBNEQ7SUFDNUQsTUFBTWdELFlBQVksTUFBTSx3SEFBTztJQUUvQixJQUFJO1FBQ0YsTUFBTSxFQUFFdEQsTUFBTUMsSUFBSSxFQUFFLEdBQUcsTUFBTVIsU0FDMUJVLElBQUksQ0FBQyxlQUNMQyxNQUFNLENBQUMsZ0NBQ1BDLEVBQUUsQ0FBQyxNQUFNQyxRQUNUQyxNQUFNO1FBRVQsSUFBSSxDQUFDTixNQUFNLE9BQU87UUFFbEIsTUFBTXdELFNBQVNILFVBQVVPLGNBQWMsQ0FBQztZQUN0Q0MsTUFBTSxDQUFDLEVBQUU3RCxLQUFLaUIsVUFBVSxDQUFDLENBQUMsRUFBRWpCLEtBQUttQixTQUFTLENBQUMsQ0FBQztZQUM1QzJDLFFBQVE7WUFDUjdCLFFBQVE7UUFDVjtRQUVBLE9BQU87WUFDTHVCLFFBQVFBLE9BQU9PLE1BQU07WUFDckJDLFFBQVFSLE9BQU9TLFdBQVc7UUFDNUI7SUFDRixFQUFFLE9BQU9oRSxPQUFPO1FBQ2RpRCxRQUFRakQsS0FBSyxDQUFDLGdDQUFnQ0E7UUFDOUMsT0FBTztJQUNUO0FBQ0Y7QUFFQTs7O0NBR0MsR0FDTSxlQUFlaUUsVUFBVTdELE1BQWMsRUFBRW1ELE1BQWMsRUFBRTlELEtBQWE7SUFDM0UsNERBQTREO0lBQzVELE1BQU0yRCxZQUFZLE1BQU0sd0hBQU87SUFFL0IsSUFBSTtRQUNGLHlCQUF5QjtRQUN6QixNQUFNQyxXQUFXRCxVQUFVRSxJQUFJLENBQUN6RCxNQUFNLENBQUM7WUFDckMwRDtZQUNBQyxVQUFVO1lBQ1YvRDtZQUNBZ0UsUUFBUTtRQUNWO1FBRUEsSUFBSSxDQUFDSixVQUFVLE9BQU87UUFFdEIsOEJBQThCO1FBQzlCLE1BQU0sRUFBRXJELEtBQUssRUFBRSxHQUFHLE1BQU1ULFNBQ3JCVSxJQUFJLENBQUMsZUFDTE8sTUFBTSxDQUFDO1lBQ05tQyxZQUFZWTtZQUNabEMsYUFBYTtRQUNmLEdBQ0NsQixFQUFFLENBQUMsTUFBTUM7UUFFWixJQUFJSixPQUFPLE9BQU87UUFFbEIsTUFBTWhCLGlFQUFRQSxDQUFDO1lBQ2JpRCxRQUFRO1lBQ1I3QjtZQUNBOEIsUUFBUTtRQUNWO1FBRUEsT0FBTztJQUNULEVBQUUsT0FBT2xDLE9BQU87UUFDZGlELFFBQVFqRCxLQUFLLENBQUMscUJBQXFCQTtRQUNuQyxPQUFPO0lBQ1Q7QUFDRjtBQUVBOztDQUVDLEdBQ0QsZUFBZXFDLG9CQUFvQnhCLEtBQWEsRUFBRWEsRUFBVyxFQUFFUSxNQUFlO0lBQzVFLE1BQU0zQyxTQUNIVSxJQUFJLENBQUMsa0JBQ0xpRSxNQUFNLENBQUM7UUFDTnJEO1FBQ0FzRCxZQUFZekM7UUFDWlMsU0FBUztRQUNURDtRQUNBa0MsWUFBWSxJQUFJMUQsT0FBT21CLFdBQVc7SUFDcEM7SUFFRixNQUFNN0MsaUVBQVFBLENBQUM7UUFDYmlELFFBQVE7UUFDUnBCO1FBQ0FhO1FBQ0FRO0lBQ0Y7QUFDRjtBQUVBOztDQUVDLEdBQ00sZUFBZW1DLFlBQVlqRSxNQUFjLEVBQUVzQixFQUFXO0lBQzNELElBQUk7UUFDRixNQUFNMUMsaUVBQVFBLENBQUM7WUFDYmlELFFBQVE7WUFDUjdCO1lBQ0FzQjtRQUNGO0lBQ0YsRUFBRSxPQUFPMUIsT0FBTztRQUNkaUQsUUFBUWpELEtBQUssQ0FBQyx1QkFBdUJBO0lBQ3ZDO0FBQ0Y7QUFFQTs7Q0FFQyxHQUNNLGVBQWVzRSx5QkFBeUI3RSxLQUFhO0lBQzFELE9BQU9ELGlCQUFpQkM7QUFDMUIiLCJzb3VyY2VzIjpbIndlYnBhY2s6Ly9vY2VhbnNvdWxzcGFya2xlcy1hZG1pbi8uL2xpYi9hdXRoL2FkbWluLWF1dGgudHM/ZTk5MiJdLCJzb3VyY2VzQ29udGVudCI6WyJpbXBvcnQgand0IGZyb20gJ2pzb253ZWJ0b2tlbic7XHJcbmltcG9ydCBiY3J5cHQgZnJvbSAnYmNyeXB0anMnO1xyXG5pbXBvcnQgeyBjcmVhdGVDbGllbnQgfSBmcm9tICdAc3VwYWJhc2Uvc3VwYWJhc2UtanMnO1xyXG5pbXBvcnQgeyBhdWRpdExvZyB9IGZyb20gJy4uL3NlY3VyaXR5L2F1ZGl0LWxvZ2dpbmcnO1xyXG5cclxuLy8gSGFuZGxlIG1pc3NpbmcgZW52aXJvbm1lbnQgdmFyaWFibGVzIGdyYWNlZnVsbHlcclxuY29uc3Qgc3VwYWJhc2VVcmwgPSBwcm9jZXNzLmVudi5ORVhUX1BVQkxJQ19TVVBBQkFTRV9VUkwgfHwgJ2h0dHBzOi8vcGxhY2Vob2xkZXIuc3VwYWJhc2UuY28nO1xyXG5jb25zdCBzdXBhYmFzZUtleSA9IHByb2Nlc3MuZW52LlNVUEFCQVNFX1NFUlZJQ0VfUk9MRV9LRVkgfHwgJ2V5SmhiR2NpT2lKSVV6STFOaUlzSW5SNWNDSTZJa3BYVkNKOS5wbGFjZWhvbGRlcic7XHJcblxyXG5jb25zdCBzdXBhYmFzZSA9IGNyZWF0ZUNsaWVudChzdXBhYmFzZVVybCwgc3VwYWJhc2VLZXkpO1xyXG5cclxuZXhwb3J0IGludGVyZmFjZSBBZG1pblVzZXIge1xyXG4gIGlkOiBzdHJpbmc7XHJcbiAgZW1haWw6IHN0cmluZztcclxuICByb2xlOiAnREVWJyB8ICdBZG1pbicgfCAnQXJ0aXN0JyB8ICdCcmFpZGVyJztcclxuICBmaXJzdE5hbWU6IHN0cmluZztcclxuICBsYXN0TmFtZTogc3RyaW5nO1xyXG4gIGlzQWN0aXZlOiBib29sZWFuO1xyXG4gIG1mYUVuYWJsZWQ6IGJvb2xlYW47XHJcbiAgbGFzdEFjdGl2aXR5OiBudW1iZXI7XHJcbiAgcGVybWlzc2lvbnM6IHN0cmluZ1tdO1xyXG59XHJcblxyXG5leHBvcnQgaW50ZXJmYWNlIEF1dGhSZXN1bHQge1xyXG4gIHZhbGlkOiBib29sZWFuO1xyXG4gIHVzZXI/OiBBZG1pblVzZXI7XHJcbiAgZXJyb3I/OiBzdHJpbmc7XHJcbn1cclxuXHJcbmV4cG9ydCBpbnRlcmZhY2UgTG9naW5SZXN1bHQge1xyXG4gIHN1Y2Nlc3M6IGJvb2xlYW47XHJcbiAgdG9rZW4/OiBzdHJpbmc7XHJcbiAgdXNlcj86IEFkbWluVXNlcjtcclxuICByZXF1aXJlc01GQT86IGJvb2xlYW47XHJcbiAgbWZhU2VjcmV0Pzogc3RyaW5nO1xyXG4gIGVycm9yPzogc3RyaW5nO1xyXG59XHJcblxyXG4vKipcclxuICogVmVyaWZ5IGFkbWluIGF1dGhlbnRpY2F0aW9uIHRva2VuXHJcbiAqL1xyXG5leHBvcnQgYXN5bmMgZnVuY3Rpb24gdmVyaWZ5QWRtaW5Ub2tlbih0b2tlbjogc3RyaW5nKTogUHJvbWlzZTxBdXRoUmVzdWx0PiB7XHJcbiAgdHJ5IHtcclxuICAgIC8vIEhhbmRsZSBtaXNzaW5nIEpXVCBzZWNyZXQgZ3JhY2VmdWxseVxyXG4gICAgY29uc3Qgand0U2VjcmV0ID0gcHJvY2Vzcy5lbnYuSldUX1NFQ1JFVCB8fCAncGxhY2Vob2xkZXItc2VjcmV0JztcclxuICAgIGNvbnN0IGRlY29kZWQgPSBqd3QudmVyaWZ5KHRva2VuLCBqd3RTZWNyZXQpIGFzIGFueTtcclxuICAgIFxyXG4gICAgLy8gR2V0IHVzZXIgZnJvbSBkYXRhYmFzZSB3aXRoIGxhdGVzdCBpbmZvXHJcbiAgICBjb25zdCB7IGRhdGE6IHVzZXIsIGVycm9yIH0gPSBhd2FpdCBzdXBhYmFzZVxyXG4gICAgICAuZnJvbSgnYWRtaW5fdXNlcnMnKVxyXG4gICAgICAuc2VsZWN0KGBcclxuICAgICAgICBpZCxcclxuICAgICAgICBlbWFpbCxcclxuICAgICAgICByb2xlLFxyXG4gICAgICAgIGZpcnN0X25hbWUsXHJcbiAgICAgICAgbGFzdF9uYW1lLFxyXG4gICAgICAgIGlzX2FjdGl2ZSxcclxuICAgICAgICBtZmFfZW5hYmxlZCxcclxuICAgICAgICBsYXN0X2FjdGl2aXR5LFxyXG4gICAgICAgIHBlcm1pc3Npb25zXHJcbiAgICAgIGApXHJcbiAgICAgIC5lcSgnaWQnLCBkZWNvZGVkLnVzZXJJZClcclxuICAgICAgLmVxKCdpc19hY3RpdmUnLCB0cnVlKVxyXG4gICAgICAuc2luZ2xlKCk7XHJcblxyXG4gICAgaWYgKGVycm9yIHx8ICF1c2VyKSB7XHJcbiAgICAgIHJldHVybiB7IHZhbGlkOiBmYWxzZSwgZXJyb3I6ICdVc2VyIG5vdCBmb3VuZCBvciBpbmFjdGl2ZScgfTtcclxuICAgIH1cclxuXHJcbiAgICAvLyBDaGVjayBpZiB1c2VyIGlzIHN0aWxsIGFjdGl2ZVxyXG4gICAgaWYgKCF1c2VyLmlzX2FjdGl2ZSkge1xyXG4gICAgICByZXR1cm4geyB2YWxpZDogZmFsc2UsIGVycm9yOiAnVXNlciBhY2NvdW50IGlzIGRlYWN0aXZhdGVkJyB9O1xyXG4gICAgfVxyXG5cclxuICAgIC8vIFVwZGF0ZSBsYXN0IGFjdGl2aXR5XHJcbiAgICBhd2FpdCBzdXBhYmFzZVxyXG4gICAgICAuZnJvbSgnYWRtaW5fdXNlcnMnKVxyXG4gICAgICAudXBkYXRlKHsgbGFzdF9hY3Rpdml0eTogRGF0ZS5ub3coKSB9KVxyXG4gICAgICAuZXEoJ2lkJywgdXNlci5pZCk7XHJcblxyXG4gICAgcmV0dXJuIHtcclxuICAgICAgdmFsaWQ6IHRydWUsXHJcbiAgICAgIHVzZXI6IHtcclxuICAgICAgICBpZDogdXNlci5pZCxcclxuICAgICAgICBlbWFpbDogdXNlci5lbWFpbCxcclxuICAgICAgICByb2xlOiB1c2VyLnJvbGUsXHJcbiAgICAgICAgZmlyc3ROYW1lOiB1c2VyLmZpcnN0X25hbWUsXHJcbiAgICAgICAgbGFzdE5hbWU6IHVzZXIubGFzdF9uYW1lLFxyXG4gICAgICAgIGlzQWN0aXZlOiB1c2VyLmlzX2FjdGl2ZSxcclxuICAgICAgICBtZmFFbmFibGVkOiB1c2VyLm1mYV9lbmFibGVkLFxyXG4gICAgICAgIGxhc3RBY3Rpdml0eTogRGF0ZS5ub3coKSxcclxuICAgICAgICBwZXJtaXNzaW9uczogdXNlci5wZXJtaXNzaW9ucyB8fCBbXVxyXG4gICAgICB9XHJcbiAgICB9O1xyXG4gIH0gY2F0Y2ggKGVycm9yKSB7XHJcbiAgICByZXR1cm4geyB2YWxpZDogZmFsc2UsIGVycm9yOiAnSW52YWxpZCB0b2tlbicgfTtcclxuICB9XHJcbn1cclxuXHJcbi8qKlxyXG4gKiBBZG1pbiBsb2dpbiB3aXRoIGVtYWlsIGFuZCBwYXNzd29yZFxyXG4gKi9cclxuZXhwb3J0IGFzeW5jIGZ1bmN0aW9uIGFkbWluTG9naW4oXHJcbiAgZW1haWw6IHN0cmluZyxcclxuICBwYXNzd29yZDogc3RyaW5nLFxyXG4gIGlwPzogc3RyaW5nXHJcbik6IFByb21pc2U8TG9naW5SZXN1bHQ+IHtcclxuICB0cnkge1xyXG4gICAgLy8gQ2hlY2sgZm9yIHJhdGUgbGltaXRpbmdcclxuICAgIGNvbnN0IHsgZGF0YTogYXR0ZW1wdHMgfSA9IGF3YWl0IHN1cGFiYXNlXHJcbiAgICAgIC5mcm9tKCdsb2dpbl9hdHRlbXB0cycpXHJcbiAgICAgIC5zZWxlY3QoJyonKVxyXG4gICAgICAuZXEoJ2VtYWlsJywgZW1haWwpXHJcbiAgICAgIC5ndGUoJ2NyZWF0ZWRfYXQnLCBuZXcgRGF0ZShEYXRlLm5vdygpIC0gMTUgKiA2MCAqIDEwMDApLnRvSVNPU3RyaW5nKCkpXHJcbiAgICAgIC5vcmRlcignY3JlYXRlZF9hdCcsIHsgYXNjZW5kaW5nOiBmYWxzZSB9KTtcclxuXHJcbiAgICBpZiAoYXR0ZW1wdHMgJiYgYXR0ZW1wdHMubGVuZ3RoID49IDUpIHtcclxuICAgICAgYXdhaXQgYXVkaXRMb2coe1xyXG4gICAgICAgIGFjdGlvbjogJ0xPR0lOX0JMT0NLRUQnLFxyXG4gICAgICAgIGVtYWlsLFxyXG4gICAgICAgIGlwLFxyXG4gICAgICAgIHJlYXNvbjogJ1RvbyBtYW55IGZhaWxlZCBhdHRlbXB0cydcclxuICAgICAgfSk7XHJcbiAgICAgIHJldHVybiB7IHN1Y2Nlc3M6IGZhbHNlLCBlcnJvcjogJ0FjY291bnQgdGVtcG9yYXJpbHkgbG9ja2VkIGR1ZSB0byB0b28gbWFueSBmYWlsZWQgYXR0ZW1wdHMnIH07XHJcbiAgICB9XHJcblxyXG4gICAgLy8gR2V0IHVzZXIgZnJvbSBkYXRhYmFzZVxyXG4gICAgY29uc3QgeyBkYXRhOiB1c2VyLCBlcnJvciB9ID0gYXdhaXQgc3VwYWJhc2VcclxuICAgICAgLmZyb20oJ2FkbWluX3VzZXJzJylcclxuICAgICAgLnNlbGVjdChgXHJcbiAgICAgICAgaWQsXHJcbiAgICAgICAgZW1haWwsXHJcbiAgICAgICAgcGFzc3dvcmRfaGFzaCxcclxuICAgICAgICByb2xlLFxyXG4gICAgICAgIGZpcnN0X25hbWUsXHJcbiAgICAgICAgbGFzdF9uYW1lLFxyXG4gICAgICAgIGlzX2FjdGl2ZSxcclxuICAgICAgICBtZmFfZW5hYmxlZCxcclxuICAgICAgICBtZmFfc2VjcmV0LFxyXG4gICAgICAgIHBlcm1pc3Npb25zXHJcbiAgICAgIGApXHJcbiAgICAgIC5lcSgnZW1haWwnLCBlbWFpbC50b0xvd2VyQ2FzZSgpKVxyXG4gICAgICAuc2luZ2xlKCk7XHJcblxyXG5cclxuXHJcbiAgICBpZiAoZXJyb3IgfHwgIXVzZXIpIHtcclxuICAgICAgYXdhaXQgcmVjb3JkRmFpbGVkQXR0ZW1wdChlbWFpbCwgaXAsIGVycm9yID8gYERhdGFiYXNlIGVycm9yOiAke2Vycm9yLm1lc3NhZ2V9YCA6ICdVc2VyIG5vdCBmb3VuZCcpO1xyXG4gICAgICByZXR1cm4geyBzdWNjZXNzOiBmYWxzZSwgZXJyb3I6ICdJbnZhbGlkIGNyZWRlbnRpYWxzJyB9O1xyXG4gICAgfVxyXG5cclxuICAgIC8vIENoZWNrIGlmIHVzZXIgaXMgYWN0aXZlXHJcbiAgICBpZiAoIXVzZXIuaXNfYWN0aXZlKSB7XHJcbiAgICAgIGF3YWl0IGF1ZGl0TG9nKHtcclxuICAgICAgICBhY3Rpb246ICdMT0dJTl9ERU5JRUQnLFxyXG4gICAgICAgIHVzZXJJZDogdXNlci5pZCxcclxuICAgICAgICBlbWFpbCxcclxuICAgICAgICBpcCxcclxuICAgICAgICByZWFzb246ICdBY2NvdW50IGRlYWN0aXZhdGVkJ1xyXG4gICAgICB9KTtcclxuICAgICAgcmV0dXJuIHsgc3VjY2VzczogZmFsc2UsIGVycm9yOiAnQWNjb3VudCBpcyBkZWFjdGl2YXRlZCcgfTtcclxuICAgIH1cclxuXHJcbiAgICAvLyBWZXJpZnkgcGFzc3dvcmRcclxuICAgIGNvbnN0IHBhc3N3b3JkVmFsaWQgPSBhd2FpdCBiY3J5cHQuY29tcGFyZShwYXNzd29yZCwgdXNlci5wYXNzd29yZF9oYXNoKTtcclxuICAgIGlmICghcGFzc3dvcmRWYWxpZCkge1xyXG4gICAgICBhd2FpdCByZWNvcmRGYWlsZWRBdHRlbXB0KGVtYWlsLCBpcCwgJ0ludmFsaWQgcGFzc3dvcmQnKTtcclxuICAgICAgcmV0dXJuIHsgc3VjY2VzczogZmFsc2UsIGVycm9yOiAnSW52YWxpZCBjcmVkZW50aWFscycgfTtcclxuICAgIH1cclxuXHJcbiAgICAvLyBDbGVhciBmYWlsZWQgYXR0ZW1wdHMgb24gc3VjY2Vzc2Z1bCBwYXNzd29yZCB2ZXJpZmljYXRpb25cclxuICAgIGF3YWl0IHN1cGFiYXNlXHJcbiAgICAgIC5mcm9tKCdsb2dpbl9hdHRlbXB0cycpXHJcbiAgICAgIC5kZWxldGUoKVxyXG4gICAgICAuZXEoJ2VtYWlsJywgZW1haWwpO1xyXG5cclxuICAgIC8vIENoZWNrIGlmIE1GQSBpcyByZXF1aXJlZFxyXG4gICAgaWYgKHVzZXIubWZhX2VuYWJsZWQgJiYgdXNlci5tZmFfc2VjcmV0KSB7XHJcbiAgICAgIC8vIFJldHVybiBzdWNjZXNzIGJ1dCBpbmRpY2F0ZSBNRkEgaXMgcmVxdWlyZWRcclxuICAgICAgcmV0dXJuIHtcclxuICAgICAgICBzdWNjZXNzOiB0cnVlLFxyXG4gICAgICAgIHJlcXVpcmVzTUZBOiB0cnVlLFxyXG4gICAgICAgIHVzZXI6IHtcclxuICAgICAgICAgIGlkOiB1c2VyLmlkLFxyXG4gICAgICAgICAgZW1haWw6IHVzZXIuZW1haWwsXHJcbiAgICAgICAgICByb2xlOiB1c2VyLnJvbGUsXHJcbiAgICAgICAgICBmaXJzdE5hbWU6IHVzZXIuZmlyc3RfbmFtZSxcclxuICAgICAgICAgIGxhc3ROYW1lOiB1c2VyLmxhc3RfbmFtZSxcclxuICAgICAgICAgIGlzQWN0aXZlOiB1c2VyLmlzX2FjdGl2ZSxcclxuICAgICAgICAgIG1mYUVuYWJsZWQ6IHVzZXIubWZhX2VuYWJsZWQsXHJcbiAgICAgICAgICBsYXN0QWN0aXZpdHk6IERhdGUubm93KCksXHJcbiAgICAgICAgICBwZXJtaXNzaW9uczogdXNlci5wZXJtaXNzaW9ucyB8fCBbXVxyXG4gICAgICAgIH1cclxuICAgICAgfTtcclxuICAgIH1cclxuXHJcbiAgICAvLyBHZW5lcmF0ZSBKV1QgdG9rZW5cclxuICAgIGNvbnN0IGp3dFNlY3JldCA9IHByb2Nlc3MuZW52LkpXVF9TRUNSRVQgfHwgJ3BsYWNlaG9sZGVyLXNlY3JldCc7XHJcbiAgICBjb25zdCB0b2tlbiA9IGp3dC5zaWduKFxyXG4gICAgICB7XHJcbiAgICAgICAgdXNlcklkOiB1c2VyLmlkLFxyXG4gICAgICAgIGVtYWlsOiB1c2VyLmVtYWlsLFxyXG4gICAgICAgIHJvbGU6IHVzZXIucm9sZVxyXG4gICAgICB9LFxyXG4gICAgICBqd3RTZWNyZXQsXHJcbiAgICAgIHsgZXhwaXJlc0luOiAnOGgnIH1cclxuICAgICk7XHJcblxyXG4gICAgLy8gVXBkYXRlIGxhc3QgbG9naW5cclxuICAgIGF3YWl0IHN1cGFiYXNlXHJcbiAgICAgIC5mcm9tKCdhZG1pbl91c2VycycpXHJcbiAgICAgIC51cGRhdGUoe1xyXG4gICAgICAgIGxhc3RfbG9naW5fYXQ6IG5ldyBEYXRlKCkudG9JU09TdHJpbmcoKSxcclxuICAgICAgICBsYXN0X2FjdGl2aXR5OiBEYXRlLm5vdygpXHJcbiAgICAgIH0pXHJcbiAgICAgIC5lcSgnaWQnLCB1c2VyLmlkKTtcclxuXHJcbiAgICBhd2FpdCBhdWRpdExvZyh7XHJcbiAgICAgIGFjdGlvbjogJ0xPR0lOX1NVQ0NFU1MnLFxyXG4gICAgICB1c2VySWQ6IHVzZXIuaWQsXHJcbiAgICAgIHVzZXJSb2xlOiB1c2VyLnJvbGUsXHJcbiAgICAgIGVtYWlsLFxyXG4gICAgICBpcFxyXG4gICAgfSk7XHJcblxyXG4gICAgcmV0dXJuIHtcclxuICAgICAgc3VjY2VzczogdHJ1ZSxcclxuICAgICAgdG9rZW4sXHJcbiAgICAgIHVzZXI6IHtcclxuICAgICAgICBpZDogdXNlci5pZCxcclxuICAgICAgICBlbWFpbDogdXNlci5lbWFpbCxcclxuICAgICAgICByb2xlOiB1c2VyLnJvbGUsXHJcbiAgICAgICAgZmlyc3ROYW1lOiB1c2VyLmZpcnN0X25hbWUsXHJcbiAgICAgICAgbGFzdE5hbWU6IHVzZXIubGFzdF9uYW1lLFxyXG4gICAgICAgIGlzQWN0aXZlOiB1c2VyLmlzX2FjdGl2ZSxcclxuICAgICAgICBtZmFFbmFibGVkOiB1c2VyLm1mYV9lbmFibGVkLFxyXG4gICAgICAgIGxhc3RBY3Rpdml0eTogRGF0ZS5ub3coKSxcclxuICAgICAgICBwZXJtaXNzaW9uczogdXNlci5wZXJtaXNzaW9ucyB8fCBbXVxyXG4gICAgICB9XHJcbiAgICB9O1xyXG4gIH0gY2F0Y2ggKGVycm9yKSB7XHJcbiAgICBjb25zb2xlLmVycm9yKCdBZG1pbiBsb2dpbiBlcnJvcjonLCBlcnJvcik7XHJcbiAgICByZXR1cm4geyBzdWNjZXNzOiBmYWxzZSwgZXJyb3I6ICdMb2dpbiBmYWlsZWQnIH07XHJcbiAgfVxyXG59XHJcblxyXG4vKipcclxuICogVmVyaWZ5IE1GQSB0b2tlbiBhbmQgY29tcGxldGUgbG9naW5cclxuICogTm90ZTogVGhpcyBmdW5jdGlvbiByZXF1aXJlcyBOb2RlLmpzIHJ1bnRpbWUgZHVlIHRvIHNwZWFrZWFzeSBkZXBlbmRlbmN5XHJcbiAqL1xyXG5leHBvcnQgYXN5bmMgZnVuY3Rpb24gdmVyaWZ5TUZBQW5kTG9naW4oXHJcbiAgdXNlcklkOiBzdHJpbmcsXHJcbiAgbWZhVG9rZW46IHN0cmluZyxcclxuICBpcD86IHN0cmluZ1xyXG4pOiBQcm9taXNlPExvZ2luUmVzdWx0PiB7XHJcbiAgLy8gSW1wb3J0IHNwZWFrZWFzeSBkeW5hbWljYWxseSB0byBhdm9pZCBFZGdlIFJ1bnRpbWUgaXNzdWVzXHJcbiAgY29uc3Qgc3BlYWtlYXN5ID0gYXdhaXQgaW1wb3J0KCdzcGVha2Vhc3knKTtcclxuXHJcbiAgdHJ5IHtcclxuICAgIGNvbnN0IHsgZGF0YTogdXNlciwgZXJyb3IgfSA9IGF3YWl0IHN1cGFiYXNlXHJcbiAgICAgIC5mcm9tKCdhZG1pbl91c2VycycpXHJcbiAgICAgIC5zZWxlY3QoYFxyXG4gICAgICAgIGlkLFxyXG4gICAgICAgIGVtYWlsLFxyXG4gICAgICAgIHJvbGUsXHJcbiAgICAgICAgZmlyc3RfbmFtZSxcclxuICAgICAgICBsYXN0X25hbWUsXHJcbiAgICAgICAgaXNfYWN0aXZlLFxyXG4gICAgICAgIG1mYV9lbmFibGVkLFxyXG4gICAgICAgIG1mYV9zZWNyZXQsXHJcbiAgICAgICAgcGVybWlzc2lvbnNcclxuICAgICAgYClcclxuICAgICAgLmVxKCdpZCcsIHVzZXJJZClcclxuICAgICAgLnNpbmdsZSgpO1xyXG5cclxuICAgIGlmIChlcnJvciB8fCAhdXNlciB8fCAhdXNlci5tZmFfc2VjcmV0KSB7XHJcbiAgICAgIHJldHVybiB7IHN1Y2Nlc3M6IGZhbHNlLCBlcnJvcjogJ0ludmFsaWQgTUZBIHNldHVwJyB9O1xyXG4gICAgfVxyXG5cclxuICAgIC8vIFZlcmlmeSBNRkEgdG9rZW5cclxuICAgIGNvbnN0IHZlcmlmaWVkID0gc3BlYWtlYXN5LnRvdHAudmVyaWZ5KHtcclxuICAgICAgc2VjcmV0OiB1c2VyLm1mYV9zZWNyZXQsXHJcbiAgICAgIGVuY29kaW5nOiAnYmFzZTMyJyxcclxuICAgICAgdG9rZW46IG1mYVRva2VuLFxyXG4gICAgICB3aW5kb3c6IDJcclxuICAgIH0pO1xyXG5cclxuICAgIGlmICghdmVyaWZpZWQpIHtcclxuICAgICAgYXdhaXQgYXVkaXRMb2coe1xyXG4gICAgICAgIGFjdGlvbjogJ01GQV9GQUlMRUQnLFxyXG4gICAgICAgIHVzZXJJZDogdXNlci5pZCxcclxuICAgICAgICBlbWFpbDogdXNlci5lbWFpbCxcclxuICAgICAgICBpcCxcclxuICAgICAgICByZWFzb246ICdJbnZhbGlkIE1GQSB0b2tlbidcclxuICAgICAgfSk7XHJcbiAgICAgIHJldHVybiB7IHN1Y2Nlc3M6IGZhbHNlLCBlcnJvcjogJ0ludmFsaWQgTUZBIHRva2VuJyB9O1xyXG4gICAgfVxyXG5cclxuICAgIC8vIEdlbmVyYXRlIEpXVCB0b2tlblxyXG4gICAgY29uc3Qgand0U2VjcmV0ID0gcHJvY2Vzcy5lbnYuSldUX1NFQ1JFVCB8fCAncGxhY2Vob2xkZXItc2VjcmV0JztcclxuICAgIGNvbnN0IHRva2VuID0gand0LnNpZ24oXHJcbiAgICAgIHtcclxuICAgICAgICB1c2VySWQ6IHVzZXIuaWQsXHJcbiAgICAgICAgZW1haWw6IHVzZXIuZW1haWwsXHJcbiAgICAgICAgcm9sZTogdXNlci5yb2xlXHJcbiAgICAgIH0sXHJcbiAgICAgIGp3dFNlY3JldCxcclxuICAgICAgeyBleHBpcmVzSW46ICc4aCcgfVxyXG4gICAgKTtcclxuXHJcbiAgICAvLyBVcGRhdGUgbGFzdCBsb2dpblxyXG4gICAgYXdhaXQgc3VwYWJhc2VcclxuICAgICAgLmZyb20oJ2FkbWluX3VzZXJzJylcclxuICAgICAgLnVwZGF0ZSh7XHJcbiAgICAgICAgbGFzdF9sb2dpbl9hdDogbmV3IERhdGUoKS50b0lTT1N0cmluZygpLFxyXG4gICAgICAgIGxhc3RfYWN0aXZpdHk6IERhdGUubm93KClcclxuICAgICAgfSlcclxuICAgICAgLmVxKCdpZCcsIHVzZXIuaWQpO1xyXG5cclxuICAgIGF3YWl0IGF1ZGl0TG9nKHtcclxuICAgICAgYWN0aW9uOiAnTUZBX0xPR0lOX1NVQ0NFU1MnLFxyXG4gICAgICB1c2VySWQ6IHVzZXIuaWQsXHJcbiAgICAgIHVzZXJSb2xlOiB1c2VyLnJvbGUsXHJcbiAgICAgIGVtYWlsOiB1c2VyLmVtYWlsLFxyXG4gICAgICBpcFxyXG4gICAgfSk7XHJcblxyXG4gICAgcmV0dXJuIHtcclxuICAgICAgc3VjY2VzczogdHJ1ZSxcclxuICAgICAgdG9rZW4sXHJcbiAgICAgIHVzZXI6IHtcclxuICAgICAgICBpZDogdXNlci5pZCxcclxuICAgICAgICBlbWFpbDogdXNlci5lbWFpbCxcclxuICAgICAgICByb2xlOiB1c2VyLnJvbGUsXHJcbiAgICAgICAgZmlyc3ROYW1lOiB1c2VyLmZpcnN0X25hbWUsXHJcbiAgICAgICAgbGFzdE5hbWU6IHVzZXIubGFzdF9uYW1lLFxyXG4gICAgICAgIGlzQWN0aXZlOiB1c2VyLmlzX2FjdGl2ZSxcclxuICAgICAgICBtZmFFbmFibGVkOiB1c2VyLm1mYV9lbmFibGVkLFxyXG4gICAgICAgIGxhc3RBY3Rpdml0eTogRGF0ZS5ub3coKSxcclxuICAgICAgICBwZXJtaXNzaW9uczogdXNlci5wZXJtaXNzaW9ucyB8fCBbXVxyXG4gICAgICB9XHJcbiAgICB9O1xyXG4gIH0gY2F0Y2ggKGVycm9yKSB7XHJcbiAgICBjb25zb2xlLmVycm9yKCdNRkEgdmVyaWZpY2F0aW9uIGVycm9yOicsIGVycm9yKTtcclxuICAgIHJldHVybiB7IHN1Y2Nlc3M6IGZhbHNlLCBlcnJvcjogJ01GQSB2ZXJpZmljYXRpb24gZmFpbGVkJyB9O1xyXG4gIH1cclxufVxyXG5cclxuLyoqXHJcbiAqIEdlbmVyYXRlIE1GQSBzZWNyZXQgZm9yIHVzZXJcclxuICogTm90ZTogVGhpcyBmdW5jdGlvbiByZXF1aXJlcyBOb2RlLmpzIHJ1bnRpbWUgZHVlIHRvIHNwZWFrZWFzeSBkZXBlbmRlbmN5XHJcbiAqL1xyXG5leHBvcnQgYXN5bmMgZnVuY3Rpb24gZ2VuZXJhdGVNRkFTZWNyZXQodXNlcklkOiBzdHJpbmcpOiBQcm9taXNlPHsgc2VjcmV0OiBzdHJpbmc7IHFyQ29kZTogc3RyaW5nIH0gfCBudWxsPiB7XHJcbiAgLy8gSW1wb3J0IHNwZWFrZWFzeSBkeW5hbWljYWxseSB0byBhdm9pZCBFZGdlIFJ1bnRpbWUgaXNzdWVzXHJcbiAgY29uc3Qgc3BlYWtlYXN5ID0gYXdhaXQgaW1wb3J0KCdzcGVha2Vhc3knKTtcclxuXHJcbiAgdHJ5IHtcclxuICAgIGNvbnN0IHsgZGF0YTogdXNlciB9ID0gYXdhaXQgc3VwYWJhc2VcclxuICAgICAgLmZyb20oJ2FkbWluX3VzZXJzJylcclxuICAgICAgLnNlbGVjdCgnZW1haWwsIGZpcnN0X25hbWUsIGxhc3RfbmFtZScpXHJcbiAgICAgIC5lcSgnaWQnLCB1c2VySWQpXHJcbiAgICAgIC5zaW5nbGUoKTtcclxuXHJcbiAgICBpZiAoIXVzZXIpIHJldHVybiBudWxsO1xyXG5cclxuICAgIGNvbnN0IHNlY3JldCA9IHNwZWFrZWFzeS5nZW5lcmF0ZVNlY3JldCh7XHJcbiAgICAgIG5hbWU6IGAke3VzZXIuZmlyc3RfbmFtZX0gJHt1c2VyLmxhc3RfbmFtZX1gLFxyXG4gICAgICBpc3N1ZXI6ICdPY2VhbiBTb3VsIFNwYXJrbGVzIEFkbWluJyxcclxuICAgICAgbGVuZ3RoOiAzMlxyXG4gICAgfSk7XHJcblxyXG4gICAgcmV0dXJuIHtcclxuICAgICAgc2VjcmV0OiBzZWNyZXQuYmFzZTMyLFxyXG4gICAgICBxckNvZGU6IHNlY3JldC5vdHBhdXRoX3VybCFcclxuICAgIH07XHJcbiAgfSBjYXRjaCAoZXJyb3IpIHtcclxuICAgIGNvbnNvbGUuZXJyb3IoJ01GQSBzZWNyZXQgZ2VuZXJhdGlvbiBlcnJvcjonLCBlcnJvcik7XHJcbiAgICByZXR1cm4gbnVsbDtcclxuICB9XHJcbn1cclxuXHJcbi8qKlxyXG4gKiBFbmFibGUgTUZBIGZvciB1c2VyXHJcbiAqIE5vdGU6IFRoaXMgZnVuY3Rpb24gcmVxdWlyZXMgTm9kZS5qcyBydW50aW1lIGR1ZSB0byBzcGVha2Vhc3kgZGVwZW5kZW5jeVxyXG4gKi9cclxuZXhwb3J0IGFzeW5jIGZ1bmN0aW9uIGVuYWJsZU1GQSh1c2VySWQ6IHN0cmluZywgc2VjcmV0OiBzdHJpbmcsIHRva2VuOiBzdHJpbmcpOiBQcm9taXNlPGJvb2xlYW4+IHtcclxuICAvLyBJbXBvcnQgc3BlYWtlYXN5IGR5bmFtaWNhbGx5IHRvIGF2b2lkIEVkZ2UgUnVudGltZSBpc3N1ZXNcclxuICBjb25zdCBzcGVha2Vhc3kgPSBhd2FpdCBpbXBvcnQoJ3NwZWFrZWFzeScpO1xyXG5cclxuICB0cnkge1xyXG4gICAgLy8gVmVyaWZ5IHRoZSB0b2tlbiBmaXJzdFxyXG4gICAgY29uc3QgdmVyaWZpZWQgPSBzcGVha2Vhc3kudG90cC52ZXJpZnkoe1xyXG4gICAgICBzZWNyZXQsXHJcbiAgICAgIGVuY29kaW5nOiAnYmFzZTMyJyxcclxuICAgICAgdG9rZW4sXHJcbiAgICAgIHdpbmRvdzogMlxyXG4gICAgfSk7XHJcblxyXG4gICAgaWYgKCF2ZXJpZmllZCkgcmV0dXJuIGZhbHNlO1xyXG5cclxuICAgIC8vIFNhdmUgTUZBIHNlY3JldCB0byBkYXRhYmFzZVxyXG4gICAgY29uc3QgeyBlcnJvciB9ID0gYXdhaXQgc3VwYWJhc2VcclxuICAgICAgLmZyb20oJ2FkbWluX3VzZXJzJylcclxuICAgICAgLnVwZGF0ZSh7XHJcbiAgICAgICAgbWZhX3NlY3JldDogc2VjcmV0LFxyXG4gICAgICAgIG1mYV9lbmFibGVkOiB0cnVlXHJcbiAgICAgIH0pXHJcbiAgICAgIC5lcSgnaWQnLCB1c2VySWQpO1xyXG5cclxuICAgIGlmIChlcnJvcikgcmV0dXJuIGZhbHNlO1xyXG5cclxuICAgIGF3YWl0IGF1ZGl0TG9nKHtcclxuICAgICAgYWN0aW9uOiAnTUZBX0VOQUJMRUQnLFxyXG4gICAgICB1c2VySWQsXHJcbiAgICAgIHJlYXNvbjogJ1VzZXIgZW5hYmxlZCBNRkEnXHJcbiAgICB9KTtcclxuXHJcbiAgICByZXR1cm4gdHJ1ZTtcclxuICB9IGNhdGNoIChlcnJvcikge1xyXG4gICAgY29uc29sZS5lcnJvcignTUZBIGVuYWJsZSBlcnJvcjonLCBlcnJvcik7XHJcbiAgICByZXR1cm4gZmFsc2U7XHJcbiAgfVxyXG59XHJcblxyXG4vKipcclxuICogUmVjb3JkIGZhaWxlZCBsb2dpbiBhdHRlbXB0XHJcbiAqL1xyXG5hc3luYyBmdW5jdGlvbiByZWNvcmRGYWlsZWRBdHRlbXB0KGVtYWlsOiBzdHJpbmcsIGlwPzogc3RyaW5nLCByZWFzb24/OiBzdHJpbmcpIHtcclxuICBhd2FpdCBzdXBhYmFzZVxyXG4gICAgLmZyb20oJ2xvZ2luX2F0dGVtcHRzJylcclxuICAgIC5pbnNlcnQoe1xyXG4gICAgICBlbWFpbCxcclxuICAgICAgaXBfYWRkcmVzczogaXAsXHJcbiAgICAgIHN1Y2Nlc3M6IGZhbHNlLFxyXG4gICAgICByZWFzb24sXHJcbiAgICAgIGNyZWF0ZWRfYXQ6IG5ldyBEYXRlKCkudG9JU09TdHJpbmcoKVxyXG4gICAgfSk7XHJcblxyXG4gIGF3YWl0IGF1ZGl0TG9nKHtcclxuICAgIGFjdGlvbjogJ0xPR0lOX0ZBSUxFRCcsXHJcbiAgICBlbWFpbCxcclxuICAgIGlwLFxyXG4gICAgcmVhc29uXHJcbiAgfSk7XHJcbn1cclxuXHJcbi8qKlxyXG4gKiBBZG1pbiBsb2dvdXRcclxuICovXHJcbmV4cG9ydCBhc3luYyBmdW5jdGlvbiBhZG1pbkxvZ291dCh1c2VySWQ6IHN0cmluZywgaXA/OiBzdHJpbmcpIHtcclxuICB0cnkge1xyXG4gICAgYXdhaXQgYXVkaXRMb2coe1xyXG4gICAgICBhY3Rpb246ICdMT0dPVVQnLFxyXG4gICAgICB1c2VySWQsXHJcbiAgICAgIGlwXHJcbiAgICB9KTtcclxuICB9IGNhdGNoIChlcnJvcikge1xyXG4gICAgY29uc29sZS5lcnJvcignTG9nb3V0IGF1ZGl0IGVycm9yOicsIGVycm9yKTtcclxuICB9XHJcbn1cclxuXHJcbi8qKlxyXG4gKiBBdXRoZW50aWNhdGUgYWRtaW4gcmVxdWVzdCAoYWxpYXMgZm9yIHZlcmlmeUFkbWluVG9rZW4gZm9yIGJhY2t3YXJkIGNvbXBhdGliaWxpdHkpXHJcbiAqL1xyXG5leHBvcnQgYXN5bmMgZnVuY3Rpb24gYXV0aGVudGljYXRlQWRtaW5SZXF1ZXN0KHRva2VuOiBzdHJpbmcpOiBQcm9taXNlPEF1dGhSZXN1bHQ+IHtcclxuICByZXR1cm4gdmVyaWZ5QWRtaW5Ub2tlbih0b2tlbik7XHJcbn1cclxuIl0sIm5hbWVzIjpbImp3dCIsImJjcnlwdCIsImNyZWF0ZUNsaWVudCIsImF1ZGl0TG9nIiwic3VwYWJhc2VVcmwiLCJwcm9jZXNzIiwiZW52IiwiTkVYVF9QVUJMSUNfU1VQQUJBU0VfVVJMIiwic3VwYWJhc2VLZXkiLCJTVVBBQkFTRV9TRVJWSUNFX1JPTEVfS0VZIiwic3VwYWJhc2UiLCJ2ZXJpZnlBZG1pblRva2VuIiwidG9rZW4iLCJqd3RTZWNyZXQiLCJKV1RfU0VDUkVUIiwiZGVjb2RlZCIsInZlcmlmeSIsImRhdGEiLCJ1c2VyIiwiZXJyb3IiLCJmcm9tIiwic2VsZWN0IiwiZXEiLCJ1c2VySWQiLCJzaW5nbGUiLCJ2YWxpZCIsImlzX2FjdGl2ZSIsInVwZGF0ZSIsImxhc3RfYWN0aXZpdHkiLCJEYXRlIiwibm93IiwiaWQiLCJlbWFpbCIsInJvbGUiLCJmaXJzdE5hbWUiLCJmaXJzdF9uYW1lIiwibGFzdE5hbWUiLCJsYXN0X25hbWUiLCJpc0FjdGl2ZSIsIm1mYUVuYWJsZWQiLCJtZmFfZW5hYmxlZCIsImxhc3RBY3Rpdml0eSIsInBlcm1pc3Npb25zIiwiYWRtaW5Mb2dpbiIsInBhc3N3b3JkIiwiaXAiLCJhdHRlbXB0cyIsImd0ZSIsInRvSVNPU3RyaW5nIiwib3JkZXIiLCJhc2NlbmRpbmciLCJsZW5ndGgiLCJhY3Rpb24iLCJyZWFzb24iLCJzdWNjZXNzIiwidG9Mb3dlckNhc2UiLCJyZWNvcmRGYWlsZWRBdHRlbXB0IiwibWVzc2FnZSIsInBhc3N3b3JkVmFsaWQiLCJjb21wYXJlIiwicGFzc3dvcmRfaGFzaCIsImRlbGV0ZSIsIm1mYV9zZWNyZXQiLCJyZXF1aXJlc01GQSIsInNpZ24iLCJleHBpcmVzSW4iLCJsYXN0X2xvZ2luX2F0IiwidXNlclJvbGUiLCJjb25zb2xlIiwidmVyaWZ5TUZBQW5kTG9naW4iLCJtZmFUb2tlbiIsInNwZWFrZWFzeSIsInZlcmlmaWVkIiwidG90cCIsInNlY3JldCIsImVuY29kaW5nIiwid2luZG93IiwiZ2VuZXJhdGVNRkFTZWNyZXQiLCJnZW5lcmF0ZVNlY3JldCIsIm5hbWUiLCJpc3N1ZXIiLCJiYXNlMzIiLCJxckNvZGUiLCJvdHBhdXRoX3VybCIsImVuYWJsZU1GQSIsImluc2VydCIsImlwX2FkZHJlc3MiLCJjcmVhdGVkX2F0IiwiYWRtaW5Mb2dvdXQiLCJhdXRoZW50aWNhdGVBZG1pblJlcXVlc3QiXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(api)/./lib/auth/admin-auth.ts\n");

/***/ }),

/***/ "(api)/./lib/email/email-service.js":
/*!************************************!*\
  !*** ./lib/email/email-service.js ***!
  \************************************/
/***/ ((module, __unused_webpack_exports, __webpack_require__) => {

eval("/**\n * Email Service for Ocean Soul Sparkles Admin\n * Main service that combines SMTP functionality with email templates\n */ \nconst { sendEmail, verifyConnection, sendTestEmail } = __webpack_require__(/*! ./smtp-service */ \"(api)/./lib/email/smtp-service.js\");\nconst { bookingConfirmationTemplate, bookingReminderTemplate, bookingCancellationTemplate, paymentReceiptTemplate, staffNotificationTemplate, lowInventoryAlertTemplate } = __webpack_require__(/*! ./templates */ \"(api)/./lib/email/templates.js\");\n/**\n * Email service class\n */ class EmailService {\n    constructor(){\n        this.isConfigured = !!(process.env.SMTP_USER && process.env.SMTP_PASS);\n    }\n    /**\n   * Send booking confirmation email\n   */ async sendBookingConfirmation(booking) {\n        if (!booking.customerEmail) {\n            console.warn(\"No customer email provided for booking confirmation\");\n            return {\n                success: false,\n                error: \"No customer email\"\n            };\n        }\n        const html = bookingConfirmationTemplate(booking);\n        return await sendEmail({\n            to: booking.customerEmail,\n            subject: `Booking Confirmation - ${booking.serviceName}`,\n            html\n        });\n    }\n    /**\n   * Send booking reminder email\n   */ async sendBookingReminder(booking) {\n        if (!booking.customerEmail) {\n            console.warn(\"No customer email provided for booking reminder\");\n            return {\n                success: false,\n                error: \"No customer email\"\n            };\n        }\n        const html = bookingReminderTemplate(booking);\n        return await sendEmail({\n            to: booking.customerEmail,\n            subject: `Appointment Reminder - Tomorrow at ${booking.time}`,\n            html\n        });\n    }\n    /**\n   * Send booking cancellation email\n   */ async sendBookingCancellation(booking) {\n        if (!booking.customerEmail) {\n            console.warn(\"No customer email provided for booking cancellation\");\n            return {\n                success: false,\n                error: \"No customer email\"\n            };\n        }\n        const html = bookingCancellationTemplate(booking);\n        return await sendEmail({\n            to: booking.customerEmail,\n            subject: `Booking Cancellation - ${booking.serviceName}`,\n            html\n        });\n    }\n    /**\n   * Send payment receipt email\n   */ async sendPaymentReceipt(payment) {\n        if (!payment.customerEmail) {\n            console.warn(\"No customer email provided for payment receipt\");\n            return {\n                success: false,\n                error: \"No customer email\"\n            };\n        }\n        const html = paymentReceiptTemplate(payment);\n        return await sendEmail({\n            to: payment.customerEmail,\n            subject: `Payment Receipt - ${payment.receiptNumber}`,\n            html\n        });\n    }\n    /**\n   * Send staff notification email\n   */ async sendStaffNotification(notification) {\n        if (!notification.staffEmail) {\n            console.warn(\"No staff email provided for notification\");\n            return {\n                success: false,\n                error: \"No staff email\"\n            };\n        }\n        const html = staffNotificationTemplate(notification);\n        return await sendEmail({\n            to: notification.staffEmail,\n            subject: notification.subject || \"Staff Notification\",\n            html\n        });\n    }\n    /**\n   * Send low inventory alert to admin\n   */ async sendLowInventoryAlert(items, adminEmail) {\n        if (!adminEmail) {\n            adminEmail = process.env.ADMIN_EMAIL || \"<EMAIL>\";\n        }\n        const html = lowInventoryAlertTemplate(items);\n        return await sendEmail({\n            to: adminEmail,\n            subject: `Low Inventory Alert - ${items.length} items need attention`,\n            html\n        });\n    }\n    /**\n   * Send bulk emails (for newsletters, announcements, etc.)\n   */ async sendBulkEmail(recipients, subject, html) {\n        const results = [];\n        for (const recipient of recipients){\n            try {\n                const result = await sendEmail({\n                    to: recipient.email,\n                    subject,\n                    html: html.replace(/{{name}}/g, recipient.name || \"Valued Customer\")\n                });\n                results.push({\n                    email: recipient.email,\n                    ...result\n                });\n                // Add delay to avoid overwhelming SMTP server\n                await new Promise((resolve)=>setTimeout(resolve, 1000));\n            } catch (error) {\n                results.push({\n                    email: recipient.email,\n                    success: false,\n                    error: error.message\n                });\n            }\n        }\n        return results;\n    }\n    /**\n   * Verify email configuration\n   */ async verifyConfiguration() {\n        return await verifyConnection();\n    }\n    /**\n   * Send test email\n   */ async sendTest(to) {\n        return await sendTestEmail(to);\n    }\n    /**\n   * Get email service status\n   */ getStatus() {\n        return {\n            configured: this.isConfigured,\n            smtpHost: process.env.SMTP_HOST || \"Not configured\",\n            smtpUser: process.env.SMTP_USER ? \"Configured\" : \"Not configured\",\n            smtpPort: process.env.SMTP_PORT || \"587\"\n        };\n    }\n}\n// Create singleton instance\nconst emailService = new EmailService();\nmodule.exports = emailService;\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(api)/./lib/email/email-service.js\n");

/***/ }),

/***/ "(api)/./lib/email/smtp-service.js":
/*!***********************************!*\
  !*** ./lib/email/smtp-service.js ***!
  \***********************************/
/***/ ((module, __unused_webpack_exports, __webpack_require__) => {

eval("/**\n * SMTP Email Service for Ocean Soul Sparkles Admin\n * Handles email sending functionality using Nodemailer\n */ \nconst nodemailer = __webpack_require__(/*! nodemailer */ \"nodemailer\");\n/**\n * Create SMTP transporter based on environment configuration\n */ function createTransporter() {\n    const config = {\n        host: process.env.SMTP_HOST || \"smtp.gmail.com\",\n        port: parseInt(process.env.SMTP_PORT || \"587\"),\n        secure: process.env.SMTP_SECURE === \"true\",\n        auth: {\n            user: process.env.SMTP_USER,\n            pass: process.env.SMTP_PASS\n        }\n    };\n    // Fallback to console logging if SMTP not configured\n    if (!process.env.SMTP_USER || !process.env.SMTP_PASS) {\n        console.warn(\"⚠️  SMTP credentials not configured. Emails will be logged to console.\");\n        return null;\n    }\n    try {\n        return nodemailer.createTransporter(config);\n    } catch (error) {\n        console.error(\"Failed to create SMTP transporter:\", error);\n        return null;\n    }\n}\n/**\n * Send email using SMTP or fallback to console logging\n */ async function sendEmail({ to, subject, html, text, from }) {\n    const transporter = createTransporter();\n    const emailData = {\n        from: from || process.env.SMTP_FROM || \"<EMAIL>\",\n        to,\n        subject,\n        html,\n        text: text || html?.replace(/<[^>]*>/g, \"\") // Strip HTML for text version\n    };\n    // If no transporter (SMTP not configured), log to console\n    if (!transporter) {\n        console.log(\"\\n\\uD83D\\uDCE7 EMAIL WOULD BE SENT:\");\n        console.log(\"To:\", to);\n        console.log(\"Subject:\", subject);\n        console.log(\"Content:\", text || html);\n        console.log(\"---\\n\");\n        return {\n            success: true,\n            messageId: \"console-log-\" + Date.now()\n        };\n    }\n    try {\n        const info = await transporter.sendMail(emailData);\n        console.log(\"✅ Email sent successfully:\", info.messageId);\n        return {\n            success: true,\n            messageId: info.messageId\n        };\n    } catch (error) {\n        console.error(\"❌ Failed to send email:\", error);\n        return {\n            success: false,\n            error: error.message\n        };\n    }\n}\n/**\n * Verify SMTP connection\n */ async function verifyConnection() {\n    const transporter = createTransporter();\n    if (!transporter) {\n        return {\n            success: false,\n            error: \"SMTP not configured\"\n        };\n    }\n    try {\n        await transporter.verify();\n        return {\n            success: true,\n            message: \"SMTP connection verified\"\n        };\n    } catch (error) {\n        return {\n            success: false,\n            error: error.message\n        };\n    }\n}\n/**\n * Send test email\n */ async function sendTestEmail(to) {\n    const html = `\n    <div style=\"font-family: Arial, sans-serif; max-width: 600px; margin: 0 auto;\">\n      <h2 style=\"color: #1e40af;\">Ocean Soul Sparkles Admin</h2>\n      <p>This is a test email to verify your email configuration is working correctly.</p>\n      <p>If you received this email, your SMTP settings are properly configured!</p>\n      <hr style=\"margin: 20px 0; border: none; border-top: 1px solid #e5e7eb;\">\n      <p style=\"color: #6b7280; font-size: 14px;\">\n        Sent from Ocean Soul Sparkles Admin Dashboard<br>\n        ${new Date().toLocaleString()}\n      </p>\n    </div>\n  `;\n    return await sendEmail({\n        to,\n        subject: \"Ocean Soul Sparkles - Email Test\",\n        html\n    });\n}\nmodule.exports = {\n    sendEmail,\n    verifyConnection,\n    sendTestEmail,\n    createTransporter\n};\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(api)/./lib/email/smtp-service.js\n");

/***/ }),

/***/ "(api)/./lib/email/templates.js":
/*!********************************!*\
  !*** ./lib/email/templates.js ***!
  \********************************/
/***/ ((module) => {

eval("/**\n * Email Templates for Ocean Soul Sparkles Admin\n * Provides HTML email templates for various notification types\n */ /**\n * Base email template wrapper\n */ \nfunction baseTemplate(content, title = \"Ocean Soul Sparkles\") {\n    return `\n<!DOCTYPE html>\n<html lang=\"en\">\n<head>\n  <meta charset=\"UTF-8\">\n  <meta name=\"viewport\" content=\"width=device-width, initial-scale=1.0\">\n  <title>${title}</title>\n  <style>\n    body { font-family: Arial, sans-serif; line-height: 1.6; color: #333; margin: 0; padding: 0; }\n    .container { max-width: 600px; margin: 0 auto; padding: 20px; }\n    .header { background: linear-gradient(135deg, #667eea 0%, #764ba2 100%); color: white; padding: 30px; text-align: center; border-radius: 8px 8px 0 0; }\n    .content { background: white; padding: 30px; border: 1px solid #e5e7eb; }\n    .footer { background: #f9fafb; padding: 20px; text-align: center; border-radius: 0 0 8px 8px; font-size: 14px; color: #6b7280; }\n    .button { display: inline-block; background: #1e40af; color: white; padding: 12px 24px; text-decoration: none; border-radius: 6px; margin: 10px 0; }\n    .alert { padding: 15px; border-radius: 6px; margin: 15px 0; }\n    .alert-info { background: #dbeafe; border-left: 4px solid #3b82f6; }\n    .alert-success { background: #d1fae5; border-left: 4px solid #10b981; }\n    .alert-warning { background: #fef3c7; border-left: 4px solid #f59e0b; }\n    .booking-details { background: #f8fafc; padding: 20px; border-radius: 6px; margin: 15px 0; }\n  </style>\n</head>\n<body>\n  <div class=\"container\">\n    <div class=\"header\">\n      <h1>✨ Ocean Soul Sparkles</h1>\n      <p>Face Painting • Hair Braiding • Glitter Art</p>\n    </div>\n    <div class=\"content\">\n      ${content}\n    </div>\n    <div class=\"footer\">\n      <p>Ocean Soul Sparkles Admin Dashboard</p>\n      <p><EMAIL> | +61 XXX XXX XXX</p>\n      <p><small>This email was sent automatically. Please do not reply to this email.</small></p>\n    </div>\n  </div>\n</body>\n</html>`;\n}\n/**\n * Booking confirmation email template\n */ function bookingConfirmationTemplate(booking) {\n    const content = `\n    <h2>Booking Confirmation</h2>\n    <p>Dear ${booking.customerName},</p>\n    <p>Your booking has been confirmed! Here are the details:</p>\n    \n    <div class=\"booking-details\">\n      <h3>Booking Details</h3>\n      <p><strong>Service:</strong> ${booking.serviceName}</p>\n      <p><strong>Artist:</strong> ${booking.artistName}</p>\n      <p><strong>Date:</strong> ${booking.date}</p>\n      <p><strong>Time:</strong> ${booking.time}</p>\n      <p><strong>Duration:</strong> ${booking.duration} minutes</p>\n      <p><strong>Location:</strong> ${booking.location || \"Studio\"}</p>\n      <p><strong>Total Amount:</strong> $${booking.totalAmount}</p>\n    </div>\n\n    <div class=\"alert alert-info\">\n      <p><strong>Important:</strong> Please arrive 10 minutes before your appointment time.</p>\n    </div>\n\n    <p>If you need to reschedule or cancel, please contact us at least 24 hours in advance.</p>\n    <p>We look forward to creating something magical for you!</p>\n  `;\n    return baseTemplate(content, \"Booking Confirmation - Ocean Soul Sparkles\");\n}\n/**\n * Booking reminder email template\n */ function bookingReminderTemplate(booking) {\n    const content = `\n    <h2>Booking Reminder</h2>\n    <p>Dear ${booking.customerName},</p>\n    <p>This is a friendly reminder about your upcoming appointment:</p>\n    \n    <div class=\"booking-details\">\n      <h3>Tomorrow's Appointment</h3>\n      <p><strong>Service:</strong> ${booking.serviceName}</p>\n      <p><strong>Artist:</strong> ${booking.artistName}</p>\n      <p><strong>Date:</strong> ${booking.date}</p>\n      <p><strong>Time:</strong> ${booking.time}</p>\n      <p><strong>Location:</strong> ${booking.location || \"Studio\"}</p>\n    </div>\n\n    <div class=\"alert alert-warning\">\n      <p><strong>Reminder:</strong> Please arrive 10 minutes early and bring any reference images if you have them.</p>\n    </div>\n\n    <p>Can't wait to see you tomorrow!</p>\n  `;\n    return baseTemplate(content, \"Appointment Reminder - Ocean Soul Sparkles\");\n}\n/**\n * Booking cancellation email template\n */ function bookingCancellationTemplate(booking) {\n    const content = `\n    <h2>Booking Cancellation</h2>\n    <p>Dear ${booking.customerName},</p>\n    <p>We're sorry to confirm that your booking has been cancelled:</p>\n    \n    <div class=\"booking-details\">\n      <h3>Cancelled Booking</h3>\n      <p><strong>Service:</strong> ${booking.serviceName}</p>\n      <p><strong>Date:</strong> ${booking.date}</p>\n      <p><strong>Time:</strong> ${booking.time}</p>\n      <p><strong>Reason:</strong> ${booking.cancellationReason || \"Not specified\"}</p>\n    </div>\n\n    ${booking.refundAmount ? `\n    <div class=\"alert alert-success\">\n      <p><strong>Refund:</strong> $${booking.refundAmount} will be processed within 3-5 business days.</p>\n    </div>\n    ` : \"\"}\n\n    <p>We apologize for any inconvenience. Please feel free to book another appointment when convenient.</p>\n  `;\n    return baseTemplate(content, \"Booking Cancellation - Ocean Soul Sparkles\");\n}\n/**\n * Payment receipt email template\n */ function paymentReceiptTemplate(payment) {\n    const content = `\n    <h2>Payment Receipt</h2>\n    <p>Dear ${payment.customerName},</p>\n    <p>Thank you for your payment! Here's your receipt:</p>\n    \n    <div class=\"booking-details\">\n      <h3>Payment Details</h3>\n      <p><strong>Receipt #:</strong> ${payment.receiptNumber}</p>\n      <p><strong>Date:</strong> ${payment.date}</p>\n      <p><strong>Service:</strong> ${payment.serviceName}</p>\n      <p><strong>Amount Paid:</strong> $${payment.amount}</p>\n      <p><strong>Payment Method:</strong> ${payment.method}</p>\n      <p><strong>Transaction ID:</strong> ${payment.transactionId}</p>\n    </div>\n\n    <div class=\"alert alert-success\">\n      <p>Payment processed successfully!</p>\n    </div>\n\n    <p>Keep this receipt for your records.</p>\n  `;\n    return baseTemplate(content, \"Payment Receipt - Ocean Soul Sparkles\");\n}\n/**\n * Staff notification email template\n */ function staffNotificationTemplate(notification) {\n    const content = `\n    <h2>Staff Notification</h2>\n    <p>Dear ${notification.staffName},</p>\n    <p>${notification.message}</p>\n    \n    ${notification.details ? `\n    <div class=\"booking-details\">\n      <h3>Details</h3>\n      ${notification.details}\n    </div>\n    ` : \"\"}\n\n    ${notification.actionRequired ? `\n    <div class=\"alert alert-warning\">\n      <p><strong>Action Required:</strong> ${notification.actionRequired}</p>\n    </div>\n    ` : \"\"}\n\n    <p>Please check the admin dashboard for more information.</p>\n    <a href=\"${\"http://localhost:3002\"}/admin\" class=\"button\">Open Admin Dashboard</a>\n  `;\n    return baseTemplate(content, \"Staff Notification - Ocean Soul Sparkles\");\n}\n/**\n * Low inventory alert email template\n */ function lowInventoryAlertTemplate(items) {\n    const itemsList = items.map((item)=>`<li><strong>${item.name}</strong> - ${item.currentStock} remaining (minimum: ${item.minStock})</li>`).join(\"\");\n    const content = `\n    <h2>Low Inventory Alert</h2>\n    <p>The following items are running low and need to be restocked:</p>\n    \n    <div class=\"alert alert-warning\">\n      <h3>Items Requiring Attention</h3>\n      <ul>\n        ${itemsList}\n      </ul>\n    </div>\n\n    <p>Please review and reorder these items to avoid stockouts.</p>\n    <a href=\"${\"http://localhost:3002\"}/admin/inventory\" class=\"button\">Manage Inventory</a>\n  `;\n    return baseTemplate(content, \"Low Inventory Alert - Ocean Soul Sparkles\");\n}\nmodule.exports = {\n    baseTemplate,\n    bookingConfirmationTemplate,\n    bookingReminderTemplate,\n    bookingCancellationTemplate,\n    paymentReceiptTemplate,\n    staffNotificationTemplate,\n    lowInventoryAlertTemplate\n};\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(api)/./lib/email/templates.js\n");

/***/ }),

/***/ "(api)/./lib/security/audit-logging.ts":
/*!***************************************!*\
  !*** ./lib/security/audit-logging.ts ***!
  \***************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   AuditActions: () => (/* binding */ AuditActions),\n/* harmony export */   auditLog: () => (/* binding */ auditLog),\n/* harmony export */   exportAuditLogs: () => (/* binding */ exportAuditLogs),\n/* harmony export */   getAuditLogs: () => (/* binding */ getAuditLogs),\n/* harmony export */   logCriticalEvent: () => (/* binding */ logCriticalEvent),\n/* harmony export */   logDataChange: () => (/* binding */ logDataChange),\n/* harmony export */   logSecurityEvent: () => (/* binding */ logSecurityEvent),\n/* harmony export */   logUserAction: () => (/* binding */ logUserAction)\n/* harmony export */ });\n/* harmony import */ var _supabase_supabase_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! @supabase/supabase-js */ \"@supabase/supabase-js\");\n/* harmony import */ var _supabase_supabase_js__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(_supabase_supabase_js__WEBPACK_IMPORTED_MODULE_0__);\n\n// Handle missing environment variables gracefully\nconst supabaseUrl = \"https://ndlgbcsbidyhxbpqzgqp.supabase.co\" || 0;\nconst supabaseKey = process.env.SUPABASE_SERVICE_ROLE_KEY || \"eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.placeholder\";\nconst supabase = (0,_supabase_supabase_js__WEBPACK_IMPORTED_MODULE_0__.createClient)(supabaseUrl, supabaseKey);\n/**\r\n * Log audit event to database and console\r\n */ async function auditLog(entry) {\n    try {\n        const logEntry = {\n            action: entry.action,\n            user_id: entry.userId,\n            user_role: entry.userRole,\n            email: entry.email,\n            ip_address: entry.ip,\n            path: entry.path,\n            resource: entry.resource,\n            resource_id: entry.resourceId,\n            old_values: entry.oldValues,\n            new_values: entry.newValues,\n            reason: entry.reason,\n            error: entry.error,\n            metadata: entry.metadata,\n            severity: entry.severity || \"medium\",\n            created_at: new Date().toISOString()\n        };\n        // Log to database\n        const { error } = await supabase.from(\"audit_logs\").insert(logEntry);\n        if (error) {\n            console.error(\"Failed to write audit log to database:\", error);\n        }\n        // Log to console for immediate visibility\n        const logLevel = getLogLevel(entry.severity || \"medium\");\n        const logMessage = formatLogMessage(entry);\n        console[logLevel](logMessage);\n        // For critical events, also send alerts\n        if (entry.severity === \"critical\") {\n            await sendCriticalAlert(entry);\n        }\n    } catch (error) {\n        console.error(\"Audit logging failed:\", error);\n        // Fallback to console logging\n        console.error(\"AUDIT_LOG_FAILURE:\", JSON.stringify(entry, null, 2));\n    }\n}\n/**\r\n * Get appropriate console log level based on severity\r\n */ function getLogLevel(severity) {\n    switch(severity){\n        case \"low\":\n            return \"log\";\n        case \"medium\":\n            return \"log\";\n        case \"high\":\n            return \"warn\";\n        case \"critical\":\n            return \"error\";\n        default:\n            return \"log\";\n    }\n}\n/**\r\n * Format audit log message for console output\r\n */ function formatLogMessage(entry) {\n    const timestamp = new Date().toISOString();\n    const user = entry.userId ? `[User: ${entry.userId}]` : \"\";\n    const ip = entry.ip ? `[IP: ${entry.ip}]` : \"\";\n    const path = entry.path ? `[Path: ${entry.path}]` : \"\";\n    return `[AUDIT] ${timestamp} ${entry.action} ${user} ${ip} ${path} ${entry.reason || \"\"}`.trim();\n}\n/**\r\n * Send critical alert for high-severity events\r\n */ async function sendCriticalAlert(entry) {\n    try {\n        // In production, this would send alerts via:\n        // - Email to admin team\n        // - Slack/Discord webhook\n        // - SMS for critical security events\n        // - Push notifications\n        console.error(\"\\uD83D\\uDEA8 CRITICAL SECURITY EVENT:\", {\n            action: entry.action,\n            userId: entry.userId,\n            ip: entry.ip,\n            reason: entry.reason,\n            timestamp: new Date().toISOString()\n        });\n        // Example: Send email alert (implement based on your email service)\n        if (process.env.ENABLE_CRITICAL_ALERTS === \"true\") {\n            await sendEmailAlert(entry);\n        }\n    } catch (error) {\n        console.error(\"Failed to send critical alert:\", error);\n    }\n}\n/**\r\n * Send email alert for critical events\r\n */ async function sendEmailAlert(entry) {\n    // Implementation would depend on your email service\n    // This is a placeholder for the actual implementation\n    console.log(\"Email alert would be sent for:\", entry.action);\n}\n/**\r\n * Audit log helper functions for common actions\r\n */ const AuditActions = {\n    // Authentication events\n    LOGIN_SUCCESS: \"LOGIN_SUCCESS\",\n    LOGIN_FAILED: \"LOGIN_FAILED\",\n    LOGIN_BLOCKED: \"LOGIN_BLOCKED\",\n    LOGOUT: \"LOGOUT\",\n    MFA_ENABLED: \"MFA_ENABLED\",\n    MFA_DISABLED: \"MFA_DISABLED\",\n    MFA_FAILED: \"MFA_FAILED\",\n    PASSWORD_CHANGED: \"PASSWORD_CHANGED\",\n    PASSWORD_RESET: \"PASSWORD_RESET\",\n    // Access control events\n    ACCESS_GRANTED: \"ACCESS_GRANTED\",\n    ACCESS_DENIED: \"ACCESS_DENIED\",\n    UNAUTHORIZED_ACCESS: \"UNAUTHORIZED_ACCESS\",\n    INSUFFICIENT_PERMISSIONS: \"INSUFFICIENT_PERMISSIONS\",\n    SESSION_TIMEOUT: \"SESSION_TIMEOUT\",\n    // Data modification events\n    RECORD_CREATED: \"RECORD_CREATED\",\n    RECORD_UPDATED: \"RECORD_UPDATED\",\n    RECORD_DELETED: \"RECORD_DELETED\",\n    BULK_UPDATE: \"BULK_UPDATE\",\n    BULK_DELETE: \"BULK_DELETE\",\n    // Admin actions\n    USER_CREATED: \"USER_CREATED\",\n    USER_UPDATED: \"USER_UPDATED\",\n    USER_DEACTIVATED: \"USER_DEACTIVATED\",\n    ROLE_CHANGED: \"ROLE_CHANGED\",\n    PERMISSIONS_CHANGED: \"PERMISSIONS_CHANGED\",\n    // System events\n    SYSTEM_ERROR: \"SYSTEM_ERROR\",\n    CONFIGURATION_CHANGED: \"CONFIGURATION_CHANGED\",\n    BACKUP_CREATED: \"BACKUP_CREATED\",\n    BACKUP_RESTORED: \"BACKUP_RESTORED\",\n    // Security events\n    IP_BLOCKED: \"IP_BLOCKED\",\n    RATE_LIMITED: \"RATE_LIMITED\",\n    SUSPICIOUS_ACTIVITY: \"SUSPICIOUS_ACTIVITY\",\n    SECURITY_SCAN: \"SECURITY_SCAN\",\n    VULNERABILITY_DETECTED: \"VULNERABILITY_DETECTED\",\n    // Business events\n    BOOKING_CREATED: \"BOOKING_CREATED\",\n    BOOKING_MODIFIED: \"BOOKING_MODIFIED\",\n    BOOKING_CANCELLED: \"BOOKING_CANCELLED\",\n    PAYMENT_PROCESSED: \"PAYMENT_PROCESSED\",\n    REFUND_ISSUED: \"REFUND_ISSUED\"\n};\n/**\r\n * Helper function to log user actions\r\n */ async function logUserAction(action, userId, userRole, details = {}) {\n    await auditLog({\n        action,\n        userId,\n        userRole,\n        severity: \"medium\",\n        ...details\n    });\n}\n/**\r\n * Helper function to log security events\r\n */ async function logSecurityEvent(action, details = {}) {\n    await auditLog({\n        action,\n        severity: \"high\",\n        ...details\n    });\n}\n/**\r\n * Helper function to log critical security events\r\n */ async function logCriticalEvent(action, details = {}) {\n    await auditLog({\n        action,\n        severity: \"critical\",\n        ...details\n    });\n}\n/**\r\n * Helper function to log data changes\r\n */ async function logDataChange(action, userId, resource, resourceId, oldValues, newValues) {\n    await auditLog({\n        action,\n        userId,\n        resource,\n        resourceId,\n        oldValues,\n        newValues,\n        severity: \"medium\"\n    });\n}\n/**\r\n * Get audit logs with filtering and pagination\r\n */ async function getAuditLogs(filters) {\n    try {\n        let query = supabase.from(\"audit_logs\").select(\"*\").order(\"created_at\", {\n            ascending: false\n        });\n        if (filters.userId) {\n            query = query.eq(\"user_id\", filters.userId);\n        }\n        if (filters.action) {\n            query = query.eq(\"action\", filters.action);\n        }\n        if (filters.severity) {\n            query = query.eq(\"severity\", filters.severity);\n        }\n        if (filters.startDate) {\n            query = query.gte(\"created_at\", filters.startDate);\n        }\n        if (filters.endDate) {\n            query = query.lte(\"created_at\", filters.endDate);\n        }\n        if (filters.limit) {\n            query = query.limit(filters.limit);\n        }\n        if (filters.offset) {\n            query = query.range(filters.offset, filters.offset + (filters.limit || 50) - 1);\n        }\n        const { data, error } = await query;\n        if (error) {\n            throw error;\n        }\n        return {\n            data,\n            error: null\n        };\n    } catch (error) {\n        console.error(\"Error fetching audit logs:\", error);\n        return {\n            data: null,\n            error\n        };\n    }\n}\n/**\r\n * Export audit logs for compliance\r\n */ async function exportAuditLogs(startDate, endDate, format = \"json\") {\n    try {\n        const { data, error } = await supabase.from(\"audit_logs\").select(\"*\").gte(\"created_at\", startDate).lte(\"created_at\", endDate).order(\"created_at\", {\n            ascending: true\n        });\n        if (error) {\n            throw error;\n        }\n        if (format === \"csv\") {\n            return convertToCSV(data);\n        }\n        return data;\n    } catch (error) {\n        console.error(\"Error exporting audit logs:\", error);\n        throw error;\n    }\n}\n/**\r\n * Convert audit logs to CSV format\r\n */ function convertToCSV(data) {\n    if (!data || data.length === 0) {\n        return \"\";\n    }\n    const headers = Object.keys(data[0]);\n    const csvContent = [\n        headers.join(\",\"),\n        ...data.map((row)=>headers.map((header)=>{\n                const value = row[header];\n                if (typeof value === \"object\" && value !== null) {\n                    return `\"${JSON.stringify(value).replace(/\"/g, '\"\"')}\"`;\n                }\n                return `\"${String(value || \"\").replace(/\"/g, '\"\"')}\"`;\n            }).join(\",\"))\n    ].join(\"\\n\");\n    return csvContent;\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(api)/./lib/security/audit-logging.ts\n");

/***/ }),

/***/ "(api)/./pages/api/admin/notifications/email.js":
/*!************************************************!*\
  !*** ./pages/api/admin/notifications/email.js ***!
  \************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ handler)\n/* harmony export */ });\n/* harmony import */ var _lib_auth_admin_auth__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! @/lib/auth/admin-auth */ \"(api)/./lib/auth/admin-auth.ts\");\n/* harmony import */ var _lib_email_email_service__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @/lib/email/email-service */ \"(api)/./lib/email/email-service.js\");\n/* harmony import */ var _lib_email_email_service__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(_lib_email_email_service__WEBPACK_IMPORTED_MODULE_1__);\n\n\n/**\n * API endpoint for email notifications\n * Handles sending various types of email notifications\n *\n * @param {Object} req - HTTP request object\n * @param {Object} res - HTTP response object\n */ async function handler(req, res) {\n    const requestId = Math.random().toString(36).substring(2, 8);\n    console.log(`[${requestId}] Email notifications API called - ${req.method}`);\n    try {\n        // Authenticate admin request\n        const { user, error: authError } = await (0,_lib_auth_admin_auth__WEBPACK_IMPORTED_MODULE_0__.authenticateAdminRequest)(req);\n        if (authError || !user) {\n            return res.status(401).json({\n                error: \"Authentication required\",\n                message: authError?.message || \"Authentication failed\",\n                requestId\n            });\n        }\n        if (req.method === \"POST\") {\n            const { type, data } = req.body;\n            if (!type) {\n                return res.status(400).json({\n                    error: \"Email type is required\",\n                    requestId\n                });\n            }\n            let result;\n            switch(type){\n                case \"booking_confirmation\":\n                    result = await _lib_email_email_service__WEBPACK_IMPORTED_MODULE_1___default().sendBookingConfirmation(data);\n                    break;\n                case \"booking_reminder\":\n                    result = await _lib_email_email_service__WEBPACK_IMPORTED_MODULE_1___default().sendBookingReminder(data);\n                    break;\n                case \"booking_cancellation\":\n                    result = await _lib_email_email_service__WEBPACK_IMPORTED_MODULE_1___default().sendBookingCancellation(data);\n                    break;\n                case \"payment_receipt\":\n                    result = await _lib_email_email_service__WEBPACK_IMPORTED_MODULE_1___default().sendPaymentReceipt(data);\n                    break;\n                case \"staff_notification\":\n                    result = await _lib_email_email_service__WEBPACK_IMPORTED_MODULE_1___default().sendStaffNotification(data);\n                    break;\n                case \"low_inventory_alert\":\n                    result = await _lib_email_email_service__WEBPACK_IMPORTED_MODULE_1___default().sendLowInventoryAlert(data.items, data.adminEmail);\n                    break;\n                case \"test_email\":\n                    if (!data.to) {\n                        return res.status(400).json({\n                            error: \"Recipient email is required for test email\",\n                            requestId\n                        });\n                    }\n                    result = await _lib_email_email_service__WEBPACK_IMPORTED_MODULE_1___default().sendTest(data.to);\n                    break;\n                default:\n                    return res.status(400).json({\n                        error: `Unknown email type: ${type}`,\n                        requestId\n                    });\n            }\n            if (result.success) {\n                console.log(`[${requestId}] Email sent successfully:`, result.messageId);\n                return res.status(200).json({\n                    success: true,\n                    messageId: result.messageId,\n                    message: \"Email sent successfully\",\n                    requestId\n                });\n            } else {\n                console.error(`[${requestId}] Email sending failed:`, result.error);\n                return res.status(500).json({\n                    error: \"Failed to send email\",\n                    message: result.error,\n                    requestId\n                });\n            }\n        }\n        if (req.method === \"GET\") {\n            // Get email service status\n            const status = _lib_email_email_service__WEBPACK_IMPORTED_MODULE_1___default().getStatus();\n            return res.status(200).json({\n                status,\n                requestId\n            });\n        }\n        return res.status(405).json({\n            error: \"Method not allowed\",\n            requestId\n        });\n    } catch (error) {\n        console.error(`[${requestId}] Email API error:`, error);\n        return res.status(500).json({\n            error: \"Internal server error\",\n            message: error.message,\n            requestId\n        });\n    }\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(api)/./pages/api/admin/notifications/email.js\n");

/***/ })

};
;

// load runtime
var __webpack_require__ = require("../../../../webpack-api-runtime.js");
__webpack_require__.C(exports);
var __webpack_exec__ = (moduleId) => (__webpack_require__(__webpack_require__.s = moduleId))
var __webpack_exports__ = __webpack_require__.X(0, ["vendor-chunks/next"], () => (__webpack_exec__("(api)/./node_modules/next/dist/build/webpack/loaders/next-route-loader/index.js?kind=PAGES_API&page=%2Fapi%2Fadmin%2Fnotifications%2Femail&preferredRegion=&absolutePagePath=.%2Fpages%5Capi%5Cadmin%5Cnotifications%5Cemail.js&middlewareConfigBase64=e30%3D!")));
module.exports = __webpack_exports__;

})();