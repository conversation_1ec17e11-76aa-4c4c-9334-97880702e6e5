"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
(() => {
var exports = {};
exports.id = "pages/api/admin/receipts";
exports.ids = ["pages/api/admin/receipts"];
exports.modules = {

/***/ "@supabase/supabase-js":
/*!****************************************!*\
  !*** external "@supabase/supabase-js" ***!
  \****************************************/
/***/ ((module) => {

module.exports = require("@supabase/supabase-js");

/***/ }),

/***/ "bcryptjs":
/*!***************************!*\
  !*** external "bcryptjs" ***!
  \***************************/
/***/ ((module) => {

module.exports = require("bcryptjs");

/***/ }),

/***/ "jsonwebtoken":
/*!*******************************!*\
  !*** external "jsonwebtoken" ***!
  \*******************************/
/***/ ((module) => {

module.exports = require("jsonwebtoken");

/***/ }),

/***/ "next/dist/compiled/next-server/pages-api.runtime.dev.js":
/*!**************************************************************************!*\
  !*** external "next/dist/compiled/next-server/pages-api.runtime.dev.js" ***!
  \**************************************************************************/
/***/ ((module) => {

module.exports = require("next/dist/compiled/next-server/pages-api.runtime.dev.js");

/***/ }),

/***/ "speakeasy":
/*!****************************!*\
  !*** external "speakeasy" ***!
  \****************************/
/***/ ((module) => {

module.exports = require("speakeasy");

/***/ }),

/***/ "(api)/./node_modules/next/dist/build/webpack/loaders/next-route-loader/index.js?kind=PAGES_API&page=%2Fapi%2Fadmin%2Freceipts&preferredRegion=&absolutePagePath=.%2Fpages%5Capi%5Cadmin%5Creceipts.js&middlewareConfigBase64=e30%3D!":
/*!**************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-route-loader/index.js?kind=PAGES_API&page=%2Fapi%2Fadmin%2Freceipts&preferredRegion=&absolutePagePath=.%2Fpages%5Capi%5Cadmin%5Creceipts.js&middlewareConfigBase64=e30%3D! ***!
  \**************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   config: () => (/* binding */ config),\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__),\n/* harmony export */   routeModule: () => (/* binding */ routeModule)\n/* harmony export */ });\n/* harmony import */ var next_dist_server_future_route_modules_pages_api_module_compiled__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/server/future/route-modules/pages-api/module.compiled */ \"(api)/./node_modules/next/dist/server/future/route-modules/pages-api/module.compiled.js\");\n/* harmony import */ var next_dist_server_future_route_modules_pages_api_module_compiled__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_future_route_modules_pages_api_module_compiled__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_dist_server_future_route_kind__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next/dist/server/future/route-kind */ \"(api)/./node_modules/next/dist/server/future/route-kind.js\");\n/* harmony import */ var next_dist_build_templates_helpers__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/dist/build/templates/helpers */ \"(api)/./node_modules/next/dist/build/templates/helpers.js\");\n/* harmony import */ var _pages_api_admin_receipts_js__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./pages\\api\\admin\\receipts.js */ \"(api)/./pages/api/admin/receipts.js\");\n\n\n\n// Import the userland code.\n\n// Re-export the handler (should be the default export).\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = ((0,next_dist_build_templates_helpers__WEBPACK_IMPORTED_MODULE_2__.hoist)(_pages_api_admin_receipts_js__WEBPACK_IMPORTED_MODULE_3__, \"default\"));\n// Re-export config.\nconst config = (0,next_dist_build_templates_helpers__WEBPACK_IMPORTED_MODULE_2__.hoist)(_pages_api_admin_receipts_js__WEBPACK_IMPORTED_MODULE_3__, \"config\");\n// Create and export the route module that will be consumed.\nconst routeModule = new next_dist_server_future_route_modules_pages_api_module_compiled__WEBPACK_IMPORTED_MODULE_0__.PagesAPIRouteModule({\n    definition: {\n        kind: next_dist_server_future_route_kind__WEBPACK_IMPORTED_MODULE_1__.RouteKind.PAGES_API,\n        page: \"/api/admin/receipts\",\n        pathname: \"/api/admin/receipts\",\n        // The following aren't used in production.\n        bundlePath: \"\",\n        filename: \"\"\n    },\n    userland: _pages_api_admin_receipts_js__WEBPACK_IMPORTED_MODULE_3__\n});\n\n//# sourceMappingURL=pages-api.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKGFwaSkvLi9ub2RlX21vZHVsZXMvbmV4dC9kaXN0L2J1aWxkL3dlYnBhY2svbG9hZGVycy9uZXh0LXJvdXRlLWxvYWRlci9pbmRleC5qcz9raW5kPVBBR0VTX0FQSSZwYWdlPSUyRmFwaSUyRmFkbWluJTJGcmVjZWlwdHMmcHJlZmVycmVkUmVnaW9uPSZhYnNvbHV0ZVBhZ2VQYXRoPS4lMkZwYWdlcyU1Q2FwaSU1Q2FkbWluJTVDcmVjZWlwdHMuanMmbWlkZGxld2FyZUNvbmZpZ0Jhc2U2ND1lMzAlM0QhIiwibWFwcGluZ3MiOiI7Ozs7Ozs7Ozs7O0FBQXNHO0FBQ3ZDO0FBQ0w7QUFDMUQ7QUFDNkQ7QUFDN0Q7QUFDQSxpRUFBZSx3RUFBSyxDQUFDLHlEQUFRLFlBQVksRUFBQztBQUMxQztBQUNPLGVBQWUsd0VBQUssQ0FBQyx5REFBUTtBQUNwQztBQUNPLHdCQUF3QixnSEFBbUI7QUFDbEQ7QUFDQSxjQUFjLHlFQUFTO0FBQ3ZCO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSxLQUFLO0FBQ0wsWUFBWTtBQUNaLENBQUM7O0FBRUQiLCJzb3VyY2VzIjpbIndlYnBhY2s6Ly9vY2VhbnNvdWxzcGFya2xlcy1hZG1pbi8/YTIxZSJdLCJzb3VyY2VzQ29udGVudCI6WyJpbXBvcnQgeyBQYWdlc0FQSVJvdXRlTW9kdWxlIH0gZnJvbSBcIm5leHQvZGlzdC9zZXJ2ZXIvZnV0dXJlL3JvdXRlLW1vZHVsZXMvcGFnZXMtYXBpL21vZHVsZS5jb21waWxlZFwiO1xuaW1wb3J0IHsgUm91dGVLaW5kIH0gZnJvbSBcIm5leHQvZGlzdC9zZXJ2ZXIvZnV0dXJlL3JvdXRlLWtpbmRcIjtcbmltcG9ydCB7IGhvaXN0IH0gZnJvbSBcIm5leHQvZGlzdC9idWlsZC90ZW1wbGF0ZXMvaGVscGVyc1wiO1xuLy8gSW1wb3J0IHRoZSB1c2VybGFuZCBjb2RlLlxuaW1wb3J0ICogYXMgdXNlcmxhbmQgZnJvbSBcIi4vcGFnZXNcXFxcYXBpXFxcXGFkbWluXFxcXHJlY2VpcHRzLmpzXCI7XG4vLyBSZS1leHBvcnQgdGhlIGhhbmRsZXIgKHNob3VsZCBiZSB0aGUgZGVmYXVsdCBleHBvcnQpLlxuZXhwb3J0IGRlZmF1bHQgaG9pc3QodXNlcmxhbmQsIFwiZGVmYXVsdFwiKTtcbi8vIFJlLWV4cG9ydCBjb25maWcuXG5leHBvcnQgY29uc3QgY29uZmlnID0gaG9pc3QodXNlcmxhbmQsIFwiY29uZmlnXCIpO1xuLy8gQ3JlYXRlIGFuZCBleHBvcnQgdGhlIHJvdXRlIG1vZHVsZSB0aGF0IHdpbGwgYmUgY29uc3VtZWQuXG5leHBvcnQgY29uc3Qgcm91dGVNb2R1bGUgPSBuZXcgUGFnZXNBUElSb3V0ZU1vZHVsZSh7XG4gICAgZGVmaW5pdGlvbjoge1xuICAgICAgICBraW5kOiBSb3V0ZUtpbmQuUEFHRVNfQVBJLFxuICAgICAgICBwYWdlOiBcIi9hcGkvYWRtaW4vcmVjZWlwdHNcIixcbiAgICAgICAgcGF0aG5hbWU6IFwiL2FwaS9hZG1pbi9yZWNlaXB0c1wiLFxuICAgICAgICAvLyBUaGUgZm9sbG93aW5nIGFyZW4ndCB1c2VkIGluIHByb2R1Y3Rpb24uXG4gICAgICAgIGJ1bmRsZVBhdGg6IFwiXCIsXG4gICAgICAgIGZpbGVuYW1lOiBcIlwiXG4gICAgfSxcbiAgICB1c2VybGFuZFxufSk7XG5cbi8vIyBzb3VyY2VNYXBwaW5nVVJMPXBhZ2VzLWFwaS5qcy5tYXAiXSwibmFtZXMiOltdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(api)/./node_modules/next/dist/build/webpack/loaders/next-route-loader/index.js?kind=PAGES_API&page=%2Fapi%2Fadmin%2Freceipts&preferredRegion=&absolutePagePath=.%2Fpages%5Capi%5Cadmin%5Creceipts.js&middlewareConfigBase64=e30%3D!\n");

/***/ }),

/***/ "(api)/./lib/auth/admin-auth.ts":
/*!********************************!*\
  !*** ./lib/auth/admin-auth.ts ***!
  \********************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   adminLogin: () => (/* binding */ adminLogin),\n/* harmony export */   adminLogout: () => (/* binding */ adminLogout),\n/* harmony export */   authenticateAdminRequest: () => (/* binding */ authenticateAdminRequest),\n/* harmony export */   enableMFA: () => (/* binding */ enableMFA),\n/* harmony export */   generateMFASecret: () => (/* binding */ generateMFASecret),\n/* harmony export */   verifyAdminToken: () => (/* binding */ verifyAdminToken),\n/* harmony export */   verifyMFAAndLogin: () => (/* binding */ verifyMFAAndLogin)\n/* harmony export */ });\n/* harmony import */ var jsonwebtoken__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! jsonwebtoken */ \"jsonwebtoken\");\n/* harmony import */ var jsonwebtoken__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(jsonwebtoken__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var bcryptjs__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! bcryptjs */ \"bcryptjs\");\n/* harmony import */ var bcryptjs__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(bcryptjs__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _supabase_supabase_js__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @supabase/supabase-js */ \"@supabase/supabase-js\");\n/* harmony import */ var _supabase_supabase_js__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(_supabase_supabase_js__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var _security_audit_logging__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ../security/audit-logging */ \"(api)/./lib/security/audit-logging.ts\");\n\n\n\n\n// Handle missing environment variables gracefully\nconst supabaseUrl = \"https://ndlgbcsbidyhxbpqzgqp.supabase.co\" || 0;\nconst supabaseKey = process.env.SUPABASE_SERVICE_ROLE_KEY || \"eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.placeholder\";\nconst supabase = (0,_supabase_supabase_js__WEBPACK_IMPORTED_MODULE_2__.createClient)(supabaseUrl, supabaseKey);\n/**\r\n * Verify admin authentication token\r\n */ async function verifyAdminToken(token) {\n    try {\n        // Handle missing JWT secret gracefully\n        const jwtSecret = process.env.JWT_SECRET || \"placeholder-secret\";\n        const decoded = jsonwebtoken__WEBPACK_IMPORTED_MODULE_0___default().verify(token, jwtSecret);\n        // Get user from database with latest info\n        const { data: user, error } = await supabase.from(\"admin_users\").select(`\r\n        id,\r\n        email,\r\n        role,\r\n        first_name,\r\n        last_name,\r\n        is_active,\r\n        mfa_enabled,\r\n        last_activity,\r\n        permissions\r\n      `).eq(\"id\", decoded.userId).eq(\"is_active\", true).single();\n        if (error || !user) {\n            return {\n                valid: false,\n                error: \"User not found or inactive\"\n            };\n        }\n        // Check if user is still active\n        if (!user.is_active) {\n            return {\n                valid: false,\n                error: \"User account is deactivated\"\n            };\n        }\n        // Update last activity\n        await supabase.from(\"admin_users\").update({\n            last_activity: Date.now()\n        }).eq(\"id\", user.id);\n        return {\n            valid: true,\n            user: {\n                id: user.id,\n                email: user.email,\n                role: user.role,\n                firstName: user.first_name,\n                lastName: user.last_name,\n                isActive: user.is_active,\n                mfaEnabled: user.mfa_enabled,\n                lastActivity: Date.now(),\n                permissions: user.permissions || []\n            }\n        };\n    } catch (error) {\n        return {\n            valid: false,\n            error: \"Invalid token\"\n        };\n    }\n}\n/**\r\n * Admin login with email and password\r\n */ async function adminLogin(email, password, ip) {\n    try {\n        // Check for rate limiting\n        const { data: attempts } = await supabase.from(\"login_attempts\").select(\"*\").eq(\"email\", email).gte(\"created_at\", new Date(Date.now() - 15 * 60 * 1000).toISOString()).order(\"created_at\", {\n            ascending: false\n        });\n        if (attempts && attempts.length >= 5) {\n            await (0,_security_audit_logging__WEBPACK_IMPORTED_MODULE_3__.auditLog)({\n                action: \"LOGIN_BLOCKED\",\n                email,\n                ip,\n                reason: \"Too many failed attempts\"\n            });\n            return {\n                success: false,\n                error: \"Account temporarily locked due to too many failed attempts\"\n            };\n        }\n        // Get user from database\n        const { data: user, error } = await supabase.from(\"admin_users\").select(`\r\n        id,\r\n        email,\r\n        password_hash,\r\n        role,\r\n        first_name,\r\n        last_name,\r\n        is_active,\r\n        mfa_enabled,\r\n        mfa_secret,\r\n        permissions\r\n      `).eq(\"email\", email.toLowerCase()).single();\n        if (error || !user) {\n            await recordFailedAttempt(email, ip, error ? `Database error: ${error.message}` : \"User not found\");\n            return {\n                success: false,\n                error: \"Invalid credentials\"\n            };\n        }\n        // Check if user is active\n        if (!user.is_active) {\n            await (0,_security_audit_logging__WEBPACK_IMPORTED_MODULE_3__.auditLog)({\n                action: \"LOGIN_DENIED\",\n                userId: user.id,\n                email,\n                ip,\n                reason: \"Account deactivated\"\n            });\n            return {\n                success: false,\n                error: \"Account is deactivated\"\n            };\n        }\n        // Verify password\n        const passwordValid = await bcryptjs__WEBPACK_IMPORTED_MODULE_1___default().compare(password, user.password_hash);\n        if (!passwordValid) {\n            await recordFailedAttempt(email, ip, \"Invalid password\");\n            return {\n                success: false,\n                error: \"Invalid credentials\"\n            };\n        }\n        // Clear failed attempts on successful password verification\n        await supabase.from(\"login_attempts\").delete().eq(\"email\", email);\n        // Check if MFA is required\n        if (user.mfa_enabled && user.mfa_secret) {\n            // Return success but indicate MFA is required\n            return {\n                success: true,\n                requiresMFA: true,\n                user: {\n                    id: user.id,\n                    email: user.email,\n                    role: user.role,\n                    firstName: user.first_name,\n                    lastName: user.last_name,\n                    isActive: user.is_active,\n                    mfaEnabled: user.mfa_enabled,\n                    lastActivity: Date.now(),\n                    permissions: user.permissions || []\n                }\n            };\n        }\n        // Generate JWT token\n        const jwtSecret = process.env.JWT_SECRET || \"placeholder-secret\";\n        const token = jsonwebtoken__WEBPACK_IMPORTED_MODULE_0___default().sign({\n            userId: user.id,\n            email: user.email,\n            role: user.role\n        }, jwtSecret, {\n            expiresIn: \"8h\"\n        });\n        // Update last login\n        await supabase.from(\"admin_users\").update({\n            last_login_at: new Date().toISOString(),\n            last_activity: Date.now()\n        }).eq(\"id\", user.id);\n        await (0,_security_audit_logging__WEBPACK_IMPORTED_MODULE_3__.auditLog)({\n            action: \"LOGIN_SUCCESS\",\n            userId: user.id,\n            userRole: user.role,\n            email,\n            ip\n        });\n        return {\n            success: true,\n            token,\n            user: {\n                id: user.id,\n                email: user.email,\n                role: user.role,\n                firstName: user.first_name,\n                lastName: user.last_name,\n                isActive: user.is_active,\n                mfaEnabled: user.mfa_enabled,\n                lastActivity: Date.now(),\n                permissions: user.permissions || []\n            }\n        };\n    } catch (error) {\n        console.error(\"Admin login error:\", error);\n        return {\n            success: false,\n            error: \"Login failed\"\n        };\n    }\n}\n/**\r\n * Verify MFA token and complete login\r\n * Note: This function requires Node.js runtime due to speakeasy dependency\r\n */ async function verifyMFAAndLogin(userId, mfaToken, ip) {\n    // Import speakeasy dynamically to avoid Edge Runtime issues\n    const speakeasy = await Promise.resolve(/*! import() */).then(__webpack_require__.t.bind(__webpack_require__, /*! speakeasy */ \"speakeasy\", 23));\n    try {\n        const { data: user, error } = await supabase.from(\"admin_users\").select(`\r\n        id,\r\n        email,\r\n        role,\r\n        first_name,\r\n        last_name,\r\n        is_active,\r\n        mfa_enabled,\r\n        mfa_secret,\r\n        permissions\r\n      `).eq(\"id\", userId).single();\n        if (error || !user || !user.mfa_secret) {\n            return {\n                success: false,\n                error: \"Invalid MFA setup\"\n            };\n        }\n        // Verify MFA token\n        const verified = speakeasy.totp.verify({\n            secret: user.mfa_secret,\n            encoding: \"base32\",\n            token: mfaToken,\n            window: 2\n        });\n        if (!verified) {\n            await (0,_security_audit_logging__WEBPACK_IMPORTED_MODULE_3__.auditLog)({\n                action: \"MFA_FAILED\",\n                userId: user.id,\n                email: user.email,\n                ip,\n                reason: \"Invalid MFA token\"\n            });\n            return {\n                success: false,\n                error: \"Invalid MFA token\"\n            };\n        }\n        // Generate JWT token\n        const jwtSecret = process.env.JWT_SECRET || \"placeholder-secret\";\n        const token = jsonwebtoken__WEBPACK_IMPORTED_MODULE_0___default().sign({\n            userId: user.id,\n            email: user.email,\n            role: user.role\n        }, jwtSecret, {\n            expiresIn: \"8h\"\n        });\n        // Update last login\n        await supabase.from(\"admin_users\").update({\n            last_login_at: new Date().toISOString(),\n            last_activity: Date.now()\n        }).eq(\"id\", user.id);\n        await (0,_security_audit_logging__WEBPACK_IMPORTED_MODULE_3__.auditLog)({\n            action: \"MFA_LOGIN_SUCCESS\",\n            userId: user.id,\n            userRole: user.role,\n            email: user.email,\n            ip\n        });\n        return {\n            success: true,\n            token,\n            user: {\n                id: user.id,\n                email: user.email,\n                role: user.role,\n                firstName: user.first_name,\n                lastName: user.last_name,\n                isActive: user.is_active,\n                mfaEnabled: user.mfa_enabled,\n                lastActivity: Date.now(),\n                permissions: user.permissions || []\n            }\n        };\n    } catch (error) {\n        console.error(\"MFA verification error:\", error);\n        return {\n            success: false,\n            error: \"MFA verification failed\"\n        };\n    }\n}\n/**\r\n * Generate MFA secret for user\r\n * Note: This function requires Node.js runtime due to speakeasy dependency\r\n */ async function generateMFASecret(userId) {\n    // Import speakeasy dynamically to avoid Edge Runtime issues\n    const speakeasy = await Promise.resolve(/*! import() */).then(__webpack_require__.t.bind(__webpack_require__, /*! speakeasy */ \"speakeasy\", 23));\n    try {\n        const { data: user } = await supabase.from(\"admin_users\").select(\"email, first_name, last_name\").eq(\"id\", userId).single();\n        if (!user) return null;\n        const secret = speakeasy.generateSecret({\n            name: `${user.first_name} ${user.last_name}`,\n            issuer: \"Ocean Soul Sparkles Admin\",\n            length: 32\n        });\n        return {\n            secret: secret.base32,\n            qrCode: secret.otpauth_url\n        };\n    } catch (error) {\n        console.error(\"MFA secret generation error:\", error);\n        return null;\n    }\n}\n/**\r\n * Enable MFA for user\r\n * Note: This function requires Node.js runtime due to speakeasy dependency\r\n */ async function enableMFA(userId, secret, token) {\n    // Import speakeasy dynamically to avoid Edge Runtime issues\n    const speakeasy = await Promise.resolve(/*! import() */).then(__webpack_require__.t.bind(__webpack_require__, /*! speakeasy */ \"speakeasy\", 23));\n    try {\n        // Verify the token first\n        const verified = speakeasy.totp.verify({\n            secret,\n            encoding: \"base32\",\n            token,\n            window: 2\n        });\n        if (!verified) return false;\n        // Save MFA secret to database\n        const { error } = await supabase.from(\"admin_users\").update({\n            mfa_secret: secret,\n            mfa_enabled: true\n        }).eq(\"id\", userId);\n        if (error) return false;\n        await (0,_security_audit_logging__WEBPACK_IMPORTED_MODULE_3__.auditLog)({\n            action: \"MFA_ENABLED\",\n            userId,\n            reason: \"User enabled MFA\"\n        });\n        return true;\n    } catch (error) {\n        console.error(\"MFA enable error:\", error);\n        return false;\n    }\n}\n/**\r\n * Record failed login attempt\r\n */ async function recordFailedAttempt(email, ip, reason) {\n    await supabase.from(\"login_attempts\").insert({\n        email,\n        ip_address: ip,\n        success: false,\n        reason,\n        created_at: new Date().toISOString()\n    });\n    await (0,_security_audit_logging__WEBPACK_IMPORTED_MODULE_3__.auditLog)({\n        action: \"LOGIN_FAILED\",\n        email,\n        ip,\n        reason\n    });\n}\n/**\r\n * Admin logout\r\n */ async function adminLogout(userId, ip) {\n    try {\n        await (0,_security_audit_logging__WEBPACK_IMPORTED_MODULE_3__.auditLog)({\n            action: \"LOGOUT\",\n            userId,\n            ip\n        });\n    } catch (error) {\n        console.error(\"Logout audit error:\", error);\n    }\n}\n/**\r\n * Authenticate admin request (alias for verifyAdminToken for backward compatibility)\r\n */ async function authenticateAdminRequest(token) {\n    return verifyAdminToken(token);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(api)/./lib/auth/admin-auth.ts\n");

/***/ }),

/***/ "(api)/./lib/security/audit-logging.ts":
/*!***************************************!*\
  !*** ./lib/security/audit-logging.ts ***!
  \***************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   AuditActions: () => (/* binding */ AuditActions),\n/* harmony export */   auditLog: () => (/* binding */ auditLog),\n/* harmony export */   exportAuditLogs: () => (/* binding */ exportAuditLogs),\n/* harmony export */   getAuditLogs: () => (/* binding */ getAuditLogs),\n/* harmony export */   logCriticalEvent: () => (/* binding */ logCriticalEvent),\n/* harmony export */   logDataChange: () => (/* binding */ logDataChange),\n/* harmony export */   logSecurityEvent: () => (/* binding */ logSecurityEvent),\n/* harmony export */   logUserAction: () => (/* binding */ logUserAction)\n/* harmony export */ });\n/* harmony import */ var _supabase_supabase_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! @supabase/supabase-js */ \"@supabase/supabase-js\");\n/* harmony import */ var _supabase_supabase_js__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(_supabase_supabase_js__WEBPACK_IMPORTED_MODULE_0__);\n\n// Handle missing environment variables gracefully\nconst supabaseUrl = \"https://ndlgbcsbidyhxbpqzgqp.supabase.co\" || 0;\nconst supabaseKey = process.env.SUPABASE_SERVICE_ROLE_KEY || \"eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.placeholder\";\nconst supabase = (0,_supabase_supabase_js__WEBPACK_IMPORTED_MODULE_0__.createClient)(supabaseUrl, supabaseKey);\n/**\r\n * Log audit event to database and console\r\n */ async function auditLog(entry) {\n    try {\n        const logEntry = {\n            action: entry.action,\n            user_id: entry.userId,\n            user_role: entry.userRole,\n            email: entry.email,\n            ip_address: entry.ip,\n            path: entry.path,\n            resource: entry.resource,\n            resource_id: entry.resourceId,\n            old_values: entry.oldValues,\n            new_values: entry.newValues,\n            reason: entry.reason,\n            error: entry.error,\n            metadata: entry.metadata,\n            severity: entry.severity || \"medium\",\n            created_at: new Date().toISOString()\n        };\n        // Log to database\n        const { error } = await supabase.from(\"audit_logs\").insert(logEntry);\n        if (error) {\n            console.error(\"Failed to write audit log to database:\", error);\n        }\n        // Log to console for immediate visibility\n        const logLevel = getLogLevel(entry.severity || \"medium\");\n        const logMessage = formatLogMessage(entry);\n        console[logLevel](logMessage);\n        // For critical events, also send alerts\n        if (entry.severity === \"critical\") {\n            await sendCriticalAlert(entry);\n        }\n    } catch (error) {\n        console.error(\"Audit logging failed:\", error);\n        // Fallback to console logging\n        console.error(\"AUDIT_LOG_FAILURE:\", JSON.stringify(entry, null, 2));\n    }\n}\n/**\r\n * Get appropriate console log level based on severity\r\n */ function getLogLevel(severity) {\n    switch(severity){\n        case \"low\":\n            return \"log\";\n        case \"medium\":\n            return \"log\";\n        case \"high\":\n            return \"warn\";\n        case \"critical\":\n            return \"error\";\n        default:\n            return \"log\";\n    }\n}\n/**\r\n * Format audit log message for console output\r\n */ function formatLogMessage(entry) {\n    const timestamp = new Date().toISOString();\n    const user = entry.userId ? `[User: ${entry.userId}]` : \"\";\n    const ip = entry.ip ? `[IP: ${entry.ip}]` : \"\";\n    const path = entry.path ? `[Path: ${entry.path}]` : \"\";\n    return `[AUDIT] ${timestamp} ${entry.action} ${user} ${ip} ${path} ${entry.reason || \"\"}`.trim();\n}\n/**\r\n * Send critical alert for high-severity events\r\n */ async function sendCriticalAlert(entry) {\n    try {\n        // In production, this would send alerts via:\n        // - Email to admin team\n        // - Slack/Discord webhook\n        // - SMS for critical security events\n        // - Push notifications\n        console.error(\"\\uD83D\\uDEA8 CRITICAL SECURITY EVENT:\", {\n            action: entry.action,\n            userId: entry.userId,\n            ip: entry.ip,\n            reason: entry.reason,\n            timestamp: new Date().toISOString()\n        });\n        // Example: Send email alert (implement based on your email service)\n        if (process.env.ENABLE_CRITICAL_ALERTS === \"true\") {\n            await sendEmailAlert(entry);\n        }\n    } catch (error) {\n        console.error(\"Failed to send critical alert:\", error);\n    }\n}\n/**\r\n * Send email alert for critical events\r\n */ async function sendEmailAlert(entry) {\n    // Implementation would depend on your email service\n    // This is a placeholder for the actual implementation\n    console.log(\"Email alert would be sent for:\", entry.action);\n}\n/**\r\n * Audit log helper functions for common actions\r\n */ const AuditActions = {\n    // Authentication events\n    LOGIN_SUCCESS: \"LOGIN_SUCCESS\",\n    LOGIN_FAILED: \"LOGIN_FAILED\",\n    LOGIN_BLOCKED: \"LOGIN_BLOCKED\",\n    LOGOUT: \"LOGOUT\",\n    MFA_ENABLED: \"MFA_ENABLED\",\n    MFA_DISABLED: \"MFA_DISABLED\",\n    MFA_FAILED: \"MFA_FAILED\",\n    PASSWORD_CHANGED: \"PASSWORD_CHANGED\",\n    PASSWORD_RESET: \"PASSWORD_RESET\",\n    // Access control events\n    ACCESS_GRANTED: \"ACCESS_GRANTED\",\n    ACCESS_DENIED: \"ACCESS_DENIED\",\n    UNAUTHORIZED_ACCESS: \"UNAUTHORIZED_ACCESS\",\n    INSUFFICIENT_PERMISSIONS: \"INSUFFICIENT_PERMISSIONS\",\n    SESSION_TIMEOUT: \"SESSION_TIMEOUT\",\n    // Data modification events\n    RECORD_CREATED: \"RECORD_CREATED\",\n    RECORD_UPDATED: \"RECORD_UPDATED\",\n    RECORD_DELETED: \"RECORD_DELETED\",\n    BULK_UPDATE: \"BULK_UPDATE\",\n    BULK_DELETE: \"BULK_DELETE\",\n    // Admin actions\n    USER_CREATED: \"USER_CREATED\",\n    USER_UPDATED: \"USER_UPDATED\",\n    USER_DEACTIVATED: \"USER_DEACTIVATED\",\n    ROLE_CHANGED: \"ROLE_CHANGED\",\n    PERMISSIONS_CHANGED: \"PERMISSIONS_CHANGED\",\n    // System events\n    SYSTEM_ERROR: \"SYSTEM_ERROR\",\n    CONFIGURATION_CHANGED: \"CONFIGURATION_CHANGED\",\n    BACKUP_CREATED: \"BACKUP_CREATED\",\n    BACKUP_RESTORED: \"BACKUP_RESTORED\",\n    // Security events\n    IP_BLOCKED: \"IP_BLOCKED\",\n    RATE_LIMITED: \"RATE_LIMITED\",\n    SUSPICIOUS_ACTIVITY: \"SUSPICIOUS_ACTIVITY\",\n    SECURITY_SCAN: \"SECURITY_SCAN\",\n    VULNERABILITY_DETECTED: \"VULNERABILITY_DETECTED\",\n    // Business events\n    BOOKING_CREATED: \"BOOKING_CREATED\",\n    BOOKING_MODIFIED: \"BOOKING_MODIFIED\",\n    BOOKING_CANCELLED: \"BOOKING_CANCELLED\",\n    PAYMENT_PROCESSED: \"PAYMENT_PROCESSED\",\n    REFUND_ISSUED: \"REFUND_ISSUED\"\n};\n/**\r\n * Helper function to log user actions\r\n */ async function logUserAction(action, userId, userRole, details = {}) {\n    await auditLog({\n        action,\n        userId,\n        userRole,\n        severity: \"medium\",\n        ...details\n    });\n}\n/**\r\n * Helper function to log security events\r\n */ async function logSecurityEvent(action, details = {}) {\n    await auditLog({\n        action,\n        severity: \"high\",\n        ...details\n    });\n}\n/**\r\n * Helper function to log critical security events\r\n */ async function logCriticalEvent(action, details = {}) {\n    await auditLog({\n        action,\n        severity: \"critical\",\n        ...details\n    });\n}\n/**\r\n * Helper function to log data changes\r\n */ async function logDataChange(action, userId, resource, resourceId, oldValues, newValues) {\n    await auditLog({\n        action,\n        userId,\n        resource,\n        resourceId,\n        oldValues,\n        newValues,\n        severity: \"medium\"\n    });\n}\n/**\r\n * Get audit logs with filtering and pagination\r\n */ async function getAuditLogs(filters) {\n    try {\n        let query = supabase.from(\"audit_logs\").select(\"*\").order(\"created_at\", {\n            ascending: false\n        });\n        if (filters.userId) {\n            query = query.eq(\"user_id\", filters.userId);\n        }\n        if (filters.action) {\n            query = query.eq(\"action\", filters.action);\n        }\n        if (filters.severity) {\n            query = query.eq(\"severity\", filters.severity);\n        }\n        if (filters.startDate) {\n            query = query.gte(\"created_at\", filters.startDate);\n        }\n        if (filters.endDate) {\n            query = query.lte(\"created_at\", filters.endDate);\n        }\n        if (filters.limit) {\n            query = query.limit(filters.limit);\n        }\n        if (filters.offset) {\n            query = query.range(filters.offset, filters.offset + (filters.limit || 50) - 1);\n        }\n        const { data, error } = await query;\n        if (error) {\n            throw error;\n        }\n        return {\n            data,\n            error: null\n        };\n    } catch (error) {\n        console.error(\"Error fetching audit logs:\", error);\n        return {\n            data: null,\n            error\n        };\n    }\n}\n/**\r\n * Export audit logs for compliance\r\n */ async function exportAuditLogs(startDate, endDate, format = \"json\") {\n    try {\n        const { data, error } = await supabase.from(\"audit_logs\").select(\"*\").gte(\"created_at\", startDate).lte(\"created_at\", endDate).order(\"created_at\", {\n            ascending: true\n        });\n        if (error) {\n            throw error;\n        }\n        if (format === \"csv\") {\n            return convertToCSV(data);\n        }\n        return data;\n    } catch (error) {\n        console.error(\"Error exporting audit logs:\", error);\n        throw error;\n    }\n}\n/**\r\n * Convert audit logs to CSV format\r\n */ function convertToCSV(data) {\n    if (!data || data.length === 0) {\n        return \"\";\n    }\n    const headers = Object.keys(data[0]);\n    const csvContent = [\n        headers.join(\",\"),\n        ...data.map((row)=>headers.map((header)=>{\n                const value = row[header];\n                if (typeof value === \"object\" && value !== null) {\n                    return `\"${JSON.stringify(value).replace(/\"/g, '\"\"')}\"`;\n                }\n                return `\"${String(value || \"\").replace(/\"/g, '\"\"')}\"`;\n            }).join(\",\"))\n    ].join(\"\\n\");\n    return csvContent;\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(api)/./lib/security/audit-logging.ts\n");

/***/ }),

/***/ "(api)/./lib/supabase.js":
/*!*************************!*\
  !*** ./lib/supabase.js ***!
  \*************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__),\n/* harmony export */   supabase: () => (/* binding */ supabase),\n/* harmony export */   supabaseAdmin: () => (/* binding */ supabaseAdmin),\n/* harmony export */   testConnection: () => (/* binding */ testConnection)\n/* harmony export */ });\n/* harmony import */ var _supabase_supabase_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! @supabase/supabase-js */ \"@supabase/supabase-js\");\n/* harmony import */ var _supabase_supabase_js__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(_supabase_supabase_js__WEBPACK_IMPORTED_MODULE_0__);\n\nconst supabaseUrl = \"https://ndlgbcsbidyhxbpqzgqp.supabase.co\";\nconst supabaseAnonKey = \"eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6Im5kbGdiY3NiaWR5aHhicHF6Z3FwIiwicm9sZSI6ImFub24iLCJpYXQiOjE3NDcwNTk5OTcsImV4cCI6MjA2MjYzNTk5N30.W8qsqYWPzTGZHu3MxRLYq4147K0CGcGxznCbe9emCzI\";\nconst supabaseServiceRoleKey = process.env.SUPABASE_SERVICE_ROLE_KEY;\nif (!supabaseUrl || !supabaseAnonKey) {\n    throw new Error(\"Missing Supabase environment variables\");\n}\n// Client for browser-side operations (with RLS)\nconst supabase = (0,_supabase_supabase_js__WEBPACK_IMPORTED_MODULE_0__.createClient)(supabaseUrl, supabaseAnonKey, {\n    auth: {\n        autoRefreshToken: true,\n        persistSession: true,\n        detectSessionInUrl: true\n    },\n    realtime: {\n        params: {\n            eventsPerSecond: 10\n        }\n    }\n});\n// Admin client for server-side operations (bypasses RLS)\nconst supabaseAdmin = (0,_supabase_supabase_js__WEBPACK_IMPORTED_MODULE_0__.createClient)(supabaseUrl, supabaseServiceRoleKey || supabaseAnonKey, {\n    auth: {\n        autoRefreshToken: false,\n        persistSession: false\n    }\n});\n// Test connection function\nasync function testConnection() {\n    try {\n        const { data, error } = await supabaseAdmin.from(\"admin_users\").select(\"count(*)\").limit(1);\n        if (error) {\n            console.error(\"Supabase connection test failed:\", error);\n            return false;\n        }\n        console.log(\"✅ Supabase connection successful\");\n        return true;\n    } catch (err) {\n        console.error(\"❌ Supabase connection error:\", err);\n        return false;\n    }\n}\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (supabase);\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(api)/./lib/supabase.js\n");

/***/ }),

/***/ "(api)/./pages/api/admin/receipts.js":
/*!*************************************!*\
  !*** ./pages/api/admin/receipts.js ***!
  \*************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ handler)\n/* harmony export */ });\n/* harmony import */ var _lib_supabase__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! @/lib/supabase */ \"(api)/./lib/supabase.js\");\n/* harmony import */ var _lib_auth_admin_auth__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @/lib/auth/admin-auth */ \"(api)/./lib/auth/admin-auth.ts\");\n\n\n/**\n * API endpoint for managing receipt templates\n * Handles CRUD operations for receipt customization\n */ async function handler(req, res) {\n    try {\n        // Authenticate admin user\n        const token = req.headers.authorization?.replace(\"Bearer \", \"\") || req.cookies[\"admin-token\"];\n        if (!token) {\n            return res.status(401).json({\n                error: \"No authentication token\"\n            });\n        }\n        const authResult = await (0,_lib_auth_admin_auth__WEBPACK_IMPORTED_MODULE_1__.authenticateAdminRequest)(token);\n        if (!authResult.valid || !authResult.user) {\n            return res.status(401).json({\n                error: \"Invalid authentication\"\n            });\n        }\n        const user = authResult.user;\n        const requestId = Math.random().toString(36).substring(2, 8);\n        console.log(`[${requestId}] Receipt templates API called by ${user.email}`);\n        switch(req.method){\n            case \"GET\":\n                return await handleGetTemplates(req, res, user, requestId);\n            case \"POST\":\n                return await handleCreateTemplate(req, res, user, requestId);\n            case \"PUT\":\n                return await handleUpdateTemplate(req, res, user, requestId);\n            case \"DELETE\":\n                return await handleDeleteTemplate(req, res, user, requestId);\n            default:\n                return res.status(405).json({\n                    error: \"Method not allowed\"\n                });\n        }\n    } catch (error) {\n        console.error(\"Receipt templates API error:\", error);\n        return res.status(500).json({\n            error: \"Internal server error\"\n        });\n    }\n}\n/**\n * Get all receipt templates\n */ async function handleGetTemplates(req, res, user, requestId) {\n    try {\n        const { data: templates, error } = await _lib_supabase__WEBPACK_IMPORTED_MODULE_0__.supabaseAdmin.from(\"receipt_templates\").select(\"*\").eq(\"is_active\", true).order(\"is_default\", {\n            ascending: false\n        }).order(\"created_at\", {\n            ascending: false\n        });\n        if (error) {\n            console.error(`[${requestId}] Error fetching receipt templates:`, error);\n            // If table doesn't exist, return default templates\n            if (error.code === \"42P01\") {\n                console.log(`[${requestId}] Receipt templates table doesn't exist, returning default templates`);\n                const defaultTemplates = [\n                    {\n                        id: \"default-standard\",\n                        name: \"Standard Receipt\",\n                        description: \"Default receipt template with all standard information\",\n                        template_type: \"standard\",\n                        is_default: true,\n                        is_active: true,\n                        business_name: \"Ocean Soul Sparkles\",\n                        business_address: \"Australia\",\n                        business_phone: \"+61 XXX XXX XXX\",\n                        business_email: \"<EMAIL>\",\n                        business_website: \"oceansoulsparkles.com.au\",\n                        show_logo: true,\n                        logo_position: \"center\",\n                        header_color: \"#667eea\",\n                        text_color: \"#333333\",\n                        font_family: \"Arial\",\n                        font_size: 12,\n                        show_customer_details: true,\n                        show_service_details: true,\n                        show_artist_details: true,\n                        show_payment_details: true,\n                        show_booking_notes: false,\n                        show_terms_conditions: true,\n                        footer_message: \"Thank you for choosing Ocean Soul Sparkles!\",\n                        show_social_media: false\n                    },\n                    {\n                        id: \"default-compact\",\n                        name: \"Compact Receipt\",\n                        description: \"Minimal receipt template for quick transactions\",\n                        template_type: \"compact\",\n                        is_default: false,\n                        is_active: true,\n                        business_name: \"Ocean Soul Sparkles\",\n                        business_address: \"Australia\",\n                        business_phone: \"+61 XXX XXX XXX\",\n                        business_email: \"<EMAIL>\",\n                        business_website: \"oceansoulsparkles.com.au\",\n                        show_logo: false,\n                        logo_position: \"left\",\n                        header_color: \"#667eea\",\n                        text_color: \"#333333\",\n                        font_family: \"Arial\",\n                        font_size: 10,\n                        show_customer_details: true,\n                        show_service_details: true,\n                        show_artist_details: false,\n                        show_payment_details: true,\n                        show_booking_notes: false,\n                        show_terms_conditions: false,\n                        footer_message: \"Thank you!\",\n                        show_social_media: false\n                    }\n                ];\n                return res.status(200).json({\n                    templates: defaultTemplates\n                });\n            }\n            return res.status(500).json({\n                error: \"Failed to fetch receipt templates\"\n            });\n        }\n        console.log(`[${requestId}] Retrieved ${templates?.length || 0} receipt templates`);\n        return res.status(200).json({\n            templates: templates || []\n        });\n    } catch (error) {\n        console.error(`[${requestId}] Error in handleGetTemplates:`, error);\n        return res.status(500).json({\n            error: \"Failed to fetch receipt templates\"\n        });\n    }\n}\n/**\n * Create new receipt template\n */ async function handleCreateTemplate(req, res, user, requestId) {\n    try {\n        const { name, description, template_type, is_default, business_name, business_address, business_phone, business_email, business_website, business_abn, show_logo, logo_position, header_color, text_color, font_family, font_size, show_customer_details, show_service_details, show_artist_details, show_payment_details, show_booking_notes, show_terms_conditions, footer_message, show_social_media, social_media_links, custom_fields } = req.body;\n        if (!name || !template_type) {\n            return res.status(400).json({\n                error: \"Name and template type are required\"\n            });\n        }\n        // If setting as default, unset other defaults first\n        if (is_default) {\n            await _lib_supabase__WEBPACK_IMPORTED_MODULE_0__.supabaseAdmin.from(\"receipt_templates\").update({\n                is_default: false\n            }).eq(\"is_default\", true);\n        }\n        const { data: template, error } = await _lib_supabase__WEBPACK_IMPORTED_MODULE_0__.supabaseAdmin.from(\"receipt_templates\").insert([\n            {\n                name,\n                description,\n                template_type,\n                is_default: is_default || false,\n                is_active: true,\n                business_name,\n                business_address,\n                business_phone,\n                business_email,\n                business_website,\n                business_abn,\n                show_logo,\n                logo_position,\n                header_color,\n                text_color,\n                font_family,\n                font_size,\n                show_customer_details,\n                show_service_details,\n                show_artist_details,\n                show_payment_details,\n                show_booking_notes,\n                show_terms_conditions,\n                footer_message,\n                show_social_media,\n                social_media_links,\n                custom_fields,\n                created_by: user.id,\n                updated_by: user.id\n            }\n        ]).select().single();\n        if (error) {\n            console.error(`[${requestId}] Error creating receipt template:`, error);\n            return res.status(500).json({\n                error: \"Failed to create receipt template\"\n            });\n        }\n        console.log(`[${requestId}] Created receipt template: ${template.name}`);\n        return res.status(201).json({\n            template\n        });\n    } catch (error) {\n        console.error(`[${requestId}] Error in handleCreateTemplate:`, error);\n        return res.status(500).json({\n            error: \"Failed to create receipt template\"\n        });\n    }\n}\n/**\n * Update existing receipt template\n */ async function handleUpdateTemplate(req, res, user, requestId) {\n    try {\n        const { id } = req.query;\n        if (!id) {\n            return res.status(400).json({\n                error: \"Template ID is required\"\n            });\n        }\n        const updateData = {\n            ...req.body\n        };\n        delete updateData.id;\n        updateData.updated_by = user.id;\n        updateData.updated_at = new Date().toISOString();\n        // If setting as default, unset other defaults first\n        if (updateData.is_default) {\n            await _lib_supabase__WEBPACK_IMPORTED_MODULE_0__.supabaseAdmin.from(\"receipt_templates\").update({\n                is_default: false\n            }).eq(\"is_default\", true).neq(\"id\", id);\n        }\n        const { data: template, error } = await _lib_supabase__WEBPACK_IMPORTED_MODULE_0__.supabaseAdmin.from(\"receipt_templates\").update(updateData).eq(\"id\", id).select().single();\n        if (error) {\n            console.error(`[${requestId}] Error updating receipt template:`, error);\n            return res.status(500).json({\n                error: \"Failed to update receipt template\"\n            });\n        }\n        console.log(`[${requestId}] Updated receipt template: ${template.name}`);\n        return res.status(200).json({\n            template\n        });\n    } catch (error) {\n        console.error(`[${requestId}] Error in handleUpdateTemplate:`, error);\n        return res.status(500).json({\n            error: \"Failed to update receipt template\"\n        });\n    }\n}\n/**\n * Delete receipt template (soft delete)\n */ async function handleDeleteTemplate(req, res, user, requestId) {\n    try {\n        const { id } = req.query;\n        if (!id) {\n            return res.status(400).json({\n                error: \"Template ID is required\"\n            });\n        }\n        // Check if it's the default template\n        const { data: template } = await _lib_supabase__WEBPACK_IMPORTED_MODULE_0__.supabaseAdmin.from(\"receipt_templates\").select(\"is_default, name\").eq(\"id\", id).single();\n        if (template?.is_default) {\n            return res.status(400).json({\n                error: \"Cannot delete the default template\"\n            });\n        }\n        const { error } = await _lib_supabase__WEBPACK_IMPORTED_MODULE_0__.supabaseAdmin.from(\"receipt_templates\").update({\n            is_active: false,\n            updated_by: user.id,\n            updated_at: new Date().toISOString()\n        }).eq(\"id\", id);\n        if (error) {\n            console.error(`[${requestId}] Error deleting receipt template:`, error);\n            return res.status(500).json({\n                error: \"Failed to delete receipt template\"\n            });\n        }\n        console.log(`[${requestId}] Deleted receipt template: ${template?.name}`);\n        return res.status(200).json({\n            success: true\n        });\n    } catch (error) {\n        console.error(`[${requestId}] Error in handleDeleteTemplate:`, error);\n        return res.status(500).json({\n            error: \"Failed to delete receipt template\"\n        });\n    }\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,****************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************\n//# sourceURL=webpack-internal:///(api)/./pages/api/admin/receipts.js\n");

/***/ })

};
;

// load runtime
var __webpack_require__ = require("../../../webpack-api-runtime.js");
__webpack_require__.C(exports);
var __webpack_exec__ = (moduleId) => (__webpack_require__(__webpack_require__.s = moduleId))
var __webpack_exports__ = __webpack_require__.X(0, ["vendor-chunks/next"], () => (__webpack_exec__("(api)/./node_modules/next/dist/build/webpack/loaders/next-route-loader/index.js?kind=PAGES_API&page=%2Fapi%2Fadmin%2Freceipts&preferredRegion=&absolutePagePath=.%2Fpages%5Capi%5Cadmin%5Creceipts.js&middlewareConfigBase64=e30%3D!")));
module.exports = __webpack_exports__;

})();