{"c": ["pages/admin/bookings", "pages/admin/email-templates", "pages/admin/communications", "pages/admin/feedback", "webpack"], "r": ["/_error", "pages/admin/feedback"], "m": ["./node_modules/next/dist/build/webpack/loaders/next-client-pages-loader.js?absolutePagePath=C%3A%5CUsers%5Cconne%5CDownloads%5Cconnectplusapps%20website%5Cadmin.oceansoulsparkles.com.au%5Coceansoulsparkles-admin%5Cnode_modules%5Cnext%5Cdist%5Cpages%5C_error.js&page=%2F_error!", "./node_modules/next/dist/build/webpack/loaders/css-loader/src/index.js??ruleSet[1].rules[7].oneOf[9].use[1]!./node_modules/next/dist/build/webpack/loaders/postcss-loader/src/index.js??ruleSet[1].rules[7].oneOf[9].use[2]!./styles/admin/Feedback.module.css", "./node_modules/next/dist/build/webpack/loaders/next-client-pages-loader.js?absolutePagePath=C%3A%5CUsers%5Cconne%5CDownloads%5Cconnectplusapps%20website%5Cadmin.oceansoulsparkles.com.au%5Coceansoulsparkles-admin%5Cpages%5Cadmin%5Cfeedback.tsx&page=%2Fadmin%2Ffeedback!", "./pages/admin/feedback.tsx", "./styles/admin/Feedback.module.css"]}