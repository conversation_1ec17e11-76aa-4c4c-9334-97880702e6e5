{"c": ["webpack"], "r": ["pages/admin/dashboard", "pages/admin/login", "pages/admin/bookings/[id]"], "m": ["./components/admin/ActivityFeed.tsx", "./components/admin/DashboardStats.tsx", "./components/admin/QuickActions.tsx", "./components/admin/RecentBookings.tsx", "./node_modules/next/dist/build/webpack/loaders/css-loader/src/index.js??ruleSet[1].rules[7].oneOf[9].use[1]!./node_modules/next/dist/build/webpack/loaders/postcss-loader/src/index.js??ruleSet[1].rules[7].oneOf[9].use[2]!./styles/admin/ActivityFeed.module.css", "./node_modules/next/dist/build/webpack/loaders/css-loader/src/index.js??ruleSet[1].rules[7].oneOf[9].use[1]!./node_modules/next/dist/build/webpack/loaders/postcss-loader/src/index.js??ruleSet[1].rules[7].oneOf[9].use[2]!./styles/admin/Dashboard.module.css", "./node_modules/next/dist/build/webpack/loaders/css-loader/src/index.js??ruleSet[1].rules[7].oneOf[9].use[1]!./node_modules/next/dist/build/webpack/loaders/postcss-loader/src/index.js??ruleSet[1].rules[7].oneOf[9].use[2]!./styles/admin/DashboardStats.module.css", "./node_modules/next/dist/build/webpack/loaders/css-loader/src/index.js??ruleSet[1].rules[7].oneOf[9].use[1]!./node_modules/next/dist/build/webpack/loaders/postcss-loader/src/index.js??ruleSet[1].rules[7].oneOf[9].use[2]!./styles/admin/QuickActions.module.css", "./node_modules/next/dist/build/webpack/loaders/css-loader/src/index.js??ruleSet[1].rules[7].oneOf[9].use[1]!./node_modules/next/dist/build/webpack/loaders/postcss-loader/src/index.js??ruleSet[1].rules[7].oneOf[9].use[2]!./styles/admin/RecentBookings.module.css", "./node_modules/next/dist/build/webpack/loaders/next-client-pages-loader.js?absolutePagePath=C%3A%5CUsers%5Cconne%5CDownloads%5Cconnectplusapps%20website%5Cadmin.oceansoulsparkles.com.au%5Coceansoulsparkles-admin%5Cpages%5Cadmin%5Cdashboard.tsx&page=%2Fadmin%2Fdashboard!", "./pages/admin/dashboard.tsx", "./styles/admin/ActivityFeed.module.css", "./styles/admin/Dashboard.module.css", "./styles/admin/DashboardStats.module.css", "./styles/admin/QuickActions.module.css", "./styles/admin/RecentBookings.module.css", "./components/auth/LoginForm.tsx", "./components/auth/MFAForm.tsx", "./node_modules/next/dist/build/webpack/loaders/css-loader/src/index.js??ruleSet[1].rules[7].oneOf[9].use[1]!./node_modules/next/dist/build/webpack/loaders/postcss-loader/src/index.js??ruleSet[1].rules[7].oneOf[9].use[2]!./styles/admin/Login.module.css", "./node_modules/next/dist/build/webpack/loaders/css-loader/src/index.js??ruleSet[1].rules[7].oneOf[9].use[1]!./node_modules/next/dist/build/webpack/loaders/postcss-loader/src/index.js??ruleSet[1].rules[7].oneOf[9].use[2]!./styles/admin/LoginForm.module.css", "./node_modules/next/dist/build/webpack/loaders/css-loader/src/index.js??ruleSet[1].rules[7].oneOf[9].use[1]!./node_modules/next/dist/build/webpack/loaders/postcss-loader/src/index.js??ruleSet[1].rules[7].oneOf[9].use[2]!./styles/admin/MFAForm.module.css", "./node_modules/next/dist/build/webpack/loaders/next-client-pages-loader.js?absolutePagePath=C%3A%5CUsers%5Cconne%5CDownloads%5Cconnectplusapps%20website%5Cadmin.oceansoulsparkles.com.au%5Coceansoulsparkles-admin%5Cpages%5Cadmin%5Clogin.tsx&page=%2Fadmin%2Flogin!", "./node_modules/react-hook-form/dist/index.esm.mjs", "./pages/admin/login.tsx", "./styles/admin/Login.module.css", "./styles/admin/LoginForm.module.css", "./styles/admin/MFAForm.module.css", "./node_modules/next/dist/build/webpack/loaders/css-loader/src/index.js??ruleSet[1].rules[7].oneOf[9].use[1]!./node_modules/next/dist/build/webpack/loaders/postcss-loader/src/index.js??ruleSet[1].rules[7].oneOf[9].use[2]!./styles/admin/BookingDetails.module.css", "./node_modules/next/dist/build/webpack/loaders/next-client-pages-loader.js?absolutePagePath=C%3A%5CUsers%5Cconne%5CDownloads%5Cconnectplusapps%20website%5Cadmin.oceansoulsparkles.com.au%5Coceansoulsparkles-admin%5Cpages%5Cadmin%5Cbookings%5C%5Bid%5D.js&page=%2Fadmin%2Fbookings%2F%5Bid%5D!", "./pages/admin/bookings/[id].js", "./styles/admin/BookingDetails.module.css"]}